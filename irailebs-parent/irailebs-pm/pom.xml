<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.irailebs</groupId>
        <artifactId>irailebs-parent</artifactId>
        <version>1.0.0-7.1.0-SNAPSHOT</version>
    </parent>

    <artifactId>irailebs-pm</artifactId>
    <version>${irailebs-pm.version}</version>
    <packaging>pom</packaging>

    <modules>
        <module>irailebs-pm-cqyy</module>
        <!-- 未来的业务模块 -->
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <!-- 业务模块通用依赖 -->
    <dependencies>
        <!-- lombok插件-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
</project>
