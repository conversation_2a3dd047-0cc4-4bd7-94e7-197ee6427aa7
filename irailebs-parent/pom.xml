<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 继承企业框架 -->
    <parent>
        <groupId>com.baosight.iplat4j</groupId>
        <artifactId>iplat4j-boot-starter</artifactId>
        <version>7.1.0</version>
        <relativePath/>  <!-- 从仓库获取，不从本地文件系统查找 -->
    </parent>

    <groupId>com.baosight.irailebs</groupId>
    <artifactId>irailebs-parent</artifactId>
    <version>1.0.0-7.1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>irailebs-pm-cqyy</module>
        <module>irailebs-agent-hub</module>
    </modules>

    <!-- 从根项目继承版本属性 -->
    <properties>
        <irailebs-pm-cqyy.version>1.0.0-7.1.0-SNAPSHOT</irailebs-pm-cqyy.version>
        <irailebs-agent-hub.version>1.0.0-7.1.0-SNAPSHOT</irailebs-agent-hub.version>
        
        <!-- 编译配置 -->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <!-- 业务框架层依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.baosight.iplat4j</groupId>
                <artifactId>redis-plugin</artifactId>
                <version>*******</version>
            </dependency>

            <dependency>
                <groupId>com.baosight.irailebs</groupId>
                <artifactId>irailebs-pm-cqyy</artifactId>
                <version>${irailebs-pm-cqyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baosight.irailebs</groupId>
                <artifactId>irailebs-agent-hub</artifactId>
                <version>${irailebs-agent-hub.version}</version>
            </dependency>
            <!-- 达梦数据库驱动 -->
            <dependency>
                <groupId>com.dm</groupId>
                <artifactId>dm8-jdbc-driver-18</artifactId>
                <version>*********</version>
            </dependency>

            <!-- hutool工具包-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.26</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 插件版本管理 -->
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- 业务模块通用依赖 -->
    <dependencies>
        <!-- hutool工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- V7插件依赖 -->
        <!--redis适配插件-->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>redis-plugin</artifactId>
        </dependency>

        <!-- lombok插件-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </repository>
        <repository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </repository>
        <repository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </pluginRepository>
        <pluginRepository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </pluginRepository>
        <pluginRepository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
