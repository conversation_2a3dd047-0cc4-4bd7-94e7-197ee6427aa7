<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.irailebs</groupId>
        <artifactId>irailebs-parent</artifactId>
        <version>1.0.0-7.1.0-SNAPSHOT</version>
    </parent>

    <artifactId>irailebs-pm-cqyy</artifactId>
    <version>1.0.0-7.1.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <lowcode.version>7.2.0</lowcode.version>
    </properties>

    <!-- 业务模块特定依赖 -->
    <dependencies>
        <!-- 组织机构、岗位、分级授权等 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>org-all-plugin</artifactId>
            <version>7.1.0</version>
        </dependency>

<!--        &lt;!&ndash; 低代码设计器依赖 &ndash;&gt;-->
<!--        &lt;!&ndash;设计器相关公共方法&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.baosight.eplat</groupId>-->
<!--            <artifactId>lowcode-management</artifactId>-->
<!--            <version>${lowcode.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.xin3plat</groupId>-->
<!--                    <artifactId>delivery-dashboard</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-job</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-message</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-bpm</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.eplat</groupId>-->
<!--                    <artifactId>eplat-sdk-standard</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.eplat</groupId>-->
<!--                    <artifactId>eplat-sdk-authority</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.xin3plat</groupId>-->
<!--                    <artifactId>biz-user</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

<!--        &lt;!&ndash;表单设计器相关的前台和后台&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.baosight.eplat</groupId>-->
<!--            <artifactId>lowcode-form</artifactId>-->
<!--            <version>${lowcode.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.xin3plat</groupId>-->
<!--                    <artifactId>delivery-dashboard</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-job</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-message</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-bpm</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.eplat</groupId>-->
<!--                    <artifactId>eplat-sdk-standard</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.eplat</groupId>-->
<!--                    <artifactId>eplat-sdk-authority</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.xin3plat</groupId>-->
<!--                    <artifactId>biz-user</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.baosight.eplat</groupId>-->
<!--            <artifactId>code-display</artifactId>-->
<!--            <version>${lowcode.version}</version>-->
<!--        </dependency>-->

<!--        &lt;!&ndash;流程设计器相关的前台和后台&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.baosight.eplat</groupId>-->
<!--            <artifactId>lowcode-workflow</artifactId>-->
<!--            <version>${lowcode.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.xin3plat</groupId>-->
<!--                    <artifactId>delivery-dashboard</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-job</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.iplat4j</groupId>-->
<!--                    <artifactId>xservices-message</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.eplat</groupId>-->
<!--                    <artifactId>eplat-sdk-standard</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.eplat</groupId>-->
<!--                    <artifactId>eplat-sdk-authority</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baosight.xin3plat</groupId>-->
<!--                    <artifactId>biz-user</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.baosight.eplat</groupId>-->
<!--            <artifactId>code-display-workflow</artifactId>-->
<!--            <version>${lowcode.version}</version>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>false</addClasspath>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
