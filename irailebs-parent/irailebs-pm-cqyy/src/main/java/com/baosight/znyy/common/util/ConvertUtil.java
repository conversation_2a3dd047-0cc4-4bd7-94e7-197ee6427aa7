package com.baosight.znyy.common.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * convert util （转换实用程序）
 *
 * <AUTHOR>
 * @date 2024/09/20
 */
public class ConvertUtil {

    private static final String DEFAULT_RESULT = "result";

    private ConvertUtil() {

    }

    public static List<Map<String, Object>> parseAttr(EiInfo eiInfo) {
        return parseAttr(eiInfo, DEFAULT_RESULT);
    }

    public static List<Map<String, Object>> parseBlock(EiInfo eiInfo) {
        return parseBlock(eiInfo, DEFAULT_RESULT);
    }

    public static List<Map<String, Object>> parseAttr(EiInfo eiInfo, String key) {
        if (eiInfo.getAttr().get(key) == null) {
            return new ArrayList<>();
        }
        Object result = eiInfo.get(key);
        return parseArray(result);
    }

    public static Map<String, Object> parseAttr2Map(EiInfo eiInfo) {
        return parseAttr2Map(eiInfo, DEFAULT_RESULT);
    }

    public static Map<String, Object> parseAttr2Map(EiInfo eiInfo, String key) {
        if (eiInfo.getAttr().get(key) == null) {
            return new HashMap<>();
        }
        Object result = eiInfo.get(key);
        return parseObj(result);
    }

    public static List<Map<String, Object>> parseBlock(EiInfo eiInfo, String blockId) {
        if (eiInfo.getBlock(blockId) == null) {
            return new ArrayList<>();
        }
        Object result = eiInfo.getBlock(blockId).getRows();
        return parseArray(result);
    }

    public static List<Map<String, Object>> parseJSON(Object obj) {
        if (obj == null) {
            return new ArrayList<>();
        }
        return parseArray(JSONUtil.parse(obj));
    }

    public static List<Map<String, Object>> parseArray(Object result) {
        if (result == null) {
            return new ArrayList<>();
        }
        return Convert.convert(new TypeReference<List<Map<String, Object>>>() {
        }, result);
    }

    public static Map<String, Object> parseObj(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        return Convert.convert(new TypeReference<Map<String, Object>>() {
        }, obj);
    }
}
