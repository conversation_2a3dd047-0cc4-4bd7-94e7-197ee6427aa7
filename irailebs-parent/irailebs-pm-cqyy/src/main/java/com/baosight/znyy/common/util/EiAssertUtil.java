package com.baosight.znyy.common.util;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import lombok.extern.slf4j.Slf4j;

import java.util.function.BiConsumer;

@Slf4j
public class EiAssertUtil {

    private EiAssertUtil() {
    }


    /**
     * 断言字符串不为空
     *
     * @param value   要检查的字符串
     * @param message 错误消息
     */
    public static void assertNotEmpty(String value, String message) {
        if (value == null || value.isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 创建一个链式断言验证器
     *
     * @return 链式断言验证器
     */
    public static ChainAssert builder() {
        return new ChainAssert();
    }

    /**
     * 链式断言验证器，支持多条件连续校验
     */
    public static class ChainAssert {
        private final boolean valid = true;

        /**
         * 断言字符串不为空
         *
         * @param value   要检查的字符串
         * @param message 错误消息
         * @return 当前验证器实例，用于链式调用
         */
        public ChainAssert notEmpty(String value, String message) {
            if (valid && (value == null || value.isEmpty())) {
                throw new IllegalArgumentException(message);
            }
            return this;
        }

        /**
         * 断言对象不为null
         *
         * @param value   要检查的对象
         * @param message 错误消息
         * @return 当前验证器实例，用于链式调用
         */
        public ChainAssert notNull(Object value, String message) {
            if (valid && value == null) {
                throw new IllegalArgumentException(message);
            }
            return this;
        }

        /**
         * 断言条件为真
         *
         * @param condition 要检查的条件
         * @param message   错误消息
         * @return 当前验证器实例，用于链式调用
         */
        public ChainAssert isTrue(boolean condition, String message) {
            if (valid && !condition) {
                throw new IllegalArgumentException(message);
            }
            return this;
        }
    }

    /**
     * 断言检查，如果EiInfo状态小于0则抛出异常并记录日志
     *
     * @param eiInfo       需要检查的EiInfo对象
     * @param errorHandler 错误处理函数，接收错误消息和EiInfo对象
     * @throws PlatException 当EiInfo状态小于0时抛出
     */
    public static void assertThrow(EiInfo eiInfo, BiConsumer<String, EiInfo> errorHandler) {
        if (eiInfo.getStatus() < 0) {
            String errorMsg = eiInfo.getMsg();
            if (errorHandler != null) {
                errorHandler.accept(errorMsg, eiInfo);
            }
            throw new PlatException(errorMsg, eiInfo.getStatus());
        }
    }

    public static void assertThrow(EiInfo eiInfo, String message, BiConsumer<String, EiInfo> errorHandler) {
        if (eiInfo.getStatus() < 0) {
            if (errorHandler != null) {
                errorHandler.accept(message, eiInfo);
            }
            throw new PlatException(message, eiInfo.getStatus());
        }


    }
}
