package com.baosight.znyy.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 统一的JSON工具类
 * <p>
 * 基于Jackson提供完整的JSON序列化和反序列化功能。
 * 采用一致的异常处理策略，提供类型安全的泛型支持。
 * </p>
 *
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
public class JSONUtil {

    /**
     * JSON对象映射器
     */
    @Getter
    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 更新ObjectMapper配置
     *
     * @param newObjectMapper 新的ObjectMapper实例
     */
    public static void updateObjectMapper(ObjectMapper newObjectMapper) {
        if (newObjectMapper != null) {
            objectMapper = newObjectMapper;
            log.info("ObjectMapper configuration updated");
        }
    }

    private JSONUtil() {
        // 私有构造函数，防止实例化
    }

    // ==================== 基础转换方法 ====================

    /**
     * 将对象转换为JSON字符串
     *
     * @param obj 要转换的对象
     * @return JSON字符串
     * @throws JsonProcessingException 如果转换失败
     */
    public static String toJson(Object obj) throws JsonProcessingException {
        return objectMapper.writeValueAsString(obj);
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param <T>   目标类型
     * @param json  JSON字符串
     * @param clazz 目标类
     * @return 转换后的对象
     * @throws IOException 如果转换失败
     */
    public static <T> T fromJson(String json, Class<T> clazz) throws IOException {
        return objectMapper.readValue(json, clazz);
    }

    /**
     * 将JSON字符串转换为复杂类型对象
     *
     * @param <T>           目标类型
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @return 转换后的对象
     * @throws IOException 如果转换失败
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) throws IOException {
        return objectMapper.readValue(json, typeReference);
    }

    // ==================== Map相关方法 ====================

    /**
     * 将Map转换为JSON字符串
     *
     * @param map 输入的Map
     * @return JSON字符串
     * @throws JsonProcessingException 如果转换失败
     */
    public static String toJson(Map<?, ?> map) throws JsonProcessingException {
        return stringify(map);
    }

    /**
     * 将JSON字符串解析为Map&lt;String, Object&gt;
     *
     * @param json JSON字符串
     * @return 转换后的Map
     * @throws IOException 如果转换失败
     */
    public static Map<String, Object> parseMap(String json) throws IOException {
        return fromJson(json, new TypeReference<Map<String, Object>>() {});
    }

    /**
     * 将JSON字符串解析为指定类型的Map
     *
     * @param <K>       键类型
     * @param <V>       值类型
     * @param json      JSON字符串
     * @param keyType   Map的键类型
     * @param valueType Map的值类型
     * @return 转换后的Map
     * @throws IOException 如果转换失败
     */
    public static <K, V> Map<K, V> parseMap(String json, Class<K> keyType, Class<V> valueType) throws IOException {
        MapType mapType = TypeFactory.defaultInstance().constructMapType(Map.class, keyType, valueType);
        return objectMapper.readValue(json, mapType);
    }

    /**
     * 将JSON字符串解析为Map（使用TypeReference）
     *
     * @param <K>           键类型
     * @param <V>           值类型
     * @param json          JSON字符串
     * @param typeReference 类型引用
     * @return 转换后的Map
     * @throws IOException 如果转换失败
     */
    public static <K, V> Map<K, V> parseMap(String json, TypeReference<Map<K, V>> typeReference) throws IOException {
        return objectMapper.readValue(json, typeReference);
    }

    // ==================== List相关方法 ====================

    /**
     * 将List转换为JSON字符串
     *
     * @param list 输入的List
     * @return JSON字符串
     * @throws JsonProcessingException 如果转换失败
     */
    public static String toJson(List<?> list) throws JsonProcessingException {
        return stringify(list);
    }

    /**
     * 将JSON字符串解析为List&lt;Map&lt;String, Object&gt;&gt;
     *
     * @param json JSON字符串
     * @return 转换后的List
     * @throws IOException 如果转换失败
     */
    public static List<Map<String, Object>> parseList(String json) throws IOException {
        return fromJson(json, new TypeReference<List<Map<String, Object>>>() {});
    }

    /**
     * 将JSON字符串解析为指定元素类型的List
     *
     * @param <T>         列表元素类型
     * @param json        JSON字符串
     * @param elementType List中元素的类型
     * @return 转换后的List
     * @throws IOException 如果转换失败
     */
    public static <T> List<T> parseList(String json, Class<T> elementType) throws IOException {
        return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, elementType));
    }

    /**
     * 将Object转换为指定元素类型的List
     *
     * @param <T>         列表元素类型
     * @param obj         输入的Object
     * @param elementType List中元素的类型
     * @return 转换后的List
     */
    public static <T> List<T> convertToList(Object obj, Class<T> elementType) {
        return objectMapper.convertValue(obj, objectMapper.getTypeFactory().constructCollectionType(List.class, elementType));
    }

    // ==================== 实用方法 ====================

    /**
     * 将JSON字符串转换为JsonNode
     *
     * @param json JSON字符串
     * @return JsonNode对象
     * @throws IOException 如果转换失败
     */
    public static JsonNode toJsonNode(String json) throws IOException {
        return objectMapper.readTree(json);
    }

    /**
     * 安全地将对象转换为JSON字符串
     * <p>
     * 如果对象已经是字符串，直接返回；否则进行JSON序列化
     * </p>
     *
     * @param obj 要转换的对象
     * @return JSON字符串
     * @throws JsonProcessingException 如果转换失败
     */
    public static String stringify(Object obj) throws JsonProcessingException {
        if (obj instanceof String) {
            return (String) obj;
        } else {
            return objectMapper.writeValueAsString(obj);
        }
    }

    // ==================== 文件操作方法 ====================

    /**
     * 从文件读取JSON并转换为指定类型
     *
     * @param <T>       目标类型
     * @param file      JSON文件
     * @param valueType 目标类型的Class对象
     * @return 转换后的对象
     * @throws IOException 如果读取或转换失败
     */
    public static <T> T readFromFile(File file, Class<T> valueType) throws IOException {
        return objectMapper.readValue(file, valueType);
    }

    /**
     * 从文件读取JSON并转换为复杂类型
     *
     * @param <T>           目标类型
     * @param file          JSON文件
     * @param typeReference 类型引用
     * @return 转换后的对象
     * @throws IOException 如果读取或转换失败
     */
    public static <T> T readFromFile(File file, TypeReference<T> typeReference) throws IOException {
        return objectMapper.readValue(file, typeReference);
    }

    /**
     * 将对象写入JSON文件
     *
     * @param file   目标文件
     * @param object 要保存的对象
     * @throws IOException 如果保存失败
     */
    public static void writeToFile(File file, Object object) throws IOException {
        objectMapper.writeValue(file, object);
    }

    /**
     * 将对象写入格式化的JSON文件
     *
     * @param file   目标文件
     * @param object 要保存的对象
     * @throws IOException 如果保存失败
     */
    public static void writePrettyToFile(File file, Object object) throws IOException {
        objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, object);
    }
}
