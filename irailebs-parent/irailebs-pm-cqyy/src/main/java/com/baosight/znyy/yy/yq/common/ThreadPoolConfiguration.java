package com.baosight.znyy.yy.yq.common;//package com.baosight.znyy.yq.common;
//
//
//import cn.hutool.core.thread.ThreadFactoryBuilder;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.concurrent.LinkedBlockingQueue;
//import java.util.concurrent.ThreadPoolExecutor;
//import java.util.concurrent.TimeUnit;
//
///**
// * <AUTHOR>
// * @date 2025-06-05 16:29
// * 线程池
// **/
//
//@Configuration
//@Slf4j
//public class ThreadPoolConfiguration {
//
//    @Bean(name = "defaultThreadPoolExecutor", destroyMethod = "shutdown")
//    public ThreadPoolExecutor systemCheckPoolExecutorService() {
//
//        return new ThreadPoolExecutor(3, 10, 60, TimeUnit.SECONDS,
//                new LinkedBlockingQueue<Runnable>(10000),
//                new ThreadFactoryBuilder().setNamePrefix("default-executor-%d").build(),
//                (r, executor) -> log.error("system pool is full! "));
//    }
//}