package com.baosight.znyy.yy.yq.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import lombok.val;
import lombok.var;
import me.zhyd.oauth.log.Log;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

;

/**
 * <AUTHOR>
 * @date 2025-06-04 18:47
 **/
public class ServiceYYYQ01 extends ServiceBase {
    private static final Logger log = LoggerFactory.getLogger(ServiceYYYQ01.class);
//    public Set<Integer> yqIds = new HashSet<>();//已经入库的ids
    private static final Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");
//    private YqHttpService yq;



    private int total; // 用于存储上一次请求的总记录数
    private int pageSize; // 每页的记录数
    private int page;
    private int preTotal;
    public List<Map> conditions=new ArrayList<>();
    public List<Map> plans=new ArrayList<>();
    public List<Map>  medias=new ArrayList<>();
    public Set<Integer> ids=new HashSet<>();
    public List<Integer> deleteIds=new ArrayList<>();
    public  boolean requestFlag;
    // 构造函数
    public ServiceYYYQ01() {
        this.pageSize =0;
        this.total = 0;
    }
    // 封装后的请求处理方法
    public EiInfo yqRequest( EiInfo info){
        conditions.clear();
        plans.clear();
        medias.clear();
        ids.clear();
        deleteIds.clear();
        JSONObject params = new JSONObject();
        if(info.get("provider")==null||info.get("page")==null||info.get("page_size")==null){
            Log.debug("请加入必填参数:provider,page,page_size");
            return info;
        }
        params.put("provider", info.get("provider"));
        params.put("page", info.get("page"));
        params.put("page_size", info.get("page_size"));
        if(info.get("keyword")!=null)
            params.put("keyword", info.get("keyword"));
        if(info.get("plan_id")!=null)
            params.put("plan_id", info.get("plan_id"));
        if(info.get("ai_category_id")!=null)
            params.put("ai_category_id", info.get("ai_category_id"));
        if(info.get("judged")!=null)
            params.put("judged", info.get("judged"));
        if(info.get("start_time")!=null)
            params.put("start_time", info.get("start_time"));
        if(info.get("end_time")!=null)
            params.put("end_time", info.get("end_time"));
        this.page=(int) info.get("page");
        this.pageSize= (int) info.get("page_size");
        this.requestFlag = true;
        this.ids=getYQID(info);
        handleRequest(params,info);
        uSql(info);
        return info;
    }
    /**
     * 主要用于请求过程中的刷新现象
     * @param parmObj
     */
    public void handleRequest(JSONObject parmObj,EiInfo info) {

        if(this.requestFlag){
            queryGetData(parmObj,info);
            log.debug("进入到第"+this.page+"页");
            this.requestFlag=false;
        }
        int totalPage = this.total/this.pageSize+1;
        if (this.page+1>=totalPage) return;
        for(int p=2;p<=totalPage;p++){
            parmObj.put("page", p);
            queryGetData(parmObj,info);
            log.debug("进入到第"+p+"页");
        }

    }
    /**
     * 请求接口
     * @param parmObj
     * @return
     */
    public void queryGetData(JSONObject parmObj,EiInfo info){

        //请求信息
//        int pageSize=parmObj.getInteger("page_size");
//            String Authorization=PlatApplicationContext.getProperty("yq.Authorization");
//            String baseUrl =PlatApplicationContext.getProperty("yq.httpUrl");
        String Authorization="Bearer sk-093d8060-36b5-11f0-a87b-0242ac190002";
        String baseUrl="https://chat.di.360.cn/api/open/alert/list_alert_media";
        StringBuilder yqurl = new StringBuilder(baseUrl + "?");
        for (String key : parmObj.keySet()) {
            Object value = parmObj.get(key);

            yqurl.append(key).append("=").append(value).append("&");
        }
        // 创建HttpClient
        Log.debug("请求参数为 url = " + yqurl);
//        https://chat.di.360.cn/api/open/alert/list_alert_media?provider=qihoo&page=1&page_size=10&keyword=重庆&plan_id=&ai_category_id=&judged=true&start_time=&end_time=
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建 HttpGet 实例
            val request = new HttpGet(String.valueOf(yqurl));
            request.setHeader("Authorization", Authorization);
            request.setHeader("Content-Type", "application/json");

            // 发送 GET 请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode(); // 获取响应状态码
                System.out.println("Response Status Code: " + statusCode);
                // 获取响应体内容
                String responseBody = EntityUtils.toString(response.getEntity());
                System.out.println("Response Body: " + responseBody);
                JSONObject data=JSONObject.parseObject(responseBody);
                if(data.getInteger("code")==1000&& data.getJSONObject("data").getInteger("total")>0){
                    //插入数据库处理
                    JSONArray media = data.getJSONObject("data").getJSONArray("medias");
                    this.total = data.getJSONObject("data").getInteger("total");
                    dealData(media,info);
                }else{
                    //打印日志
                    info.setStatus(EiConstant.STATUS_FAILURE);
                    Log.debug("舆情获取失败,code="+data.getString("code")+"数据总数为"+data.getJSONObject("data").getInteger("total"));
                }
            }
        } catch (Exception e) {
            log.error("http请求异常，数据获取失败，程序出现故障");
            info.setStatus(EiConstant.STATUS_FAILURE);
            throw new RuntimeException(e);
        }

    }
    /**
     * 获取已经存入的数据的id
     * @return
     */
    public Set<Integer> getYQID(EiInfo info){
        Set<Integer> yqIds = new HashSet<>();
        try{
            List<Map<String,Object>> yqIdsList = dao.query("YYYQ01.queryID","");
            for (int i=0;i < yqIdsList.size(); i++) {
                yqIds.add((Integer) yqIdsList.get(i).get("FD_ID"));
            }
        }  catch (Exception e) {
            log.debug("数据ID获取失败getYQID");
            info.setStatus(EiConstant.STATUS_FAILURE);
            e.printStackTrace();

        }
        return yqIds;
    }

    /**
     * 将数据存入数据库，注意字符串截断
     * @param data
     */
    //        @Async("defaultThreadPoolExecutor")
    public  void dealData(JSONArray data,EiInfo info){
        // 遍历medias数组
        for (var item:data) {
            JSONObject media = (JSONObject) item;
            int yqId = media.getInteger("id");
            Map  sqlData=null;
            try{
                //查询是否已经存入数据库
                if(ids.contains(yqId)){
                    deleteIds.add(yqId);
                };
                JSONObject condition=media.getJSONObject("plan").getJSONObject("condition");
                condition.put("main_id",yqId);
                sqlData=this.convertJsonToMap(condition);
                conditions.add(sqlData);
                int conditionId=condition.getInteger("id");
                JSONObject plan=media.getJSONObject("plan");
                plan.remove("condition");
                plan.put("condition_id",conditionId);
                plan.put("main_id",yqId);
                sqlData=this.convertJsonToMap(plan);
//                    dao.insert("YYYQ01.insertPlan", sqlData);
                plans.add(sqlData);
                int planId=plan.getInteger("id");
                media.remove("plan");
                media.put("plan_id",planId);
                sqlData=this.convertJsonToMap(media);
                medias.add(sqlData);
                log.debug("已插入一条数据："+yqId);

            }catch (Exception e){
                info.setStatus(EiConstant.STATUS_FAILURE);
                log.debug("数据"+yqId+"插入失败"+sqlData,e);
            }

        }

    }

    /**
     * JSon 转MAp
     * @param jsonObject
     * @return
     */
    public  Map<String, Object> convertJsonToMap(JSONObject jsonObject) {
        Map<String, Object> map = new HashMap<>();
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            // 判断值的类型
            if (value instanceof Integer || value instanceof Long || value instanceof Double) {
                // 数字类型直接存储
                map.put(key, value);
            } else if (value instanceof JSONObject) {
                // 嵌套的 JSON 对象递归处理
                map.put(key, JSON.toJSONString(value));
            } else if (value instanceof JSONArray) {
                // JSON 数组转换为逗号分隔的字符串
                map.put(key, this.convertJsonArrayToString((JSONArray) value));
            } else if (value == null){
                map.put(key, "");
            } else {
                // 其他类型（如布尔值、字符串）转换为字符串
                map.put(key, value.toString());
            }
        }

        return map;
    }

    public  String convertJsonArrayToString(JSONArray jsonArray) {
        // 将 JSONArray 转换为逗号分隔的字符串
        return jsonArray.stream()
                .map(Object::toString) // 确保每个元素都转换为字符串
                .collect(Collectors.joining(",")); // 使用逗号分隔
    }
    public void uSql(EiInfo info){
        try{
            if(!deleteIds.isEmpty()){
                dao.deleteBatch("YYYQ01.deleteCID",deleteIds);
                dao.deleteBatch("YYYQ01.deletePID",deleteIds);
                dao.deleteBatch("YYYQ01.deleteMID",deleteIds);
                info.set("deleteCout",deleteIds.size());
                Log.debug("删除了主表"+deleteIds.size()+"数据");
            }

            dao.insertBatch("YYYQ01.insertCondition",conditions);
            Log.debug("T_YQ_CONDITION表新增了"+conditions.size()+"数据");
            dao.insertBatch("YYYQ01.insertPlan",plans);
            Log.debug("T_YQ_PLAN表新增了"+plans.size()+"数据");
            dao.insertBatch("YYYQ01.insertMonitoring",medias);
            Log.debug("T_YQ_MONITORING_DATA表新增了"+medias.size()+"数据");
            info.set("insertCout",medias.size());
            info.setStatus(1);
        }catch (Exception e){
            log.debug("数据插入失败"+e);
            info.setStatus(EiConstant.STATUS_FAILURE);
            e.printStackTrace();
        }
//    return info;
    }

}


