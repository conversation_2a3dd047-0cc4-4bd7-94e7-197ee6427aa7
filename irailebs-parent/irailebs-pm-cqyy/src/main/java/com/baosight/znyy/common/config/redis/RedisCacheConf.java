package com.baosight.znyy.common.config.redis;


import com.baosight.iplat4j.core.cache.CacheRegistry;
import com.baosight.iplat4j.core.cache.impl.RedisTempleteCache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

@Configuration
public class RedisCacheConf {

    @Value("${iplat.core.cache.redisExpireTime}")
    private int redisExpireTime;

    public final static String planKey = "irailebs:znyy:dd:plan";
    public final static String eventKey = "irailebs:znyy:dd:event";


    @Bean
    public RedisTempleteCache<?> planCache(RedisTemplate<String, Object> redisTemplate) {
        RedisTempleteCache<?> cache = new RedisTempleteCache<>();
        cache.setCacheEname(planKey);
        cache.setExpireTime(redisExpireTime);
        cache.setRedisTemplate(redisTemplate);
        return cache;
    }


    @Bean
    public CacheRegistry planCacheRegistry(RedisTempleteCache<?> planCache) {
        CacheRegistry registry = new CacheRegistry();
        registry.setCacheKey(planKey);
        registry.setCache(planCache);
        return registry;
    }

    @Bean
    public RedisTempleteCache<?> eventCache(RedisTemplate<String, Object> redisTemplate) {
        RedisTempleteCache<?> cache = new RedisTempleteCache<>();
        cache.setCacheEname(eventKey);
        cache.setExpireTime(redisExpireTime);
        cache.setRedisTemplate(redisTemplate);
        return cache;
    }

    @Bean
    public CacheRegistry eventCacheRegistry(RedisTempleteCache<?> eventCache) {
        CacheRegistry registry = new CacheRegistry();
        registry.setCacheKey(eventKey);
        registry.setCache(eventCache);
        return registry;
    }


}
