package com.baosight.znyy.common.impl;

import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.znyy.common.IPlatCodeInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.function.Supplier;

/**
 * @description：获取平台小代码相关信息
 * @author：yanghuanbo
 * @date：2025/06/04
 */

@Service
public class PlatCodeInfoImpl implements IPlatCodeInfo {

    private static final Logger logger = LoggerFactory.getLogger(PlatCodeInfoImpl.class);
    private Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");

    /**
     * 构建查询参数
     * @param typeCode 小代码类别
     * @param code 小代码代码
     * @param status 状态
     * @return
     */
    private Map<String, String> buildParams(String typeCode, String code, String status) {
        Map<String, String> params = new HashMap<>();
        if (StringUtils.isNotBlank(typeCode)) params.put("codesetCode", typeCode);
        if (StringUtils.isNotBlank(code)) params.put("itemCname", code);
        if (StringUtils.isNotBlank(status)) params.put("status", status);
        return params;
    }

    /**
     * 公共异常处理封装
     * @param supplier 需要处理的方法
     * @param errorMsg 错误信息
     * @return T
     * @param <T>
     */
    private <T> T handleGet(Supplier<T> supplier, String errorMsg) {
        try {
            return supplier.get();
        } catch (Exception e) {
            logger.error(errorMsg, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 公共查询封装（带 Optional 空值处理）
     * @param typeCode 小代码类别
     * @param code 小代码代码
     * @param type 小代码类别
     * @return
     */
    private Optional<Map> querySingleResult(String typeCode, String code, String type) {
        List<Map> result = this.getCode(typeCode, code, type);
        return Optional.ofNullable(result)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0));
    }


    /**
     * 获取代码项英文名
     * @param typeCode 代码分类编号
     * @param code 代码项编号
     * @param type 状态
     * @return String
     * @throws Exception
     */
    @Override
    public String getCodeEName(String typeCode, String code, String type) throws Exception {
        return handleGet(() -> {
                    try {
                        return querySingleResult(typeCode, code, type)
                                .map(map -> (String) map.get("itemEname"))
                                .orElseThrow(() -> new Exception("未找到对应的英文名"));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                },
                "获取英文名失败，typeCode=" + typeCode + ", code=" + code + ", type=" + type);
    }


    /**
     * 获取代码项中文名
     * @param typeCode 代码分类编号
     * @param code 代码项编号
     * @param type 状态
     * @return String
     */
    @Override
    public String getCodeCName(String typeCode, String code, String type) {
        return handleGet(() -> {
                    try {
                        return querySingleResult(typeCode, code, type)
                                .map(map -> (String) map.get("itemCname"))
                                .orElseThrow(() -> new Exception("未找到对应的中文名"));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                },
                "获取中文名失败，typeCode=" + typeCode + ", code=" + code + ", type=" + type);
    }

    @Override
    public List<Map> getCode(String typeCode, String code, String type) {
        Map<String, String> params = buildParams(typeCode, code, type);
        List<Map> list = dao.query("tedcm01.query", params);
        return list.size() > 0 ? list : Collections.emptyList();
    }

    @Override
    public List<Map> getCode(String typeCode, String code) {
        return getCode(typeCode, code, null);
    }

    @Override
    public List<Map> getCode(String typeCode) {
        return getCode(typeCode, null, "1");
    }

    @Override
    public List<Map<String, String>> getCodeAndName(String typeCode) {
        return getCodeAndName(typeCode, null, null);
    }

    public List<Map<String, String>> getCodeAndName(String typeCode, String code, String type) {
        Map<String, String> params = buildParams(typeCode, code, type);
        @SuppressWarnings("unchecked")
        List<Map<String, String>> list = dao.query("tedcm01.queryCodeAndName", params);
        return list;
    }

    @Override
    public List<Map<String, String>> getCodeAndName_page(String typeCode, int offset, int limit) {
        Map<String, String> params = buildParams(typeCode, null, null);
        return dao.query("tedcm01.queryCodeAndName", params, offset, limit);
    }

    @Override
    public int getCodeAndName_count(String typeCode) {
        Map<String, String> params = buildParams(typeCode, null, null);
        return dao.count("tedcm01.queryCodeAndName", params);
    }


    /**
     * 根据中文名称获取代码
     * @param codesetCode  小代码类别
     * @param codeCName 小代码中文名
     * @return List
     */
    @Override
    public List getCodeByCodeCName(String codesetCode,String codeCName) {
        Map<String, Object> params = new HashMap<>();
        params.put("itemCname", codeCName);
        params.put("codesetCode", codesetCode);
        return dao.query("tedcm01.query", params);
    }


    /**
     * 根据英文名称获取代码
     * @param codeEName 小代码英文名
     * @param codesetCode  小代码类别
     * @return List
     */
    @Override
    public List getCodeByCodeEName( String codesetCode,String codeEName) {
        Map<String, Object> params = new HashMap<>();
        params.put("itemEname", codeEName);
        params.put("codesetCode", codesetCode);
        return dao.query("tedcm01.query", params);
    }


    /**
     * 根据中文名称获取代码项英文名
     * @param codesetCode  小代码类别
     * @param codeCName 小代码中文名
     * @return String
     */
    @Override
    public String getItemEnameByCname(String codesetCode,String codeCName) {
        Map<String, Object> params = new HashMap<>();
        params.put("itemCname", codeCName);
        params.put("codesetCode", codesetCode);
        List<Map> list = dao.query("tedcm01.query", params);
        if (list.isEmpty()) {
            return "";
        }
        return list.stream().map(map -> (String) map.get("itemEname")).findFirst().orElse("");
    }


}
