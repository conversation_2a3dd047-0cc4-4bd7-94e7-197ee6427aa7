package com.baosight.znyy.yy.yq.common;

import java.io.InputStream;
import java.util.Properties;
/**
 * <AUTHOR>
 * @date 2025-06-05 15:03
 * 获取配置参数
 **/

public class GlobalProperties {
    private static final Properties props = new Properties();

    static {
        try (InputStream in = GlobalProperties.class.getClassLoader().getResourceAsStream("application.properties")) {
            if (in != null) {
                props.load(in);
            } else {
                throw new RuntimeException("application.properties not found in classpath");
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to load application.properties", e);
        }
    }

    public static String get(String key) {
        return props.getProperty(key);
    }
}