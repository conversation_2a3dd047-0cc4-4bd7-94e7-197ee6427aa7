package com.baosight.znyy.dd.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XEventManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.znyy.common.IPlatCodeInfo;
import com.baosight.znyy.common.util.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * COCC-预案推荐服务
 *
 * <AUTHOR>
 * @date 2025/06/16
 */
@Slf4j
public class ServiceDD01 extends ServiceBase {

    @Resource
    IPlatCodeInfo iplatCodeInfo;

    private final Map<String, Object> planCacheManager = CacheManager.getCache("irailebs:znyy:dd:plan");

    /**
     * 根据事件描述推荐预案
     * serviceId：S_CQYY_DD_01
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo }
     */
    public EiInfo planRecByEvent(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        // 参数获取
        Map<String, Object> paramMap = BusUtil.parse2Map(inInfo);
        String eventUUIDs = paramMap.getOrDefault("eventUUIDs", "").toString();
        String eventDesc = paramMap.getOrDefault("eventDesc", "").toString();
        //智能体配置表记录ID
        String agentRecodId = paramMap.getOrDefault("agentRecodId", "").toString();
        // 参数校验
        EiAssertUtil.builder()
                .notEmpty(eventUUIDs, "事件ID不能为空")
                .notEmpty(agentRecodId, "智能体配置记录uuid不能为空")
                .notEmpty(eventDesc, "事件描述不能为空");

        //1.1查找智能体配置记录
        List<?> agentConfigList = dao.query("YYAG01.query", MapUtil.of("recordId", agentRecodId));

        if (agentConfigList.isEmpty()) {
            log.error("未找到智能体配置记录:{}", agentRecodId);
            return outInfo;
        }
        // 1.2插入事件列表
        try {
            inInfo.addRow("event", paramMap);
            super.insert(inInfo, "DD01.insertEvent", "event");
        } catch (Exception e) {
            log.error("插入事件列表失败:{}", e.getMessage(), e);
        }
        //1.3请求智能体
        EiInfo asyncInfo = new EiInfo();
        asyncInfo.set("agentConfig", agentConfigList.get(0));
        asyncInfo.set("eventUUIDs", eventUUIDs);
        asyncInfo.set("eventDesc", eventDesc);
        asyncInfo.set(EiConstant.eventId, "E_CQYY_DD_0101");
        outInfo = XEventManager.callAsync(asyncInfo);
        EiAssertUtil.assertThrow(outInfo, (msg, eiInfo) -> log.error("推荐预案失败:{}", msg));
        outInfo.setMsg("事件信息接收成功，正在推荐预案...");
        return outInfo;
    }


    /**
     * 请求智能体推荐预案
     * serviceId：S_CQYY_DD_0101
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo }
     */
    public EiInfo requestRecommendation(EiInfo inInfo) {
        String eventUUIDs = inInfo.getString("eventUUIDs");
        String eventDesc = inInfo.getString("eventDesc");
        Map<String, Object> agentConfig = ConvertUtil.parseAttr2Map(inInfo, "agentConfig");
        // 请求智能体
        try {
            String responseBody = callAiAgent(eventDesc, agentConfig);
            processAiResponse(responseBody, eventUUIDs);
        } catch (Exception e) {
            log.error("请求智能体失败:{}", e.getMessage(), e);
        }
        return inInfo;
    }

    /**
     * 调用AI智能体API
     *
     * @param eventDesc 事件描述
     * @return 智能体响应内容
     */
    private String callAiAgent(String eventDesc, Map<String, Object> apiConfig) throws JsonProcessingException {
        // 构建请求头
        Map<String, String> headers = buildRequestHeaders((String) apiConfig.get("agentToken"));

        // 构建请求体
        Map<String, Object> body = buildRequestBody(eventDesc, apiConfig);

        String paramsStr = JSONUtil.toJson(body);

        // 发送请求
        try (HttpResponse response = HttpUtil.createPost((String) apiConfig.get("agentCallUrl"))
                .addHeaders(headers)
                .body(paramsStr)
                .execute()) {
            return response.charset("UTF-8").body();
        }
    }

    /**
     * 构建请求头
     */
    private Map<String, String> buildRequestHeaders(String token) {
        return MapUtil.builder("Authorization", token)
                .put("Content-Type", "application/json")
                .build();
    }

    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequestBody(String eventDesc, Map<String, Object> requestBodyMap) {
        // 创建基础Map
        Map<String, Object> baseMap = MapUtil.<String, Object>builder()
                .put("agent_id", Convert.toInt(requestBodyMap.get("agentId")))
                .put("user", requestBodyMap.get("agentPlatformUser"))
                .put("query", eventDesc)
                .put("stream", Convert.toInt(requestBodyMap.get("stream")) == 1)
                .put("verbose", Convert.toInt(requestBodyMap.get("verbose")) == 1)
                .put("version", requestBodyMap.get("agentVersion"))
                .build();
        //若 agentPlatformSess 不为空时 ，则添加session_id
        Optional.ofNullable(requestBodyMap.get("agentPlatformSess"))
                .map(StrUtil::toString)
                .filter(StrUtil::isNotBlank)
                .ifPresent(session -> baseMap.put("session_id", session));
        return baseMap;
    }

    /**
     * 处理AI响应并更新数据
     */
    private void processAiResponse(String responseBody, String eventUUIDs) {
        log.info("智能体响应: {}", responseBody);
        AgentResponseUtil agentResponseUtil = AgentResponseUtil.parser(responseBody);

        if (!agentResponseUtil.isSuccess()) {
            log.error("智能体响应失败:{}", agentResponseUtil.getErrorMessage());
            return;
        }
        // 解析智能体数据
        String lineName = agentResponseUtil.getLineName();
        List<String> planNameList = agentResponseUtil.getPlanNameList();
        if (lineName == null || planNameList.isEmpty()) {
            log.error("智能体响应数据解析失败:{}", responseBody);
            return;
        }
        log.info("线路名称:{}", lineName);
        log.info("预案名称列表:{}", planNameList);
        // 根据线路名称获取预案类型--根据小代码中文名称获取小代码英文名
        String planType = iplatCodeInfo.getItemEnameByCname("znyy.baseline", lineName);
        // 根据预案类型查询预案
        List<?> planList = dao.query("DD01.queryPlanInfo", MapUtil.builder(new HashMap<String, Object>())
                .put("planType", planType)
                .build());
        // 过滤预案
        List<Map<String, Object>> filteredPlanList = ConvertUtil.parseArray(planList)
                .stream()
                .filter(m -> planNameList.contains(m.get("planName").toString()))
                .collect(Collectors.toList());
        log.info("过滤后的预案列表:{}", filteredPlanList);
        // 存在 ->插入数据 （事件id,预案id） 更新缓存
        if (filteredPlanList.isEmpty()) {
            log.warn("智能体推荐预案不存在!");
            return;
        }
        try {
            String planUUIDs = filteredPlanList.stream().map(m -> m.get("planUUIDs").toString()).collect(Collectors.joining(","));
            Map<String, Object> insertMap = MapUtil.builder(new HashMap<String, Object>())
                    .put("eventUUIDs", eventUUIDs)
                    .put("planUUIDs", planUUIDs)
                    .put("createTime", DateUtils.curDateTimeStr14())
                    .build();
            EiInfo inInfo = new EiInfo();
            inInfo.addRow("insertMap", insertMap);
            super.insert(inInfo, "DD01.insertRecommendPlan", "insertMap");
            log.info("插入推荐预案成功!");
            // 缓存推荐预案
            planCacheManager.put(eventUUIDs, new ObjectMapper().writeValueAsString(filteredPlanList));
        } catch (Exception e) {
            log.error("插入推荐预案失败:{}", e.getMessage(), e);
        }
    }


    /**
     * 根据事件ID获取推荐预案
     * serviceId：S_CQYY_DD_02
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo }
     */
    public EiInfo getRecsByEventId(EiInfo inInfo) {
        TimeInterval timer = DateUtil.timer();
        EiInfo outInfo = new EiInfo();
        String eventUUIDs = inInfo.getString("eventUUIDs");
        if (eventUUIDs == null || eventUUIDs.isEmpty()) {
            inInfo.setMsg("事件ID不能为空");
            inInfo.set("status", EiConstant.STATUS_FAILURE);
            return inInfo;
        }

        // 检查缓存
        if (planCacheManager.containsKey(eventUUIDs)) {
            try {
                String planRecStr = planCacheManager.getOrDefault(eventUUIDs, "").toString();
                List<Map<String, Object>> planRecList = JSONUtil.parseList(planRecStr);
                outInfo.set("data", planRecList);
                outInfo.set("status", EiConstant.STATUS_SUCCESS);
                outInfo.setMsg("查询推荐预案成功!");
                log.info("查询推荐预案成功，本次从缓存中获取!耗时:{} ms", timer.intervalRestart());
                return outInfo;
            } catch (IOException e) {
                log.error("缓存推荐预案解析失败:{}", e.getMessage(), e);
                planCacheManager.remove(eventUUIDs);
                log.info("缓存推荐预案解析失败，已删除缓存，尝试从数据库中获取!");
            }
        }
        // 从数据库中查询
        List<?> res = dao.query("DD01.getRecsByEventId", inInfo.getAttr(), 0, -999999);
        if (res.isEmpty()) {
            outInfo.setMsg("未查询到推荐预案!");
            outInfo.set("status", EiConstant.STATUS_DEFAULT);
            log.warn("未查询到推荐预案!");
            return outInfo;
        }
        log.info("查询推荐预案成功，本次从数据库中获取!耗时:{} ms", timer.intervalRestart());
        // 转换数据
        List<Map<String, Object>> planRecList = ConvertUtil.parseArray(res)
                .stream()
                .peek(m -> m.remove("eventUUIDs"))
                .collect(Collectors.toList());
        outInfo.set("data", planRecList);
        outInfo.set("status", EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("查询推荐预案成功!");
        try {
            planCacheManager.put(eventUUIDs, new ObjectMapper().writeValueAsString(planRecList));
        } catch (IOException e) {
            log.error("缓存推荐预案失败:{}", e.getMessage(), e);
        }
        return outInfo;
    }


    /**
     * 记录实际选择的预案
     * serviceId：S_CQYY_DD_03
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo }
     */
    public EiInfo recordPlanSelection(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        Map<String, Object> map = BusUtil.parse2Map(inInfo);
        map.put("createTime", DateUtils.curDateTimeStr14());
        inInfo.addRow("planRec", map);
        try {
            //COCC侧，可能会编辑多次事件信息，每次事件保存时，都是调用本接口，所以设计：先删除数据后新增数据
            super.delete(inInfo, "DD01.deleteByEventId", "planRec");
            super.insert(inInfo, "DD01.recordPlanSelection", "planRec");
            outInfo.setMsg("记录成功");
            outInfo.setStatus(1);
            return outInfo;
        } catch (Exception e) {
            outInfo.setMsg(inInfo.getMsg());
            outInfo.set("status", EiConstant.STATUS_FAILURE);
            log.error("保存实际预案失败：{}", e.getMessage());
            return outInfo;
        }
    }
}
