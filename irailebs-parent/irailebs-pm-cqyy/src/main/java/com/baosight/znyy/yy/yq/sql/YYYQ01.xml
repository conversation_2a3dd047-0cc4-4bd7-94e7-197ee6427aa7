<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="YYYQ01">


    <insert id="insertTt" >
        insert into ${projectSchema}.
        T_YQTT (
<!--        FD_Condition_Id,-->
        FD_Name,
        FD_Type
        )
        values
        (
<!--        #fd_condition_id#,-->
        #fd_name#,
        #fd_type#
        );
    </insert>
    <insert id="insertCondition" >
    insert into ${projectSchema}.
    T_YQ_CONDITION (
    FD_ID,
    FD_NAME,
    FD_TYPE,
    FD_WORD,
    FD_SCENE,
    FD_TOKEN,
    FD_ACTIVE,
    FD_ACCOUNTS,
    FD_CATEGORY,
    FD_GROUP_ID,
    FD_IN_ORDER,
    FD_LOCATION,
    FD_PROVIDER,
    FD_RULE_EXTRA,
    FD_WORD_EVENT,
    FD_WORD_REGION,
    FD_MONITOR_TYPE,
    FD_WORD_EXCLUDE,
    FD_WORD_DISTANCE,
    FD_WORD_COMBINATION,
    FD_MONITOR_START_TIME,
    FD_MONITOR_END_TIME,
        FD_MAINID

    )
    values
    (
    #id#,
    #name#,
    #type#,
    #word#,
    #scene#,
    #token#,
    #active#,
    #accounts#,
    #category#,
    #group_id#,
    #in_order#,
    #location#,
    #provider#,
    #rule_extra#,
    #word_event#,
    #word_region#,
    #monitor_type#,
    #word_exclude#,
    #word_distance#,
    #word_combination#,
    #monitor_start_time#,
    #monitor_end_time#,
        #main_id#
    );
    </insert>
    <!--插入条件表-->
    <insert id="insertPlan" parameterClass="java.util.HashMap">
        insert into ${projectSchema}.
        T_YQ_PLAN (
        FD_ID,
        FD_NAME,
        FD_PROVIDER,
        FD_USER_ID,
        FD_DURATION,
        FD_FINISH_TIME,
        FD_COLLECTION_STATUS,
        FD_ABILITY,
        FD_SENSITIVE_FILE_COUNT,
        FD_CREATE_TIME,
        FD_GROUP_ID,
        FD_PROVIDER_PLAN_ID,
        FD_BEGIN_TIME,
        FD_YY_STATUS,
        FD_RULE_EXTRA,
        FD_UPDATE_TIME,
        FD_CONDITION_ID,
        FD_MAINID
        )
        values
        (
        #id#,
        #name#,
        #provider#,
        #user_id#,
        #duration#,
        #finish_time#,
        #collection_status#,
        #ability#,
        #sensitive_file_count#,
        #create_time#,
        #group_id#,
        #provider_plan_id#,
        #begin_time#,
        #yy_status#,
        #rule_extra#,
        #update_time#,
        #condition_id#,
        #main_id#
        );
    </insert>
    <insert id="insertMonitoring" parameterClass="java.util.HashMap">
        insert into ${projectSchema}.
        T_YQ_MONITORING_DATA (
        FD_ID,
        FD_TITLE,
        FD_PLATFORM,
        FD_LIKE_COUNT,
        FD_CONTENT,
        FD_PLATFORM_NAME,
        FD_JUDGE_CONFIRM_STATUS,
        FD_POST_CREATE_TIME,
        FD_IP_LOCATION,
        FD_SUB_CATEGORY_IDS,
        FD_PLAN_ID,
        FD_STATUS,
        FD_AVATAR,
        FD_WORKSET_ID,
        FD_BARRIER_STATUS,
        FD_DETECT_TIME,
        FD_LABELS,
        FD_WORK_ID,
        FD_NICKNAME,
        FD_FANS_COUNT,
        FD_PROVIDER,
        FD_URL,
        FD_SHARE_COUNT,
        FD_CREATE_TIME,
        FD_ORG_ID,
        FD_UNIQUE_ID,
        FD_COVER_URL,
        FD_COMMENT_COUNT,
        FD_UPDATE_TIME,
        FD_ALERT_TIME,
        FD_ALERT_USER,
        FD_JUDGE_TIME,
        FD_ALERT_USER_ID
        )
        values
        (
        #id#,
        #title#,
        #platform#,
        #like_count#,
        #content#,
        #platform_name#,
        #judge_confirm_status#,
        #post_create_time#,
        #ip_location#,
        #sub_category_ids#,
        #plan_id#,
        #status#,
        #avatar#,
        #workset_id#,
        #barrier_status#,
        #detect_time#,
        #labels#,
        #work_id#,
        #nickname#,
        #fans_count#,
        #provider#,
        #url#,
        #share_count#,
        #create_time#,
        #org_id#,
        #unique_id#,
        #cover_url#,
        #comment_count#,
        #update_time#,
        #alert_time#,
        #alert_user#,
        #judge_time#,
        #alert_user_id#
        );
    </insert>
    <select id="queryID"  resultClass="java.util.HashMap">

        SELECT FD_ID FROM ${projectSchema}.T_YQ_MONITORING_DATA;
    </select>
    <delete id="deleteMID" >
        delete from
        ${projectSchema}.T_YQ_MONITORING_DATA
        where
        FD_ID = #ids#
    </delete>
    <delete id="deletePID" >
        delete from
        ${projectSchema}.T_YQ_PLAN
        where
        FD_MAINID = #ids#
    </delete>
    <delete id="deleteCID" >
        delete from
        ${projectSchema}.T_YQ_CONDITION
        where
        FD_MAINID = #ids#
    </delete>

</sqlMap>