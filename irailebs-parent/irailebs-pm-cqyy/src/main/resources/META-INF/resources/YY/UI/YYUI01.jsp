<%@ page import="com.baosight.iplat4j.core.FrameworkInfo" %>
<%@ page import="com.baosight.iplat4j.core.web.threadlocal.UserSession" %>
<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%
    // 获取iPlatUI静态资源地址
    String iPlatStaticURL = FrameworkInfo.getPlatStaticURL(request);
    //用户信息
    UserSession.web2Service(request);
    String userId = UserSession.getLoginName();
    String userName = UserSession.getLoginCName();

    String iframeSrc = String.format("%s/vueui/index.html#?userid=%s&username=%s",
            request.getContextPath(),userId,userName);
%>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<c:set var="iPlatStaticURL" value="<%=iPlatStaticURL%>"/>

<html>
<head>
    <link rel="shortcut icon" href="${ctx}/iplat.ico" type="image/x-icon">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <script src="${ctx}/common/jquery/jquery-2.1.0.min.js"></script>
    <script src="${ctx}/common/jquery/jquery-ui.min.js"></script>
    <title></title>
    <style>
        body, html {
            height: 100%;
            margin: 0;
            overflow: hidden;
        }
    </style>
    <script>
        window.CONTEXT_PATH_VUE = "${pageContext.request.contextPath}";
        // 在父页面中添加事件监听器，接收来自iframe的消息
        window.addEventListener('message', function(event) {
            // 接收来自iframe的消息
            console.log('父页面收到消息:', event.data);
            var iframe = document.getElementById('docPageDesigner');
            iframe.contentWindow.postMessage(event.data, '*');
        });
    </script>
</head>
<body>
<div style="width:100%;height:100%">
    <iframe id="docPageDesigner" frameborder="0" style="width:100%;height:100%"
            src="<%=iframeSrc%>">
    </iframe>
</div>
</body>
</html>
