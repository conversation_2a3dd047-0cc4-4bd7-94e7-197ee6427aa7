# 重庆智能运营管理系统 (CQZNYY)

[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)
[![Java](https://img.shields.io/badge/Java-8+-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.x-green.svg)](https://spring.io/projects/spring-boot)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)]()

基于 iplat4j-boot-starter 企业框架的智能运营管理系统，采用现代化的微服务架构和前后端分离设计。

## 🏗️ 项目架构

```
cqznyy/
├── docs/                           # 项目文档
│   └── VERSION_MANAGEMENT.md       # 版本管理指南
├── irailebs-parent/                # 企业框架层
│   ├── irailebs-pm-cqyy/          # 业务模块
│   └── irailebs-agent-hub/        # 智能体门户
├── irailebs-common/               # 通用模块
├── irailebs-web/                  # Web应用层 (WAR)
├── irailebs-vue/                  # 前端模块 (Vue.js)
└── pom.xml                        # 根项目配置
```

## 🏛️ 架构设计

### 继承关系
```
cqyy (根项目)
├── irailebs-parent (继承 iplat4j-boot-starter)
│   ├── irailebs-pm-cqyy (业务模块)
│   └── irailebs-agent-hub (智能体门户)
├── irailebs-common (继承 cqyy)
├── irailebs-web (继承 cqyy) ← 通过依赖传递获得企业框架功能
└── irailebs-vue (继承 cqyy)
```

### 依赖传递链
```
irailebs-web
  → 依赖 irailebs-pm-cqyy
    → 继承 irailebs-parent
      → 继承 iplat4j-boot-starter (企业框架)
```

### 模块职责
- **cqyy**: 根项目，统一版本管理和基础配置
- **irailebs-parent**: 企业框架层，管理业务模块和企业框架依赖
- **irailebs-pm-cqyy**: 核心业务模块，包含主要业务逻辑
- **irailebs-agent-hub**: 智能体门户，AI功能集成
- **irailebs-common**: 通用工具和基础组件
- **irailebs-web**: Web应用层，最终WAR部署包
- **irailebs-vue**: 前端应用，Vue.js单页面应用

## 🎯 技术栈

### 后端技术
- **企业框架**: iplat4j-boot-starter 7.1.0
- **Java版本**: JDK 8+
- **构建工具**: Maven 3.6+
- **数据库**: 达梦数据库 (DM8)
- **缓存**: Redis (通过redis-plugin)
- **工具库**: Hutool 5.8.26

### 前端技术
- **框架**: Vue.js 3.x
- **构建工具**: Vite 7.0.0
- **包管理**: PNPM 9.14.2
- **Node.js**: 22.2.0+

## 🚀 快速开始

### 环境要求

- JDK 8 或更高版本
- Maven 3.6 或更高版本
- Node.js 22.2.0 或更高版本
- PNPM 9.14.2 或更高版本
- 达梦数据库 8.x

### 构建项目

```bash
# 克隆项目
git clone <repository-url>
cd cqznyy

# 构建所有模块
mvn clean install

# 只构建后端
mvn clean install -pl irailebs-parent

# 只构建前端
mvn clean install -pl irailebs-vue
```

### 运行项目

```bash
# 启动后端服务
cd irailebs-parent/irailebs-web
mvn spring-boot:run

# 启动前端开发服务器
cd irailebs-vue
pnpm dev
```

## 📋 模块说明

### irailebs-parent
企业框架层，继承自 iplat4j-boot-starter，提供：
- 统一的依赖管理
- 企业级框架集成
- 通用开发依赖 (lombok, hutool等)

### irailebs-pm-cqyy
核心业务模块，包含：
- 业务逻辑实现
- 数据访问层
- 服务层接口

### irailebs-agent-hub
智能体门户模块，负责：
- AI智能体管理
- 智能体服务编排
- 智能体交互接口
- 智能体生命周期管理

### irailebs-web
Web应用层，提供：
- REST API接口
- 运行时依赖管理
- WAR包部署配置

### irailebs-common
通用模块，包含：
- 公共工具类
- 通用配置
- 共享组件

### irailebs-vue
前端应用，基于Vue.js构建：
- 现代化UI界面
- 响应式设计
- 前后端分离架构

## 🔧 开发指南

### 依赖管理策略

项目采用分层依赖管理模式：

1. **Parent层**: 提供开发时通用依赖
2. **业务模块**: 保持轻量化，只包含业务逻辑
3. **Web模块**: 管理运行时依赖和部署配置

### 版本管理

使用Maven Versions Plugin进行版本管理，详见 [版本管理指南](docs/VERSION_MANAGEMENT.md)

```bash
# 更新业务模块版本
mvn versions:set-property -Dproperty=irailebs-pm-cqyy.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 验证构建
mvn clean compile

# 提交更改
mvn versions:commit
```

### 代码规范

- 使用Lombok简化代码
- 遵循阿里巴巴Java开发手册
- 统一使用UTF-8编码
- 代码注释使用中文

## 📦 部署说明

### 开发环境
```bash
# 后端
mvn spring-boot:run -pl irailebs-parent/irailebs-web

# 前端
cd irailebs-vue && pnpm dev
```

### 生产环境
```bash
# 构建WAR包
mvn clean package -pl irailebs-parent/irailebs-web

# 部署到应用服务器
cp irailebs-parent/irailebs-web/target/cqyy.war $TOMCAT_HOME/webapps/
```

## 📚 文档

- [版本管理指南](docs/VERSION_MANAGEMENT.md) - Maven版本管理详细说明
- [API文档](docs/API.md) - REST API接口文档 (待补充)
- [部署指南](docs/DEPLOYMENT.md) - 生产环境部署说明 (待补充)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目为企业内部项目，版权所有。

## 📞 联系方式

- 项目负责人: [联系信息]
- 技术支持: [技术支持邮箱]
- 项目地址: [Git仓库地址]

---

**注意**: 本项目基于 iplat4j-boot-starter 企业框架开发，请确保已获得相应的使用许可。
