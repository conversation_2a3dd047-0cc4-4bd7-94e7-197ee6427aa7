//
// $(function () {
//
//     let eventId = "7777777";
//     let userName = "admin";
//     let loadComplete = false;
//
//     $(window).on("load", function () {
//
//         let eiInfo = new EiInfo();
//         eiInfo.set("eventId", eventId);
//         //初始化读取电话通知人员列表
//         EiCommunicator.send("YJCZ02", "getPlanStep", eiInfo, {
//             onSuccess: function (response) {
//
//                 let thePlans = response.extAttr.thePlans;
//                 console.log(thePlans);
//
//                 thePlans.userName = userName;
//
//                 let template = kendo.template($("#planTemplate").html());
//                 thePlans.post = "NOCC预案";
//                 thePlans.purview = "noccPlan";
//                 let result = template(thePlans);
//                 $("#noccPlan").html(result);
//
//                 thePlans.post = "值班主任";
//                 thePlans.purview = "zbzr";
//                 result = template(thePlans);
//                 $("#zbzr").html(result);
//
//                 thePlans.post = "行车调度";
//                 thePlans.purview = "xcdd";
//                 $("#xcdd").html(template(thePlans));
//
//                 thePlans.post = "设备调度";
//                 thePlans.purview = "sbdd";
//                 $("#sbdd").html(template(thePlans));
//
//                 thePlans.post = "信息调度";
//                 thePlans.purview = "xxdd";
//                 $("#xxdd").html(template(thePlans));
//
//                 console.log($("#zbzr"))
//                 console.log($("#xcdd"))
//                 console.log($("#sbdd"))
//                 console.log($("#xxdd"))
//
//                 initCheckBox();
//             }
//         });
//
//
//     });
//
//     //设置checkBox点击事件
//     function initCheckBox() {
//         $(".col-check input").on("change", function (e) {
//             console.log(e);
//             console.log(e.currentTarget.name);
//
//             let eiInfo = new EiInfo();
//             eiInfo.set("eventId", eventId);
//             eiInfo.set("recordId", e.currentTarget.name);
//             EiCommunicator.send("YJCZ02", "completePlanStep", eiInfo, {
//                 onSuccess: function (response) {
//                     console.log(window.parent);
//                     console.log(window.opener);
//                     initResponseHRList();
//                 }
//             });
//         });
//     }
//
//
//     $("#addPlan").on("click", function () {
//         console.log($(".col-check input"));
//     });
//
//     $("#planButton").on("click", function () {
//         handlePlanWindow.open().center();
//     });
//
// });
