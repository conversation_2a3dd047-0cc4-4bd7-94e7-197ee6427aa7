/* 主样式文件 */
:root {
    --frame-dh: 950px;
    --frame-dw: 1920px
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f0f0f0;
    overflow: hidden;
}

.i-theme-nocc form.i-form {
    padding: 0;
}

#app {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* 主容器样式 */
.main-container {
    width: calc(var(--frame-dw) * 2);
    height: var(--frame-dh);
    display: flex;
    background-color: transparent;
    transform-origin: top left;
    transition: transform 0.3s ease;
}

/* 页面容器样式 */
.page-container {
    width: var(--frame-dw);
    height: var(--frame-dh);
    position: relative;
    /*: 1px solid #ddd;*/
}

.page-container:last-child {
    border-right: none;
}

/*.left-container {*/
/*    background-color: #f8f9fa;*/
/*}*/

.right-container {
    background-color: #fff8f0;
}

/* iframe样式 */
.page-frame {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
}

/* 弹窗容器 */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* 拖拽辅助层 */
.drag-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 999;
    cursor: grabbing;
}

.drag-overlay.active {
    display: block;
}

/* 控制面板样式 */
.control-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2000;
    min-width: 250px;
}

.control-group {
    margin-bottom: 15px;
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.control-group input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.control-group button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 8px;
    margin-bottom: 5px;
    font-size: 12px;
}

.control-group button:hover {
    background-color: #0056b3;
}

.control-group button:last-child {
    margin-right: 0;
}

/* 响应式适配 */
@media (max-width: 1920px) {
    .control-panel {
        top: 10px;
        right: 10px;
        padding: 15px;
        min-width: 200px;
    }
}

/* 缩放状态指示器 */
.scale-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 2000;
}

/* 页面标识 */
.page-container::before {
    content: attr(data-page-name);
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
    pointer-events: none;
}

.left-container::before {
    content: "左侧页面";
}

.right-container::before {
    content: "右侧页面";
}

/* 加载状态 */
.page-container.loading::after {
    content: "加载中...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 5;
}

/* 错误状态 */
.page-container.error::after {
    content: "加载失败";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 0, 0, 0.1);
    color: #d32f2f;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #d32f2f;
    z-index: 5;
}
