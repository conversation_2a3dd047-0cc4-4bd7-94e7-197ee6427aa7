<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="事件历史" prefix="nocc">
    <jsp:attribute name="header">
        <style>
            .eventHistory{
                width: 1815px;
                margin: 0 auto;
                padding-top: 20px;
                height: 775px;
            }
            .contain-head {
                width: 1200px;
                position: relative;
                margin-bottom: 25px;
                left: 576px;
            }
        </style>
    </jsp:attribute>
        <jsp:body>
            <div class="row">
                <div class="page-title">事件历史</div>
            </div>
            <EF:EFRegion head="hidden" class="eventHistory">
                <div class="contain-head">
                    <EF:EFDateSpan startName="inqu_status-0-fdTimeStart" endName="inqu_status-0-fdTimeEnd" startCname="开始时间" endCname="结束时间"
                                   role="datetime" ratio="4:4" startRatio="3:9" endRatio="3:9"
                                   parseFormats="['yyyy-MM-dd HH:mm:ss']" colWidth="3"/>
                    <EF:EFInput cname="关键字" ename="inqu_status-0-fdName" ratio="3:9" colWidth="3"/>
                    <EF:EFButton ename="queryHistory" cname="查询"/>
                </div>
                    <EF:EFGrid blockId="result" autoDraw="no" autoBind="true" isFloat="true"
                               checkMode="single,row"  pagerPosition="bottom" enable="false" height="600"
                               toolbarConfig="{hidden:'all'}" >
                        <EF:EFColumn ename="num" cname="序号" enable="false" primaryKey="true" align="center" width="30"/>
                        <EF:EFColumn ename="fdName" cname="事件名称" enable="false" primaryKey="true" align="center" width="100"/>
                        <EF:EFColumn ename="fdTime" cname="事发时间" enable="false" align="center" width="100"/>
                        <EF:EFColumn ename="fdPlace" cname="地点" enable="false" align="center" width="100"/>
                        <EF:EFColumn ename="fdRespTName" cname="响应等级" enable="false" align="center" width="50"/>
                        <EF:EFColumn ename="fdOperator" cname="填报人员" enable="false" align="center" width="50"/>
                        <EF:EFColumn ename="fdDisposalTName" cname="解除方式" enable="false" align="center" width="50"/>
                        <EF:EFColumn ename="fdMsgDesc" cname="事件描述" enable="false" align="center" width="150"/>
                        <EF:EFColumn ename="fdAssessReport" cname="中心级总结报告" enable="false" align="center" width="80"/>
                        <EF:EFColumn ename="fdFirmReport" cname="公司级总结报告" enable="false" align="center" width="80"/>
                        <EF:EFColumn ename="fdDisposalRecords" cname="处置记录" enable="false" align="center" width="80"/>
                    </EF:EFGrid>

            </EF:EFRegion>
        </jsp:body>
    </EF:EFPage>

    <EF:EFWindow id="upload" lazyload="true" title=" " width="70%" height="77%">
        <EF:EFRegion head="hidden">
        </EF:EFRegion>
    </EF:EFWindow>

    <EF:EFWindow id="filePreview" url="${ctx}/web/YJCZ0301" width="65%" height="70%" top="0" title="文件预览" lazyload="true"></EF:EFWindow>
</div>