var uuid = "";
var userName = "";
$(function (){
    /**
     * 源文件查看
     */
    var RowData = "";
    IPLATUI.EFGrid = {
        "updateData": {
            columns: [
                {
                    field: "updateFileShow",
                    title: "源文件查看",
                    encoded: false,
                    template: function (e) {
                        var urlJson = JSON.parse(e.fileSrc);
                        if(urlJson != null){
                            var url = urlJson.bucketName + '/' + urlJson.fileName;
                            return '<a href="javascript:void(0);" onclick="previewFile(\'' + url + '\')">查看</a>';
                        }else {
                            return "";
                        }

                    }
                },{
                    field: "updateMan",
                    title: "更新人",
                    encoded: false,
                    template: function (e) {
                        if(e.updateMan == 'admin'){
                            return  '系统管理员';
                        }else {
                            return e.updateMan;
                        }
                    }
                },
                {
                    field: "updateBefore",
                    title: "修改查看",
                    encoded: false,
                    template: function (e) {
                        var updateBefore = e.updateBefore;
                        var updateAfter = e.updateAfter;
                        if(updateBefore != null){
                            return '<a href="javascript:void(0);" onclick="previewUpdateInfo(\'' + updateBefore + '\',\'' + updateAfter + '\' )">查看</a>';
                            // previewUpdateInfo
                            return "查看";
                        }else {
                            return "";
                        }
                    }
                },
            ],
            pageable: {
                pageSize: 50, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                pageSizes: [15, 20, 50, 100] // "all"] // 分页配置
            },
        },
        "dutyData": {
            pageable: {
                pageSize: 15, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                pageSizes: [15, 20, 50, 100] // "all"] // 分页配置
            },
            onRowClick: function (e) {
                RowData = e.model;
            }
        },
        "dutyMsgData": {
            pageable: {
                pageSize: 15, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                pageSizes: [15, 20, 50, 100] // "all"] // 分页配置
            }
        },
    };

    /**
     * 页面初始化
     */
    // $(window).on("load", function () {
    //     let eiInfo = new EiInfo();
    //     EiCommunicator.send("YJZS02", "query", eiInfo, {
    //         onSuccess: function (response) {
    //             dutyDataGrid.setEiInfo(response);
    //         }
    //     });
    // });

    //修改
    $("#updateInfo").on("click", function () {

        var checkRows = dutyDataGrid.getCheckedRows();
        if (checkRows.length!=1){
            IPLAT.alert({
                message: '<b>请选择一条值班数据进行修改！</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        uuid = checkRows[0].uuid;
        userName = IPLAT.getParameterByName("userName");
        $("#YJZS0202").data("kendoWindow").setOptions({
            open: function () {
                $("#YJZS0202").data("kendoWindow").refresh({
                    url: IPLATUI.CONTEXT_PATH + "/web/YJZS0202?uuid=" + uuid +"&userName="+userName
                });
            }
        });
        YJZS0202Window.title("值班信息修改").center().open();
    });

    /**
     * 查询按钮事件
     */
    $("#queryData").on("click", function () {
        dutyDataGrid.dataSource.page(1);
        updateDataGrid.dataSource.page(1);
    });

    /**
     * 导入弹框
     */
    $('#importData').on('click', function () {

        IPLAT.ParamWindow({
            id: "upload",
            formEname: "BIFS99",
            params: ''
        });

        // let fileName = "运营公司值班表导入测试.xlsx";
        // let filePath = "http://************:8080/group1/default/FileSystemControl/project/DownLoad/运营值守管理/导入/运营公司值班表导入测试.xlsx";
        // let eiInfo = new EiInfo();
        // eiInfo.set("urlStr", filePath);
        // eiInfo.set("fileName", fileName);
        // //调用service及服务根据自身变更
        // EiCommunicator.send("YJZS02", "importFile", eiInfo, {
        //     onSuccess: function (response) {
        //         if(response.status != -1){
        //             IPLAT.alert({
        //                 message: '<b>导入附件成功！</b>',
        //                 okFn: function (e) {
        //                     location.reload();
        //                 },
        //                 title: '提示'
        //             });
        //         }else {
        //             IPLAT.alert({
        //                 message: '<b>' +"导入失败："+ response.msg +'</b>',
        //                 okFn: function (e) {
        //                     location.reload();
        //                 },
        //                 title: '导入提示'
        //             });
        //         }
        //         // let msg = response.extAttr.msg;
        //         uploadWindow.close();
        //     }
        // });
        });


    /**
     * 导出按钮事件
     */
    $("#exportData").on("click", function () {
        var inInfo = new EiInfo();
        var dutyList = [];
        dutyList.push()
        inInfo.set("startDate", $('#inqu_status-0-startDate')[0].value);
        inInfo.set("endDate", $('#inqu_status-0-endDate')[0].value);
        EiCommunicator.send("YJZS02", "exportDutyInfo", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() === 1) {
                    IPLAT.alert({
                        message: '<b>数据导出成功!</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                } else {
                    IPLAT.alert({
                        message: '<b>' + response.getMsg() +'</b>',
                        okFn: function (e) {},
                        title: '导出提示'
                    });
                }
            }
        });
    });

    /**
     * 发布弹框
     */
    $('#releaseData').on('click', function () {
        if ($('#inqu_status-0-content')[0].value == "") {
            IPLAT.alert({
                message: '<b>请先填写发布内容信息</b>',
                okFn: function (e) {
                },
                title: '提示'
            });
            return;
        }
        let eiInfo = new EiInfo();
        var userName = IPLAT.getParameterByName("userName");
        if(userName==null)userName ="admin";
        eiInfo.set("userName",userName);
        eiInfo.set("content",$('#inqu_status-0-content')[0].value);
        // eiInfo.set("date",$('#inqu_status-0-date')[0].value);
        // eiInfo.set("dutyType",$('#inqu_status-0-department')[0].value);//发布只需要内容和系统“当天”的值班人员信息，

        EiCommunicator.send("YJZS02", "releaseWorkMssInfo", eiInfo, {
            onSuccess: function (response) {
                let resultStatus = response.getStatus();
                if (resultStatus == 0){
                    IPLAT.alert({
                        message: '<b>发布成功！</b>',
                        okFn: function (e) {
                            location.reload();
                        },
                        title: '提示'
                    });
                }
                if (resultStatus != 0){
                    IPLAT.alert({
                        message: '<b>发布失败:'+response.msg+'</b>',
                        okFn: function (e) {
                            location.reload();
                        },
                        title: '提示'
                    });
                }
            }
        });
    });

});

/**
 * 文件导入回调函数
 * * */
function fileUrlCallBack(response) {
    let fileArr = JSON.parse(response).files;
    if (fileArr.length > 1) {
        IPLAT.alert({
            message: '<b>暂不支持批量上传文件，请重新选择</b>',
            okFn: function (e) {
            },
            title: '提示'
        });
        return;
    }
    let fileName = fileArr[0]["fileName"];
    let filePath = fileArr[0]["filePath"];
    //本机测试时使用获取固定模板文件
    // let fileName = "运营公司值班表.xlsx";
    // // let filePath = "http://************:8090/home/<USER>/files/default/FileSystemControl/project/运营公司值班表.xlsx";
    // let filePath = "http://************:8080/group1/default/FileSystemControl/project/运营公司值班表.xlsx";
    let eiInfo = new EiInfo();
    eiInfo.set("urlStr", filePath);
    eiInfo.set("fileName", fileName);
    var userName = IPLAT.getParameterByName("userName");
    if(userName==null)userName ="admin";
    eiInfo.set("userName",userName);
    //调用service及服务根据自身变更
    EiCommunicator.send("YJZS02", "importFile", eiInfo, {
        onSuccess: function (response) {
            let msg = response.msg;
            if (response.getStatus() != -1) {
                IPLAT.alert({
                    message: '<b>值班信息导入成功!</b>',
                    okFn: function (e) {
                        location.reload();
                    },
                    title: '导入提示'
                });
                uploadWindow.close();
            } else {
                IPLAT.alert({
                    message: '<b>' +"导入失败："+ msg +'</b>',
                    okFn: function (e) {
                        location.reload();
                    },
                    title: '导入提示'
                });
            }
            uploadWindow.close();
        }
    });
}

/**
 * 预览方法
 * @param url-路径
 */
var previewFile = function (url) {
    console.log(url);
    EiCommunicator.send("YJZS02", "getSystemType", new EiInfo(), {
        onSuccess: function (response) {
            var previewUrl = 'http://*************:80/ossrest/api/object/'+response.getAttr().systemType.toString()+"/"+url+'?tenant=1';
            IPLAT.ParamWindow({
                id: "filePreview",
                formEname: "YJZS0201",
                params: 'filePath='+IPLAT._decodeURI(previewUrl)
            })
        }
    });
}

/**
 * 预览方法
 * @param url-路径
 */
var previewUpdateInfo = function (updateBefore,updateAfert) {
    console.log(updateBefore+"     "+updateAfert);
    let msg="原值班人员信息：</b>"+updateBefore
    IPLAT.alert({
        message: '<b>'+"原值班人员信息：<br>" + updateBefore +"<br>修改为：<br>"+updateAfert+'</br>',
        okFn: function (e) {
        },
        title: '修改记录'
    });
}