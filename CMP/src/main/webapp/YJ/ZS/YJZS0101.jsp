<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="window-background">
    <EF:EFPage title="班组人员调整" prefix="nocc">
    <jsp:attribute name="header">
        <style>
            .page-contain{
                width: 400px;
                margin: 30px auto;
            }
            .page-contain>.col-md-4{
                height: 40px;
            }
        </style>
    </jsp:attribute>
        <jsp:body>
            <div class="page-contain">
                <EF:EFSelect ename="director" ratio="5:7" cname="NOCC调度主任:"
                             serviceName="YJZS01" methodName="queryStaffing"
                             resultId="employeeResult"
                             textField="name" valueField="phone">
                </EF:EFSelect>
                <EF:EFSelect ename="information" ratio="5:7" cname="NOCC信息调度:"
                             serviceName="YJZS01" methodName="queryStaffing"
                             resultId="employeeResult"
                             textField="name" valueField="phone">
                </EF:EFSelect>
                <EF:EFSelect ename="equipment" ratio="5:7" cname="NOCC设备调度:"
                             serviceName="YJZS01" methodName="queryStaffing"
                             resultId="employeeResult"
                             textField="name" valueField="phone">
                </EF:EFSelect>
                <EF:EFSelect ename="driving" ratio="5:7" cname="NOCC行车调度:"
                             serviceName="YJZS01" methodName="queryStaffing"
                             resultId="employeeResult"
                             textField="name" valueField="phone">
                </EF:EFSelect>
            </div>

            <div class="btn-custom-group">
                <EF:EFButton ename="sure" cname="确认"/>
                <EF:EFButton ename="cansel" cname="取消"/>
            </div>
        </jsp:body>
    </EF:EFPage>
</div>