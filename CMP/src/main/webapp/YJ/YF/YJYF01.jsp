<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>
<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<%
    String websocketUrl = "";
    try {
        websocketUrl = PlatApplicationContext.getProperty("websocketUrl");
    } catch(Exception e) {
    }
%>
<c:set var="websocketUrl" value="<%=websocketUrl%>"/>
<script type="text/javascript">
    var websocketUrl ="${websocketUrl}";
</script>
<script src="${ctx}/rplatui/util/webscoketUtil.js"></script>
<div class="page-background">
    <EF:EFPage title="预警发布" prefix="nocc">
    <jsp:attribute name="header">
        <style>
            .newWarn, .warnInfo {
                display: inline-block;
                width: 895px;
            }

            .warnInfo {
                position: relative;
                margin-left: 15px;
            }

            .page-up {
                width: 1815px;
                margin: 0 auto;
            }

            .metWarn, .seismicWarn {
                padding-left: -15px;
                width: 100%;
                height: 300px;
                margin-top: 35px;
            }

            .warnInfo .row {
                margin-top: 15px;
            }

            .infoButtonGroup {
                position: absolute;
                bottom: 30px;
                right: 106px;
            }

            .moduleBorder {
                width: 1810px;
                height: 385px;
                margin: 7px auto;
            }

            #metWarnDesc {
                height: 100px;
            }

            .moduleBorder > .row:first-child {
                width: 1635px;
                position: relative;
                left: 257px;
                margin-bottom: 15px;
            }

            .block-options {
                display: none;
            }

            .k-content, .k-editable-area, .k-panel > li.k-item, .k-panelbar > li.k-item, .k-tiles {
                background-color: transparent;
            }

            .i-theme-nocc input.i-state-readonly, .i-theme-nocc input[readonly=readonly]+span, .i-theme-nocc input[readonly]{
                background: transparent;
            }
        </style>
    </jsp:attribute>
        <jsp:body>
            <div class="row">
                <div class="page-title">预警发布</div>
            </div>
            <div class="row page-up">
                <EF:EFRegion title="最新预警" class="newWarn">
                    <div class="row" style="height: 15px"></div>
                    <EF:EFGrid blockId="warnResult" autoDraw="no" isFloat="true" checkMode="single,row"
                               rowNo="true,hidden" autoBind="false"
                               enable="hidden" autoFit="true" pagerPosition="bottom"
                               toolbarConfig="{hidden:'all'}" height="320" queryMethod="queryNewData">
                        <EF:EFColumn ename="title" cname="预警标题" enable="false" primaryKey="true" align="center"
                                     width="300"/>
                        <EF:EFColumn ename="time" cname="预警时间" enable="false" align="center" width="100"/>
                    </EF:EFGrid>
                </EF:EFRegion>
                <EF:EFRegion title="预警详情" class="warnInfo">
                    <div class="metWarn" style="display: block">
                        <div class="row">
                            <EF:EFInput cname="预警标题" ename="metWarnTitle" value="" readonly="true" ratio="2:9" eable="false" colWidth="12"/>
                        </div>
                        <div class="row">
                            <EF:EFInput cname="发布时间" ename="metWarnPublishTime" readonly="true" value="" ratio="4:8" colWidth="6"/>
                            <EF:EFInput cname="预警等级" ename="metWarnLevel" value="" readonly="true" ratio="3:7" colWidth="6"/>
                        </div>
                        <div class="row">
                            <EF:EFInput cname="预警内容" ename="metWarnDesc" maxLength="500" data-rules="String"
                                        colWidth="12"
                                        type="textarea" ratio="2:9"/>
                        </div>
                    </div>
                    <div class="seismicWarn" style="display: none">
                        <div class="row">
                            <EF:EFInput cname="预警标题" ename="seismicWarnTitle" readonly="true" value="" ratio="2:9" colWidth="12"/>
                        </div>
                        <div class="row">
                            <EF:EFInput cname="地震时间" ename="seismicWarnTime" readonly="true" value="" ratio="4:8" colWidth="6"/>
                            <EF:EFInput cname="震中位置" ename="seismicWarnEpicenter" readonly="true" value="" ratio="3:7" colWidth="6"/>
                        </div>
                        <div class="row">
                            <EF:EFInput cname="震级" ename="seismicWarnMagnitude" readonly="true" value="" ratio="4:8" colWidth="6"/>
                            <EF:EFInput cname="深度" ename="seismicWarnDepth" readonly="true" value="" ratio="3:7" colWidth="6"/>
                        </div>
                        <div class="row">
                            <EF:EFInput cname="经度" ename="seismicWarnLongitude" readonly="true" value="" ratio="4:8" colWidth="6"/>
                            <EF:EFInput cname="纬度" ename="seismicWarnLaitude" readonly="true" value="" ratio="3:7" colWidth="6"/>
                        </div>
                    </div>
                    <div class="row infoButtonGroup">
                        <EF:EFButton ename="notifyPeopleButton" cname="发布范围调整"/>
                        <EF:EFButton ename="relieveButton" cname="移除"/>
                        <EF:EFButton ename="publishButton" cname="发布"/>
                    </div>
                        <EF:EFInput ename="infoForm-0-fdMsgAddressValue" cname="消息通知Json值" type="hidden"></EF:EFInput>
                        <EF:EFInput ename="infoForm-0-fdMsgAddressContent" cname="消息通知" type="hidden"></EF:EFInput>
                        <EF:EFInput ename="infoForm-0-fdPhoneAddressValue" cname="电话通知Json值" type="hidden"></EF:EFInput>
                        <EF:EFInput ename="infoForm-0-fdPhoneAddressContent" cname="电话通知" type="hidden"></EF:EFInput>
                </EF:EFRegion>
            </div>
            <div class="row">
                <div class="moduleBorder">
                    <div class="row">
                        <EF:EFSelect ename="warnLevel" cname="预警等级" defaultValue="All" colWidth="2">
                            <EF:EFOption label="全部" value="All"/>
                            <EF:EFCodeOption codeName="nocc.yj.yf03"/>
                        </EF:EFSelect>
                        <EF:EFSelect ename="publishStatus" cname="发布状态" defaultValue="All" colWidth="2">
                            <EF:EFOption label="全部" value="All"/>
                            <EF:EFCodeOption codeName="nocc.yj.yf02"/>
                        </EF:EFSelect>
                        <EF:EFDateSpan startName="startDate" endName="endDate" startCname="开始时间" endCname="结束时间"
                                       role="datetime" ratio="3:3" startRatio="3:9" endRatio="3:9"
                                       parseFormats="['yyyy-MM-dd HH:mm:ss']" colWidth="3"/>
                        <EF:EFButton ename="queryHistory" cname="查询"/>
                    </div>
                    <EF:EFGrid blockId="warnHisResult" autoDraw="no" isFloat="true" checkMode="single,row"
                               rowNo="true,hidden" autoBind="false"
                               enable="hidden" autoFit="true" pagerPosition="bottom"
                               toolbarConfig="{hidden:'all'}" height="320" queryMethod="queryHisData">
                        <EF:EFColumn ename="id" cname="序号" enable="false" primaryKey="true" align="center" width="30"/>
                        <EF:EFColumn ename="source" cname="预警来源" enable="false" align="center" width="60"/>
                        <EF:EFColumn ename="title" cname="预警标题" enable="false" align="center" width="180"/>
                        <EF:EFColumn ename="publishContent" cname="发布内容" enable="false" align="center" width="300"/>
                        <EF:EFColumn ename="time" cname="预警时间" enable="false" align="center" width="100"/>
                        <EF:EFColumn ename="publishTime" cname="发布时间" enable="false" align="center" width="100"/>
                        <EF:EFColumn ename="level" cname="预警等级" enable="false" align="center" width="50"/>
                        <EF:EFColumn ename="publishType" cname="发布状态" enable="false" align="center" width="50"/>
                    </EF:EFGrid>
                </div>
            </div>
            <EF:EFWindow id="insertInfoRecipient" url="${ctx}/web/XFFB0101" width="70%" height="80%"
                         lazyload="true"  title="通知人员调整"/>
            <EF:EFWindow id="insertPhoneRecipient" url="${ctx}/web/XFFB0101" width="70%" height="80%" lazyload="true" refresh="true" title="电话通知"/>
        </jsp:body>
    </EF:EFPage>
</div>