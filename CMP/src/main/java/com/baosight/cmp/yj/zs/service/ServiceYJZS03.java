package com.baosight.cmp.yj.zs.service;
import cn.hutool.core.lang.UUID;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * 应急值守-防汛值守service
 * @author: weijiaheng
 * @date: 2023/08/14 16:36:21
 */
public class ServiceYJZS03 extends ServiceBase {
    //occ上传地址
    private static final String ossUrl = PlatApplicationContext.getProperty("iplat4j.admin.type");
    /**
     * @description: 初始化方法
     * @param inInfo
     * @return 数据集
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        inInfo.set("ossUrl",ossUrl);
        return inInfo;
    }

    /**
     * @description: 调用智能应急调度外部接口
     * 微服务号：S_YJ_ZS_01
     * @param inInfo
     * @return 数据集
     */
    public  EiInfo insertDutyFloor(EiInfo inInfo){
        Map map = new HashMap();
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String uuid = UUID.randomUUID().toString();//UUID
        String lineName = inInfo.getString("line_number"); //线路
        String place = inInfo.getString("inspect_place"); //巡查地点
        String department = inInfo.getString("inspect_department"); //部门
        String name = inInfo.getString("inspect_name"); //现场联系人姓名
        String phone = inInfo.getString("inspect_telephone"); //现场联系人电话
        String workNumber = inInfo.getString("work_number"); //工号
        String onSiteContactPerson = name + " " + phone;
        String operation = inInfo.getString("inspect_picture"); //附图

        String floodSituation = inInfo.getString("flood_condition"); //汛情
        String reportTime = inInfo.getString("report_time"); //上报时间
        String creator = "admin"; //创建人
        String createTime = dateFormat.format(date); //创建时间
        map.put("Uuid",uuid);
        map.put("line",lineName);
        map.put("place",place);
        map.put("desc",floodSituation);
        map.put("contact",onSiteContactPerson);
        map.put("annexSrc",operation);
        map.put("departmentType"," ");
        map.put("department",department);
        map.put("reportTime",reportTime);
        map.put("creator",creator);
        map.put("createTime",createTime);
        dao.insert("YJZS03.insertCmpFloor",map);
        return inInfo;
    }

    /**
     * @description: 查询防汛信息方法
     * @param inInfo
     * @return 数据集
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        //获取日期和部门类型和线路
        String department = inInfo.getString("inqu_status-0-department");
//        String reportTime = inInfo.getString("inqu_status-0-date");
        String startDate = inInfo.getString("inqu_status-0-startDate");
        String endDate = inInfo.getString("inqu_status-0-endDate");
        String line = inInfo.getString("inqu_status-0-line");
        //连接数据库查询
        Map map = new HashMap();
        //判断部门类型是否为空
        if (StringUtils.isNotEmpty(department)){
            map.put("department",department);
        }
        //判断日期是否为空
//        if (StringUtils.isNotEmpty(reportTime)){
//            map.put("reportTime",reportTime);
//        }
        if (StringUtils.isNotEmpty(startDate)){
            map.put("startDate",startDate + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(endDate)){
            map.put("endDate",endDate + " 23:59:59");
        }
        //判断线路是否为空
        if (StringUtils.isNotEmpty(line)){
            map.put("line",line);
        }
        EiBlock block = inInfo.getBlock("result");
        //设置默认值,因为初始化时获取不到result的block
        int offset = 0;
        int limit = 15;
        if (block != null){
            offset = block.getInt(EiConstant.offsetStr);
            limit = block.getInt(EiConstant.limitStr);
        }
        //查询数量总数并设置count,showCount也需要设置,不然显示条数不正确
        List<Map> list = dao.query("YJZS03.queryCmpFloor",map,offset,limit);
        List count = dao.query("YJZS03.count", map);
        inInfo.addRows("result",list);
        inInfo.set("result-count", count.get(0));
        inInfo.set("result-showCount", "true");
        //重新生成序号,并赋值给属性,前端展示该属性
        for (int i = 0; i < list.size(); i++) {
            list.get(i).put("num", offset + (i+1));
        }
        return inInfo;
    }

    /**
     * @description: 查询部门类型
     * @param inInfo
     * @return 数据集
     */
    public EiInfo queryDept(EiInfo inInfo) {
        //连接数据库查询部门类型
        Map map = new HashMap();
        List list = dao.query("YJZS03.queryCmpDept", map);
        inInfo.addRows("result",list);
        return inInfo;
    }
    /**
     * @description: 查询线路类型
     * @param inInfo
     * @return 数据集
     */
    public EiInfo queryLine(EiInfo inInfo) {
        //连接数据库查询部门类型
        Map map = new HashMap();
        List list = dao.query("YJZS03.queryCmpLine", map);
        inInfo.addRows("result",list);
        return inInfo;
    }

    /**
     * @description: 初始化查询当日防汛排查信息
     * @param inInfo
     * @return 数据集
     */
    public EiInfo queryNowDate(EiInfo inInfo){
        //获取到当日的日期
        LocalDate date = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String reportNowTime = date.format(formatter);
        Map map = new HashMap();
        //连接数据库查询当日防汛排查信息
        map.put("reportTime",reportNowTime);
        List list = dao.query("YJZS03.queryCmpFloor",map);
        //将分页下拉框设置为15
        if(inInfo.getBlock("result") != null){
            inInfo.getBlock("result").set("limit", 15);
            inInfo.addRows("result",list);
        }else {
            EiBlock result = new EiBlock("result");
            result.set("limit", 15);
            result.addRows(list);
            inInfo.addBlock(result);
        }
        return inInfo;
    }

    /**
     * @description: 导出接口
     * @param inInfo
     * @return 数据集
     */
    public EiInfo exportFloorInfo(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            //查询条件
//            String queryDate = Optional.ofNullable(inInfo.get("inqu_status-0-date").toString()).orElse("");
            String department = Optional.ofNullable(inInfo.get("inqu_status-0-department").toString()).orElse("");
            String line = Optional.ofNullable(inInfo.get("inqu_status-0-line").toString()).orElse("");
            String startDate = Optional.ofNullable(inInfo.getString("inqu_status-0-startDate")).orElse("");
            String endDate = Optional.ofNullable(inInfo.getString("inqu_status-0-endDate")).orElse("");
            //1.获取到当日的日期
            LocalDate date = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String reportNowTime = date.format(formatter);
            Map map = new HashMap();
            //根据当前查询条件查询
            if (StringUtils.isNotEmpty(startDate)){
                map.put("startDate",startDate + " 00:00:00");
            }
            if (StringUtils.isNotEmpty(endDate)){
                map.put("endDate",endDate + " 23:59:59");
            }
            //查询时间段都为空时默认导出当天的数据
            if(StringUtils.isEmpty(startDate) && StringUtils.isEmpty(endDate)){
                map.put("startDate",reportNowTime + " 00:00:00");
                map.put("endDate",reportNowTime + " 23:59:59");
            }

            if (StringUtils.isNotBlank(department)){
                map.put("department",department);
            }
            if (StringUtils.isNotBlank(line)){
                map.put("line",line);
            }
            List<Map<String,Object>> floorList = dao.query("YJZS03.queryExportFloor",map);
            //2.调用文件生成接口微服务拿到文件流
            inInfo.set("data",floorList);
            inInfo.set("command","export_floor");
            EiInfo outInfo1 = callXService(inInfo,"S_FS_02");
            //测试生成模版
//            EiInfo outInfo1 = callXService(inInfo,"S_TEST_04");
//            String floorByte = (String) outInfo1.get("floorByte");
//            byte[] bytes = floorByte.getBytes();
            byte[] bytes = outInfo1.toJSON().getBytes("floorByte");
            //3.上传至fileServer
            EiInfo eiInfo = new EiInfo();
            String version = reportNowTime;
            eiInfo.set("fileName", "防汛巡查"+ version +".xlsx");
            eiInfo.set("file", bytes);
            eiInfo.set("path", "防汛巡查/");
            eiInfo.set(EiConstant.serviceId,"S_RF_02");
            outInfo = XServiceManager.call(eiInfo);
            if (outInfo.getStatus() == -1) {
                throw new PlatException("导出到fileServer失败" + outInfo.getMsg());
            }
        } catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("数据导出失败：" + e.getMessage());
            return outInfo;
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("数据导出成功！");
        return outInfo;
    }

    /**
     * @description: 调用微服务方法
     * @param inInfo serviceId
     * @return outInfo
     */
    public EiInfo callXService(EiInfo inInfo, String serviceId){
        inInfo.set(EiConstant.serviceId, serviceId);
        EiInfo outInfo = XServiceManager.call(inInfo);
        //注意必须对outInfo的status状态进行校验
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }

    /*
     * 获取当前系统类型，区分测试环境与正式环境
     * */
    public EiInfo getSystemType(EiInfo info){
        info.set("systemType",PlatApplicationContext.getProperty("iplat4j.admin.type"));
        return info;
    }
}
