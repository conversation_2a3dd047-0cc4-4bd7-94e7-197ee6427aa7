<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="YJCZ02">
    <!--查询响应人员表-->
    <select id="queryResponseHRRecord" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_record_id as "recordUuid",
        fd_event_id as "eventId",
        fd_person_type as "type",
        fd_user_id as "userId",
        fd_name as "person",
        fd_post as "post",
        fd_telephone as "tel",
        fd_notice_time as "noticeTime",
        fd_respond_time as "respondTime",
        fd_arrival_time as "arrivalTime"
        from ${cmpProjectSchema}.t_em_handle_response_hr
        where
        fd_event_id = #eventId#
    </select>
    <!--查询响应人员表-仅响应时间-->
    <select id="queryResponseHRTime" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_user_id as "userId",
        fd_notice_time as "noticeTime",
        fd_respond_time as "respondTime",
        fd_arrival_time as "arrivalTime"
        from ${cmpProjectSchema}.t_em_handle_response_hr
        where
        fd_event_id = #eventId#
    </select>
    <!--查询响应人员表-人名-->
    <select id="queryResponseHRName" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_person_type as "type",
        fd_user_id as "userId",
        fd_name as "person",
        fd_notice_time as "noticeTime",
        fd_respond_time as "respondTime",
        fd_arrival_time as "arrivalTime"
        from ${cmpProjectSchema}.t_em_handle_response_hr
        where
        fd_event_id = #eventId#
    </select>
    <!--删除响应人员信息-->
    <delete id="delResponseHR" parameterClass="java.util.HashMap">
        DELETE FROM ${cmpProjectSchema}.t_em_handle_response_hr
        WHERE
        fd_event_id = #eventId#
    </delete>
    <!--查询处置记录表-->
    <select id="queryHandleRecord" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_record_id as "recordUuid",
        fd_event_id as "eventId",
        fd_record_type as "type",
        fd_person as "person",
        fd_datetime as "time",
        fd_content as "content"
        from ${cmpProjectSchema}.t_em_handle_record
        where
        fd_event_id = #eventId#
        order by fd_datetime
    </select>
    <!--查询指令记录表-->
    <select id="queryCommanderChange" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_record_id as "recordUuid",
        fd_event_id as "eventId",
        fd_instruct_type as "type",
        fd_datetime as "datetime",
        fd_person as "person",
        fd_content as "content",
        fd_annex as "annex"
        from ${cmpProjectSchema}.t_em_handle_instruct
        where
        fd_event_id = #eventId#
    </select>
<!--    查询指挥长，负责人-->
    <select id="queryChiefOrHead" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_person as "person",
        fd_instruct_type as "type"
        from ${cmpProjectSchema}.t_em_handle_instruct
        where
        fd_event_id = #eventId#
        <isNotEmpty prepend="and" property="type">
            fd_instruct_type=#type#
        </isNotEmpty>
        order by fd_datetime desc
    </select>
    <!--查询现场处置情况记录表-->
    <select id="queryHandleSituation" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_record_id as "recordUuid",
        fd_event_id as "eventId",
        fd_datetime as "dataTime",
        fd_post as "post",
        fd_person as "name",
        fd_content as "content",
        fd_annex as "annex",
        fd_extend1 as "identity",
        fd_extend2 as "headImg"
        from ${cmpProjectSchema}.t_em_handle_situation
        where
        fd_event_id = #eventId#
    </select>
    <!--查询事件快报弹窗内容-->
    <select id="queryHSToBulletin" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_datetime as "dataTime",
        fd_content as "content",
        fd_extend1 as "identity"
        from ${cmpProjectSchema}.t_em_handle_situation
        where
        fd_event_id = #eventId#
        order by fd_datetime
<!--        and fd_extend1 != 'NOCC'-->
    </select>
    <!--查询处置步骤表-->
    <select id="queryPlanStep" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_record_id as "recordUuid",
        fd_event_id as "eventId",
        fd_plan_name as "planName",
        fd_person as "post",
        fd_step as "content",
        fd_complete as "complete",
        fd_time_node as "node"
        from ${cmpProjectSchema}.t_em_handle_plan_step
        where
        fd_event_id = #eventId#
        order by fd_step_number asc
    </select>
    <!--查询处置步骤时间点-->
    <select id="queryPlanStepNode" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select
        fd_record_id as "recordId",
        fd_time_node as "timeNode"
        from ${cmpProjectSchema}.t_em_handle_plan_step
        where
        fd_event_id = #eventId#
        and fd_plan_name = #planName#
        order by fd_step_number
    </select>
    <!--查询库中是否存有某事件的处置步骤-->
    <select id="queryHasPlanStep" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_event_id as "eventId"
        from ${cmpProjectSchema}.t_em_handle_plan_step
        WHERE
        fd_event_id = #eventId#
        LIMIT 1
    </select>
    <!--查询事件为处置中状态事件-->
    <select id="queryEventStat" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        fd_disposal_t as "eventStat"
        from ${cmpProjectSchema}.t_event
        WHERE
        fd_uuid = #eventId# and
        fd_disposal_t = 30002
        LIMIT 1
    </select>
<!--    提供给信息发布下拉选项-->
    <select id="queryEventMss" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "eventId",
        fd_name as "eventName"
        from ${cmpProjectSchema}.t_event
        WHERE
        fd_disposal_t = 30002
    </select>
    <select id="queryYLmss" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        fd_uuid as "eventId",
        fd_drill_name as "eventName"
        from ${cmpProjectSchema}.t_drill
        WHERE
        fd_extend1 = 190003
    </select>

    <!--    查询应急事件事件等级-->
    <select id="querySjEventLevel" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        fd_resp_t  as "eventLevel"
        from ${cmpProjectSchema}.t_event
        WHERE
        fd_uuid = #eventId#
    </select>
    <select id="queryYlEventLevel" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        fd_resp_t  as "eventLevel"
        from ${cmpProjectSchema}.t_drill_info
        WHERE
        fd_uuid = #eventId#
    </select>

    <!--查询演练为处置中状态事件状态-->
    <select id="queryYLEventStat" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        fd_disposal_t as "eventStat"
        from ${cmpProjectSchema}.t_drill_info
        WHERE
        fd_uuid = #eventId# and
        fd_disposal_t = 190003
        LIMIT 1
    </select>
    <!--根据事件id读取选择预案-->
    <select id="queryPlanStepName" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        Select fd_plan_name as "planName"
        from ${cmpProjectSchema}.t_em_handle_plan_step
        where fd_event_id = #eventId#
        group by fd_plan_name
    </select>
    <!--提供信息服务的-事件信息-->
    <select id="queryXFFB" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        SELECT
        fd_msg_name as "msgName",
        fd_msg_stage as "msgStage",
        fd_msg_type as "msgType",
        fd_phone_notify as "phone",
        fd_emergency_notify as "emergency"
        <isNotEmpty prepend="" property="yjsj">
            from ${cmpProjectSchema}.t_event
        </isNotEmpty>
        <isNotEmpty prepend="" property="yjyl">
            from ${cmpProjectSchema}.t_drill_info
        </isNotEmpty>
        WHERE
        fd_uuid = #eventId#
        LIMIT 1
    </select>
    <!--提供信息服务的-事件信息-->
    <select id="queryXFFB2" parameterClass="java.util.HashMap" resultClass="java.util.HashMap" remapResults="true">
        SELECT
        *
        <isNotEmpty prepend="" property="yjsj">
            from ${cmpProjectSchema}.t_event
        </isNotEmpty>
        <isNotEmpty prepend="" property="yjyl">
            from ${cmpProjectSchema}.t_drill_info
        </isNotEmpty>
        WHERE
        fd_uuid = #eventId#
        LIMIT 1
    </select>
    <!--查询通讯录部门人员名称和工号-->
    <select id="queryPeopleInfo" resultClass="java.util.HashMap">
        select
        fd_name as "name"
        from ${mssProjectSchema}.t_mss_address_dept_person
        where 1 = 1
        <isNotEmpty prepend=" AND " property="numberIds">
            fd_number_id in
            <iterate open="(" close=")" conjunction="," property="numberIds" >
                #numberIds[]#
            </iterate>
        </isNotEmpty>
    </select>
    <!--查询通讯录群组-->
    <select id="queryAllMajor" resultClass="java.util.HashMap">
        select
        fd_name as "profession" <!--群组名称-->
        from ${mssProjectSchema}.t_mss_address_group
        where 1 = 1 and fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="fdNumbers">
            fd_number in
            <iterate open="(" close=")" conjunction="," property="fdNumbers" >
                #fdNumbers[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--向响应人员表中插入一条记录-->
    <insert id="insertResponseHRRecord" parameterClass="java.util.HashMap">
        INSERT
        INTO ${cmpProjectSchema}.t_em_handle_response_hr
        (fd_record_id,fd_event_id,fd_person_type,fd_user_id,fd_name,fd_post,fd_telephone,fd_notice_time,fd_respond_time,fd_arrival_time)
        VALUES
        (#recordUuid#,#eventId#,#type#,#userId#,#person#,#post#,#tel#,#noticeTime#,#respondTime#,#arrivalTime#)
    </insert>
    <!--向处置记录表中插入一条记录-->
    <insert id="insertHandleRecord" parameterClass="java.util.HashMap">
        INSERT
        INTO ${cmpProjectSchema}.t_em_handle_record
        (fd_record_id,fd_event_id,fd_record_type,fd_person,fd_datetime,fd_content)
        VALUES
        (#recordUuid#,#eventId#,#type#,#person#,#time#,#content#)
    </insert>
    <!--向指令记录表中插入一条记录-->
    <insert id="insertCommanderChange" parameterClass="java.util.HashMap">
        INSERT
        INTO ${cmpProjectSchema}.t_em_handle_instruct
        (fd_record_id,fd_event_id,fd_instruct_type,fd_datetime,fd_person,fd_content,fd_annex)
        VALUES
        (#recordUuid#,#eventId#,#type#,#datetime#,#person#,#content#,#annex#)
    </insert>
    <!--向现场处置情况记录表中插入一条记录-->
    <insert id="insertHandleSituation" parameterClass="java.util.HashMap">
        INSERT
        INTO ${cmpProjectSchema}.t_em_handle_situation
        (fd_record_id,fd_event_id,fd_datetime,fd_post,fd_person,fd_content,fd_annex,fd_extend1,fd_extend2)
        VALUES
        (#recordUuid#,#eventId#,#dataTime#,#post#,#name#,#content#,#annex#,#identity#,#headImg#)
    </insert>
    <!--向处置步骤表中插入一条记录-->
    <insert id="insertPlanStep" parameterClass="java.util.HashMap">
        INSERT
        INTO ${cmpProjectSchema}.t_em_handle_plan_step
        (fd_record_id,fd_event_id,fd_plan_name,fd_person,fd_step,fd_complete)
        VALUES
        (#recordUuid#,#eventId#,#planName#,#post#,#content#,#complete#)
    </insert>

    <!--向响应人员表中插入一吨记录-->
    <insert id="insertResponseHRRecordList" parameterClass="java.util.List">
        <iterate conjunction=";">
            INSERT
            INTO ${cmpProjectSchema}.t_em_handle_response_hr
            (fd_record_id,fd_event_id,fd_person_type,fd_user_id,fd_name,fd_post,fd_telephone,fd_notice_time,fd_respond_time,fd_arrival_time)
            VALUES
            (#[].recordUuid#,#[].eventId#,#[].type#,#[].userId#,#[].person#,#[].post#,#[].tel#,#[].noticeTime#,#[].respondTime#,#[].arrivalTime#)
        </iterate>
    </insert>
    <!--向处置步骤表中插入一吨记录-->
    <insert id="insertPlanStepList" parameterClass="java.util.List">
        <iterate conjunction=";">
            INSERT
            INTO ${cmpProjectSchema}.t_em_handle_plan_step
            (fd_record_id,fd_event_id,fd_plan_name,fd_person,fd_step,fd_complete,fd_time_node,fd_step_number)
            VALUES
            (#[].recordUuid#,#[].eventId#,#[].planName#,#[].post#,#[].content#,#[].complete#,#[].timeNode#,#[].stepNumber#)
        </iterate>
    </insert>
    <!-- 新增事件文件表 -->
    <insert id="insertFile" parameterClass="java.util.HashMap">
        INSERT INTO ${cmpProjectSchema}.t_event_file(
        fd_uuid  <!-- 应急事件uuid -->
        <isNotEmpty prepend="," property="fdAssessReport">
            fd_assess_report	<!-- 中心级总结报告路径 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdFirmReport">
            fd_firm_report  <!-- 公司级总结报告路径 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdDisposalRecords">
            fd_disposal_records  <!-- 处置记录路径 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            fd_created_by  <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            fd_created_time  <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            fd_update_by	<!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            fd_update_time	<!-- 更新时间 -->
        </isNotEmpty>
        )
        VALUES (#eventId#
        <isNotEmpty prepend="," property="fdAssessReport">
            #fdAssessReport#	<!-- 中心级总结报告路径 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdFirmReport">
            #fdFirmReport#  <!-- 公司级总结报告路径 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdDisposalRecords">
            #fdDisposalRecords#  <!-- 处置记录路径 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            #fdCreatedBy#  <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            #fdCreatedTime#  <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            #fdUpdateBy#	<!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            #fdUpdateTime#	<!-- 更新时间 -->
        </isNotEmpty>
        )
    </insert>


    <!--更新响应人员表中记录-->
    <update id="updateResponseHRRecord" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_em_handle_response_hr
        SET
        fd_notice_time = #noticeTime#,
        fd_respond_time = #respondTime#,
        fd_arrival_time = #arrivalTime#
        WHERE
        fd_record_id = #recordUuid#
    </update>
    <!--更新处置步骤表中记录-->
    <update id="updatePlanStep" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_em_handle_plan_step
        SET
        fd_complete = #complete#
        WHERE
        fd_record_id = #recordUuid#
    </update>


    <!--更新应急事件表中事件等级-->
    <update id="updateEventLevel" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_event
        SET
        fd_resp_t = #eventLevel#,
        fd_update_by = #updatePerson#,
        fd_update_time = #updateTime#
        WHERE
        fd_uuid = #eventId#
    </update>
    <!--更新应急事件表中事件状态（结束事件）-->
    <update id="updateEventStat" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_event
        SET
        fd_disposal_t = #eventStat#,
        fd_update_by = #updatePerson#,
        fd_update_time = #updateTime#
        WHERE
        fd_uuid = #eventId#
    </update>
    <!--更新应急演练表中事件状态（结束事件）-->
    <update id="updateDrillInfoState" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_drill_info
        set
        fd_disposal_t = #eventStat#
        <isNotEmpty prepend=" , " property="UpdateBy">
            fd_update_by = #UpdateBy#  <!-- 修改人 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="UpdateTime">
            fd_update_time = #UpdateTime#  <!-- 修改更新时间 -->
        </isNotEmpty>
        WHERE fd_uuid = #eventId#
    </update>
    <!--更新应急演练表中事件状态（结束事件）-->
    <update id="updateDrillState" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_drill
        set
        fd_extend1 = #eventStat#
        WHERE fd_uuid = #eventId#
    </update>
    <!--结束日志演练更-->
    <update id="updateDrillReport" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_drill
        set
        fd_disposal_report = #fdDisposalRecords#
        WHERE fd_uuid = #eventId#
    </update>
    <!-- 修改事件文件表 -->
    <update id="updateFile" parameterClass="java.util.HashMap">
        UPDATE ${cmpProjectSchema}.t_event_file
        set
        fd_disposal_records = #fdDisposalRecords#  <!-- 处置记录路径 -->
        WHERE fd_uuid = #eventId#
    </update>
    <!--向现场处置情况记录表中插入一条记录-->
    <delete id="delHandleSituation" parameterClass="java.util.HashMap">
        DELETE FROM ${cmpProjectSchema}.t_em_handle_situation WHERE fd_record_id=#recordUuid# and fd_event_id = #eventId#
    </delete>
</sqlMap>
