<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="YJCZ03">

	<!--<sql id="sql_query">
		<isNotEmpty prepend="and" property="fdName">
			e.fd_name like "%$fdName$%"
		</isNotEmpty>
		<isNotEmpty prepend="and" property="fdRespT">
			e.fd_resp_t = #fdRespT#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="fdTimeStart">
			e.fdTime <![CDATA[ >= ]]> #fdTimeStart#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="fdTimeEnd">
			date(e.fdTime) <![CDATA[ <= ]]> date(#fdTimeEnd#)
		</isNotEmpty>
		<isNotEmpty prepend="and" property="fdUuid">
			fd_uuid = #fdUuid#
		</isNotEmpty>
	</sql>

	<select id="query" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		Select
			e.fd_uuid as "fdUuid",
			e.fd_name as "fdName",
			e.fd_time as "fdTime",
			e.fd_plan as "fdPlan",
			e.fd_area_t as "fdAreaT",
			e.fd_line as "fdLine",
			e.fd_msg_name as "fdMsgName",
			e.fd_msg_stage as "fdMsgStage",
			e.fd_msg_type as "fdMsgType",
			e.fd_speciality_type as "fdSpecialityType",
			e.fd_resp_t as "fdRespT",
			e.fd_msg_desc as "fdMsgDesc",
			e.fd_disposal_t as "fdDisposalT",
			e.fd_phone_notify as "eventPhoneNotify",
			e.fd_emergency_notify as "eventEmergencyNotify",
			e.fd_operator as "fdOperator",
			e.fd_report_t as "fdReportT",
			st.fd_statioin_number as "fdStationNumber",
			se.fd_direction as "fdDirection",
			se.fd_start_station as "fdStartStation",
			se.fd_end_station as "fdEndStation",
			file.fd_assess_report as "fdAssessReport",
			file.fd_firm_report as "fdFirmReport",
			file.fd_disposal_records as "fdDisposalRecords"
		from ${cmpProjectSchema}.t_event e
		left join ${cmpProjectSchema}.t_event_station st on st.fd_event_uuid = e.fd_uuid
		left join ${cmpProjectSchema}.t_event_section se on se.fd_event_uuid = e.fd_uuid
		left join ${cmpProjectSchema}.t_event_file file on file.fd_uuid = e.fd_uuid
		where 1=1 and
		(e.fd_disposal_t = 30004 or (
		(e.fd_disposal_t = 30003 or e.fd_disposal_t = 30005) and (fd_report_t = 40801 or fd_report_t = 40901)
		))
		<include refid="sql_query"></include>
		<dynamic prepend="ORDER BY">
         <isNotEmpty property="orderBy">
    		  $orderBy$
   		 </isNotEmpty>
   		<isEmpty property="orderBy">
			e.fd_created_time desc
		</isEmpty>
  		</dynamic>
	</select>-->

	<!-- 查询事件历史表单表信息 -->
	<select id="queryEventHistory" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		Select
			f.fd_uuid as "fdUuid",
			f.fd_assess_report as "fdAssessReport",
			f.fd_firm_report as "fdFirmReport",
			f.fd_disposal_records as "fdDisposalRecords"
		from ${cmpProjectSchema}.t_event_file f
		where 1=1
		<isNotEmpty prepend="and" property="fdUuid">
			fd_uuid = #fdUuid#
		</isNotEmpty>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				f.fd_created_time desc
			</isEmpty>
		</dynamic>
	</select>

	<!-- 新增事件文件表 -->
	<insert id="insert">
		INSERT INTO ${cmpProjectSchema}.t_event_file(
		fd_uuid  <!-- 应急事件uuid -->
		<isNotEmpty prepend="," property="fdAssessReport">
			fd_assess_report	<!-- 中心级总结报告路径 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdFirmReport">
			fd_firm_report  <!-- 公司级总结报告路径 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdDisposalRecords">
			fd_disposal_records  <!-- 处置记录路径 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdCreatedBy">
			fd_created_by  <!-- 创建人 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdCreatedTime">
			fd_created_time  <!-- 创建时间 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdUpdateBy">
			fd_update_by	<!-- 更新人 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdUpdateTime">
			fd_update_time	<!-- 更新时间 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdExtend1">
			fd_extend1	<!-- 拓展字段1 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdExtend2">
			fd_extend2	<!-- 拓展字段2 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdExtend3">
			fd_extend3	<!-- 拓展字段3 -->
		</isNotEmpty>
		)
		VALUES (#fdUuid#
		<isNotEmpty prepend="," property="fdAssessReport">
			#fdAssessReport#	<!-- 中心级总结报告路径 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdFirmReport">
			#fdFirmReport#  <!-- 公司级总结报告路径 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdDisposalRecords">
			#fdDisposalRecords#  <!-- 处置记录路径 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdCreatedBy">
			#fdCreatedBy#  <!-- 创建人 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdCreatedTime">
			#fdCreatedTime#  <!-- 创建时间 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdUpdateBy">
			#fdUpdateBy#	<!-- 更新人 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdUpdateTime">
			#fdUpdateTime#	<!-- 更新时间 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdExtend1">
			#fdExtend1#	<!-- 拓展字段1 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdExtend2">
			#fdExtend2#	<!-- 拓展字段3 -->
		</isNotEmpty>
		<isNotEmpty prepend="," property="fdExtend3">
			#fdExtend3#	<!-- 拓展字段3 -->
		</isNotEmpty>
		)
	</insert>


	<!-- 修改事件文件表 -->
	<update id="update">
		UPDATE ${cmpProjectSchema}.t_event_file
		set
		fd_uuid = #fdUuid#
		<isNotEmpty prepend=" , " property="fdAssessReport">
			fd_assess_report = #fdAssessReport# <!-- 中心级总结报告路径 -->
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="fdFirmReport">
			fd_firm_report = #fdFirmReport#  <!-- 公司级总结报告路径 -->
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="fdDisposalRecords">
			fd_disposal_records = #fdDisposalRecords#  <!-- 处置记录路径 -->
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="fdUpdateBy">
			fd_update_by = #fdUpdateBy#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="fdUpdateTime">
			fd_update_time = #fdUpdateTime#
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="fdExtend1">
			fd_extend1 = #fdExtend1#	<!-- 拓展字段1 -->
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="fdExtend2">
			fd_extend2 = #fdExtend2#	<!-- 拓展字段2 -->
		</isNotEmpty>
		<isNotEmpty prepend=" , " property="fdExtend3">
			fd_extend3 = #fdExtend3#	<!-- 拓展字段3 -->
		</isNotEmpty>
		WHERE fd_uuid = #fdUuid#
	</update>

	<select id="queryPlanCode" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		Select
		ted.item_cname as "label",
		ted.item_code as "value"
		from ${platSchema}.tedcm01 ted
		where 1=1 and ted.codeset_code = 'nocc.yj.yg01'
		<isNotEmpty prepend="and" property="lineName">
			item_cname = #lineName#
		</isNotEmpty>
	</select>


</sqlMap>
