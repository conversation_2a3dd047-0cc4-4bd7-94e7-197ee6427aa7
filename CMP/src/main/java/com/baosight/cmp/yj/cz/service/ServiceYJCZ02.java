package com.baosight.cmp.yj.cz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baosight.cmp.common.PrefixPattern;
import com.baosight.cmp.yj.yl.service.ServiceYJYL01;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import lombok.val;
import redis.clients.jedis.*;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import static com.baosight.cmp.yj.cz.service.ServiceYJCZ01.updateEvents;
import static com.baosight.cmp.yj.cz.service.ServiceYJCZ01.changeIsRefsh;


public class ServiceYJCZ02 extends ServiceBase {
    private static Logger logger = LoggerFactory.getLogger(ServiceYJCZ02.class);
    //全局缓存
    private static Map<Object,Object> likelyRedis = new HashMap<>();
    //聊天缓存
    private static Map<String,List<Map>> msgRedis = new HashMap<>();
    //记录缓存
    private static Map<String,List<Map>> recordRedis = new HashMap<>();
    //人员缓存
    private static Map<String,List<Map>> userRedis = new HashMap<>();
    //服务器IP
    private static final String[] IPS = {"************","************"};//{"***********","***********"};

    //接受kafka调用的刷新次数
    private int kafkaNum = 0;
    private static JedisPool pool;
    private static Map<String,Map<String,Object>> kafkaCache = new HashMap<>();
    private static String redisHost = PlatApplicationContext.getProperty("spring.redis.host");
    static {initRedis();initKafkaCache();}

    public static void initRedis(){
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(100);
        config.setMaxIdle(50);
        config.setMinIdle(30);
        config.setTestOnBorrow(true);
        pool = new JedisPool(config, redisHost, 6379,1000, null, 1);
    }

    //预案处置闭环检查需检查职位类别
    private List<String> planStepCheckPost = new ArrayList<String>(){{
        add("主任调度");add("行车调度");add("设备调度");add("信息调度");
    }};

    //事件等级对照字典
    private Map<String,String> eventLevelEnumDict = new HashMap<String,String>(){{
        put("360001","一级");
        put("360002","二级");
        put("360003","三级");
        put("360004","四级");
        put("360005","无");
        put("一级","360001");
        put("二级","360002");
        put("三级","360003");
        put("四级","360004");
        put("无","360005");
    }};

    //主任调度账号列表
    private List<String> zrddList = new ArrayList<String>(){{
        add("zrdd");
    }};
    //设备调度账号列表
    private List<String> sbddList = new ArrayList<String>(){{
        add("sbdd");
    }};
    //行车调度账号列表
    private List<String> xcddList = new ArrayList<String>(){{
        add("xcdd");
    }};
    //信息调度账号列表
    private List<String> xxddList = new ArrayList<String>(){{
        add("xxdd");
    }};


    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    //生成uuid
    public String getUuid() {
        return UUID.randomUUID().toString().replace("-","");
    }

    //获取时间戳
    public String getTimeStamp() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return dateFormat.format(date);
    }

    public String getCurrentTime() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(date);
    }

    public String getTimePlus(int i) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, i); // add i hours to the calendar
        date = calendar.getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(date);
    }

    /**
     * HH:mm:ss格式时间转换为对应秒数
     * @param time String 数据列表
     */
    public int timeConvertToSecond (String time){
        String[] split = time.split(":");
        return Integer.parseInt(split[0])*3600+Integer.parseInt(split[1])*60+Integer.parseInt(split[2]);
    }

    /**
     * 分钟转秒
     * @param minute 分钟
     * @return 秒 int
     */
    public int minuteToSecond(String minute){
        return Integer.parseInt(minute)*60;
    }

    /**
     * 将账号信息转化为处置内部权限，并获取账号信息（暂定，视实际提供信息方式修改）
     * admin-系统管理员  zrdd-主任调度  sbdd-设备调度
     * xcdd-行车调度  xxdd-信息调度  none-无权限
     * 受权限影响操作及可操作权限：
     * 结束事件：admin，zrdd
     * 完成对应职位分页下处置步骤：admin（可完成全部职位），zrdd，sbdd，xcdd，xxdd
     * 发布应急处置情况信息：admin，zrdd，sbdd，xcdd，xxdd
     * 一键重播未响应人员电话：admin，zrdd，sbdd，xcdd，xxdd
     * @param inInfo EiInfo 数据集
     */
    public EiInfo getAuthority(EiInfo inInfo){
        String loginName = inInfo.getString("userName");
        if("admin".equals(loginName) || isEmit(loginName)){
            inInfo.set("userName","admin");
            inInfo.set("post","系统管理员");
            inInfo.set("user","NOCC");
        }else{
            List<String> par = new ArrayList<>();
            par.add(loginName);
            inInfo.set("workNumbers",par);
            inInfo.set(EiConstant.serviceId, "S_XF_XG_09");
            EiInfo outInfo = XServiceManager.call(inInfo);
            if (outInfo.getStatus() < 0) {
                logger.error("人员信息获取异常");
                inInfo.set("userName","admin");
                inInfo.set("post","系统管理员");
                inInfo.set("user","NOCC");
            }else{
                List<Map> data = (List) outInfo.getAttr().get("data");
                if(data.size()>0){
                    Map user = data.get(0);
                    inInfo.set("userName",loginName);
                    inInfo.set("post",user.get("post"));
                    inInfo.set("user",user.get("name"));
                }else{
                    inInfo.set("userName","admin");
                    inInfo.set("post","系统管理员");
                    inInfo.set("user","NOCC");
                }
            }
        }

//        if("admin".equals(userName)){
//            inInfo.set("userName","admin");
//            inInfo.set("post","系统管理员");
//            inInfo.set("user","系统管理员");
//        }
//        else if(zrddList.contains(userName)){
//            inInfo.set("userName","zrdd");
//            inInfo.set("post","主任调度");
//            inInfo.set("user","张三");
//        }
//        else if(sbddList.contains(userName)){
//            inInfo.set("userName","sbdd");
//            inInfo.set("post","设备调度");
//            inInfo.set("user","张三");
//        }
//        else if(xcddList.contains(userName)){
//            inInfo.set("userName","xcdd");
//            inInfo.set("post","行车调度");
//            inInfo.set("user","张三");
//        }
//        else if(xxddList.contains(userName)){
//            inInfo.set("userName","xxdd");
//            inInfo.set("post","信息调度");
//            inInfo.set("user","张三");
//        }
//        else {
//            inInfo.set("userName","none");
//            inInfo.set("post","无");
//            inInfo.set("user","无");
//        }

        return inInfo;
    }


    /**
     * 缓存中获取事件信息返回，若是缓存中无此事件，视为新事件进入处置，执行初始化方法，后读取数据返回
     * @param inInfo EiInfo 数据集
     * EiInfo中所需数据：
     * eventId：事件ID，标识一个应急事件的唯一主键
     */
    public EiInfo getEvent(EiInfo inInfo){
        String eventId = inInfo.getString("eventId");
        String user = inInfo.getString("user");
        String post = inInfo.getString("post");
        Map eventInfo = queryEventFromRedis(eventId);
        //缓存中无数据
        if(eventInfo == null){
            List<Map> queryStat = new ArrayList<>();
            if(Integer.parseInt(inInfo.getString("eventSource"))==1){
                queryStat = dao.query("YJCZ02.queryEventStat", inInfo.getAttr());
            }else{
                queryStat = dao.query("YJCZ02.queryYLEventStat", inInfo.getAttr());
            }
            if(queryStat.size() > 0){
                //数据库中有数据 事件状态为处置中，则认为缓存数据丢失，重新读取数据写入缓存(为了和已完成事件做区分)
                rereadEventInfo(inInfo);
            }else {
                //数据库中无数据，认定为新事件，执行初始化操作
                initEventBus(inInfo);
            }
            //初始化现场处置情况
            initHandleSituationToRedis(eventId);
            //初始化处置记录
            initHandleRecord(eventId);
            eventInfo = queryEventFromRedis(eventId);
            Object eventRecords = eventInfo.get("eventRecords");
            Object eventDisc = eventInfo.get("eventDisc");

            CompletableFuture.supplyAsync(() -> {
                try {
                    initAllResponList(eventId);
                    List<Map> lUserFromRedis = getLUserFromRedis(eventId);
                    addEventStateToRedis(eventId,"1");
                    if(lUserFromRedis.size()>0){
                        initNotices(eventId,lUserFromRedis);
                    }
                    if(getInfoHisteryRedis(eventId)){
                        addInfoHisterToRedis(eventId);
                        if(eventRecords!=null){
                            initInfoHistery(eventId,eventRecords);
                        }
                    }

                    if(!isAddEventDescToRedis(eventId)){
                        EiInfo info = new EiInfo();
                        info.set("eventId", eventId);
                        info.set("post", post);
                        info.set("name", user);
                        info.set("content", eventDisc);
                        info.set("annex", "[]");
                        info.set("XFFB", "1");
                        info.set("eventSource", inInfo.getString("eventSource"));
                        addHandleSituation(info);
                    }

                } catch (Exception e) {
                    Thread.currentThread().interrupt();
                }
                return "";
            });
        }
        inInfo.set("event",eventInfo);
        refshKafka();
        return inInfo;
    }

    /**
     * 缓存中数据丢失，重新读取数据并存入缓存
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo rereadEventInfo(EiInfo inInfo){
        Map<Object, Object> eventInfo = initEventInfo(inInfo);
        if(mapIsEmpty(eventInfo)){
            return inInfo;
        }
        //List query = dao.query("YJCZ02.queryHasPlanStep", inInfo.getAttr());
        List<Map> maps  = queryPlanStepFromDatabase(inInfo.getString("eventId"));
        //if(query.size()<=0){
        initResponseHR(eventInfo);
        //}
        if(maps.size()<=0){
            initPlanStep(eventInfo);
        }
        return inInfo;
    }

    /**
     * 新事件进入处置，初始化其内容
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo initEventBus(EiInfo inInfo){
        //获取事件信息,若返回为空,则可能是事件源处无此事件或事件状态为已结束
        Map<Object, Object> eventInfo = initEventInfo(inInfo);
        if(mapIsEmpty(eventInfo)){
            return inInfo;
        }
        //初始话读取响应人员信息
        initResponseHR(eventInfo);
        initPlanStep(eventInfo);
        return inInfo;
    }

    /**
     * 事件结束处置
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo endEventBus(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        int eventSource = Integer.parseInt(attr.get("eventSource").toString());
        //发送状态到 事件-演练
        String userName = "admin";
        if(!isEmit(inInfo.getAttr().get("userName"))){
            userName = inInfo.getAttr().get("userName").toString();
        }
        String filePath = changeEventStat(eventId,eventSource,userName);
        inInfo.set("filePath",filePath);
        //发送状态到智能应急调度
        if(attr.get("isDd") == null){
            sendEventEndToAPP(inInfo);
        }
        removeEventCach(eventId);
        pushMessage("eventEnd",eventId);
        return inInfo;
    }

    //事件任务批量结束
    public EiInfo timeEnds(EiInfo info){
        String eventName = "";
        try {
            Map<String,String> map = new HashMap<>();
            List<Map> query = dao.query("YJCZ02.queryEventMss", map);
            for(Map m:query){
                eventName = info.getString("eventName");
                info.set("eventId",m.get("eventId"));
                info.set("eventSource","1");
                info.set("userName","admin");
                endEventBus(info);
                Thread.sleep(5);
            }
        }catch (Exception e){
            String msg = getCurrentTime()+eventName+"事件自动结束失败";
            logger.error(msg);
        }
        return info;
    }

    /**
     * 根据事件id读取所选预案
     * @param eventId 事件id
     * @return 所选预案
     */
    public String queryPlanName(String eventId){
        StringBuilder planName = new StringBuilder();
        Map<String,String> parMap = new HashMap<>();
        parMap.put("eventId",eventId);
        List<Map<String,Object>> query = dao.query("YJCZ02.queryPlanStepName", parMap);
        //存在预案，以‘，’隔开
        for(int i=0;i<query.size();i++){
            String pn = query.get(i).get("planName").toString();
            planName.append(pn);
            if(i != query.size()-1){
                planName.append(",");
            }
        }
        return planName.toString();
    }

    /**
     * 根据事件id读取响应人员信息，根据消息通知过滤、电话通知过滤、人员签到信息过滤
     * @param eventId 事件id
     */
    public String[] queyHandleName(String eventId){
        Map<String,String> parMap = new HashMap<>();
        parMap.put("eventId",eventId);
        List<Map<String,Object>> query = dao.query("YJCZ02.queryResponseHRName", parMap);

        StringBuilder phone = new StringBuilder();//电话通知人员
        StringBuilder mess = new StringBuilder();//消息通知
        StringBuilder sign = new StringBuilder();//人员签到
        //存在人员
        Set<String> phoneSet = new HashSet<>();
        Set<String> messSet = new HashSet<>();
        Set<String> signSet = new HashSet<>();
        for(int i=0;i<query.size();i++){
            Map<String, Object> mp = query.get(i);
            //人员类型 60001-电话通知
            String type = mp.get("type").toString();
            String person = mp.get("person").toString();
            String userId = mp.get("userId").toString();
            if("60001".equals(type)){
                if(!phoneSet.contains(userId)){
                    phoneSet.add(userId);
                    String noticeTime = mp.get("noticeTime").toString();
                    if(noticeTime.length()>0){
                        phone.append(person).append(",");
                    }
                }
            } else {
                String respondTime = mp.get("respondTime").toString();
                String arrivalTime = mp.get("arrivalTime").toString();
                if(!messSet.contains(userId)){
                    messSet.add(userId);
                    if(respondTime.length()>0){
                        mess.append(person).append(",");
                    }
                }
                if(!signSet.contains(userId)){
                    signSet.add(userId);
                    if(arrivalTime.length()>0){
                        sign.append(person).append(",");
                    }
                }
            }
        }
        String phoneStr = phone.toString();
        String messStr = mess.toString();
        String signStr = sign.toString();
        if(phoneStr.length() > 0) {
            phoneStr = phoneStr.substring(0,phoneStr.length()-1);
        }
        if(messStr.length() > 0) {
            messStr = messStr.substring(0,messStr.length()-1);
        }
        if(signStr.length() > 0) {
            signStr = signStr.substring(0,signStr.length()-1);
        }
        return new String[]{messStr,phoneStr,signStr};
    }

    /**
     * 日志生成 现场处置情况生成、处置记录生成
     * @param eventId 事件Id
     * @return 返回数据集
     */
    public Map<String,Object> disposalSituation(String eventId){
        //现场处置情况
        List<Map> handleSituationList = queryHandleSituationFromDatabase(eventId);
        List<Map<String,Object>> allList = new ArrayList<>();//现场处置记录
        List<Map<String,Object>> zhzList = new ArrayList<>();//现场指挥长发布记录
        List<Map<String,Object>> fzrList = new ArrayList<>();//抢险负责人发布记录
        List<Map<String,Object>> occList = new ArrayList<>();//OCC发布记录
        List<Map<String,Object>> noccList = new ArrayList<>();//Nocc发布记录
        //数据各就各位
        for (Map map : handleSituationList) {
            Map<String,Object> cmp = new HashMap<>();
            String dataTime = map.get("dataTime").toString().replaceAll("-", "");
            cmp.put("time",dataTime);
            cmp.put("name",map.get("name"));
            cmp.put("dispose",map.get("content"));
            Object annex = map.get("annex");
            if(annex==null || annex=="[]" || annex == ""){
            }else{
                List<Map> mapList = JSONArray.parseArray(annex.toString(), Map.class);
                List<byte[]> list = new ArrayList<>();
                for(Map m:mapList){
                    Object tyep = m.get("type");
                    if("img".equals(tyep.toString())){
                        byte[] bytes = getFileByOSS(m.get("bucketName").toString(), m.get("fileName").toString());
                        list.add(bytes);
                    }
                }
                cmp.put("org",list);
            }

            String identity = map.get("identity").toString();
            if("现场指挥长".equals(identity)){
                zhzList.add(cmp);
            }else if("应急人员".equals(identity) || "抢险负责人".equals(identity)){
                fzrList.add(cmp);
            }else if("NOCC".equals(identity)){
                noccList.add(cmp);
            }else if(identity.contains("OCC")){
                occList.add(cmp);
            }
            allList.add(cmp);
        }
        //现场处置记录
        List<Map> disposalRecords = getHandleRecordFromRedis(eventId);
        List<Map<String,Object>> occList2 = new ArrayList<>();//OCC发布记录
        List<Map<String,Object>> noccList2 = new ArrayList<>();//Nocc发布记录
        for (Map map : disposalRecords) {
            Map<String,Object> cmp = new HashMap<>();
            String dataTime = map.get("time").toString().replaceAll("-", "");
            cmp.put("time",dataTime);
            cmp.put("name",map.get("person"));
            cmp.put("dispose",map.get("content"));
            Object annex = map.get("annex");
            if(annex==null || annex=="[]" || annex == ""){
            }else{
                List<Map> mapList = JSONArray.parseArray(annex.toString(), Map.class);
                List<byte[]> list = new ArrayList<>();
                for(Map m:mapList){
                    if("img".equals(m.get("type").toString())){
                        byte[] bytes = getFileByOSS(m.get("bucketName").toString(), m.get("fileName").toString());
                        list.add(bytes);
                    }
                }
                cmp.put("org",list);
            }

            if("70001".equals(map.get("type").toString())){
                noccList2.add(cmp);
            }else{
                occList2.add(cmp);
            }

        }
        return new HashMap<String,Object>(){{
            put("sceneDispose",allList);
            put("commanderRelease",zhzList);
            put("dutyRelease",fzrList);
            put("occRelease",occList);
            put("noccRelease",noccList);
            put("occDispose",occList2);
            put("noccDispose",noccList2);
        }};
    }

    //读取专业、群组、人 中文信息
    public String readMPinfo(Object param){
        try {
            String json = "";
            if(param != null){
                json = param.toString();
            }else{
                return "";
            }
            Map<String, List> infoRecords = JSON.parseObject(json, new TypeReference<Map<String, List>>(){});
            List group = infoRecords.get("group");
            List dept = infoRecords.get("dept");
            List deptPerson = infoRecords.get("deptPerson");

            List<Map<String,Object>> groupList = new ArrayList<>();
            List<Map<String,Object>> deptList = new ArrayList<>();
            List<Map<String,Object>> deptPersonList = new ArrayList<>();
            EiInfo info = new EiInfo();
            if(group.size()>0){
                try {
                    info.set("fdNumbers",group);
                    //查询根据编号查询部门名称
                    //EiInfo fileInfo = callXService(info, "S_XF_XG_11");
                    groupList = dao.query("YJCZ02.queryAllMajor", info.getAttr());
                }catch (Exception e){

                }
            }
            if(dept.size()>0){
                try {
                    info.set("fdNumbers",dept);
                    //查询根据编号查询部门名称
                    EiInfo fileInfo = callXService(info, "S_XF_XG_12");
                    deptList = (List<Map<String, Object>>) fileInfo.get("data");
                }catch (Exception e){

                }
            }
            if(deptPerson.size()>0){
                try {
                    info.set("numberIds",deptPerson);
                    deptPersonList = dao.query("YJCZ02.queryPeopleInfo", info.getAttr());
                }catch (Exception e){

                }
            }
            StringBuilder sb = new StringBuilder();
            for(Map<String,Object> map : groupList){
                sb.append(map.get("profession")).append(",");
            }
            for(Map<String,Object> map : deptList){
                sb.append(map.get("name")).append(",");
            }
            for(Map<String,Object> map : deptPersonList){
                sb.append(map.get("name")).append(",");
            }
            if(sb.length()>0){
                sb.deleteCharAt(sb.length()-1);
            }

            return sb.toString();
        }catch (Exception e){

        }
        return "";
    }

    /**
     * 处置日志的生成
     */
    public Map<String, Object> disposalLog(String eventId,String eventSource){

        Map<String,Object> redisMap = queryEventFromRedis(eventId);
        EiInfo info = new EiInfo();
        info.set("eventId",eventId);
        String eventName = redisMap.get("eventName").toString();//事件名称
        String eventTime = redisMap.get("eventTime").toString();//事发时间
        String eventPlace = redisMap.get("eventPlace").toString();//事发地点
        String eventDisc = redisMap.get("eventDisc").toString();//事发详情
        String responseHRInfo = readMPinfo(redisMap.get("responseHRInfo"));//消息通知初始信息
        String noticeHRInfo =readMPinfo(redisMap.get("noticeHRInfo"));//电话通知初始信息

        String endTime = getCurrentTime();//结束时间
        String eventLevel = eventLevelEnumDict.get( redisMap.get("eventLevel").toString());//事件等级
        String planName = queryPlanName(eventId);//选用的预案
        //事件来源-指挥长
        String commanderStr = getCommander(info).getString("commander");
        if(commanderStr.length()>0){
            String[] commander = commanderStr.split(" ");//指挥长信息
            if(commander.length>1){
                commanderStr = commander[0];//指挥长
            }
        }
        //负责人
        String directorStr = getDirector(info).getString("director");
        if(directorStr.length()>0){
            String[] director = directorStr.split(" ");//指挥长信息
            if(director.length>1){
                directorStr = director[0];//负责人
            }
        }

        String[] handles = queyHandleName(eventId);

        Map<String, Object> resultMap = disposalSituation(eventId);
        resultMap.put("eventName",eventName);//事件名称
        resultMap.put("startTime",eventTime);//开始时间
        resultMap.put("endTime",endTime);//结束时间
        resultMap.put("plan",planName);//选用预案
        resultMap.put("commander",commanderStr);//现场指挥长
        resultMap.put("place",eventPlace);//事件地点
        resultMap.put("level",eventLevel);//事件等级
        resultMap.put("source",eventSource);//来源
        resultMap.put("dutyName",directorStr);//负责人
        resultMap.put("details",eventDisc);//详情
        resultMap.put("msgNotices",responseHRInfo);//详情
        resultMap.put("cellNotices",noticeHRInfo);//详情
        resultMap.put("msgNotice",handles[0]);//消息通知
        resultMap.put("cellNotice",handles[1]);//电话通知
        resultMap.put("sighName",handles[2]);//人员签到

        return resultMap;
    }

    public void disposalLog(String eventId,Map<String, Object> resultMap,boolean issj,String userName){
        List<Map<String, Object>> backList = new ArrayList<>();
        backList.add(resultMap);
        EiInfo info = new EiInfo();
        info.set("command","export_log");
        info.set("data",backList);
        EiInfo fileInfo = callXService(info, "S_FS_98");

        String fileName = resultMap.get("eventName")+"-处置日志"+".docx";
        String fileNameOSS = getTimeStamp()+".docx";

        writeFileToOSS(fileInfo.get("logByte").toString(),fileNameOSS,"CMP");

        Map<String,String> jsonMap = new HashMap<>();
        jsonMap.put("fileNameCH",fileName);
        jsonMap.put("bucketName","CMP");
        jsonMap.put("fileName",fileNameOSS);
        jsonMap.put("eventId",eventId);
        String s = JSON.toJSONString(jsonMap);
        jsonMap.put("fdDisposalRecords",s);

        if(issj){
            dao.update("YJCZ02.updateFile",jsonMap);
        }else{
            jsonMap.put("fdCreatedBy",userName);
            jsonMap.put("fdCreatedTime",resultMap.get("endTime").toString());
            dao.insert("YJCZ02.updateDrillReport",jsonMap);
        }
    }

    /**
     * 改变事件状态
     */
    public String changeEventStat(String eventId,int eventSource,String userName){
        String filePath = "";//处置日志
        HashMap<String, Object> map = new HashMap<>();
        //修改应急事件
        if(eventSource == 1){
            map.put("fdUuid",eventId);
            map.put("fdDisposalT",30004);
            map.put("fdUpdateBy",userName);
            map.put("fdUpdateTime",getCurrentTime());
            map.put("needPushCZ02","false");
            List<Map<String,Object>> inputList = new ArrayList<>();
            inputList.add(map);
            //处置日志的生成
            Map<String, Object> resultMap = disposalLog(eventId, "应急事件");
            EiInfo eiInfo = updateEvents(inputList);
            if(eiInfo.getStatus()==-1){
                throw new PlatException("应急事件结束失败！");
            }else{
                //应急事件状态修改成功-生成处置日志
                disposalLog(eventId,resultMap,true,userName);
            }

        }else {
            //修改应急演练
            map.put("event_uuid",eventId);
            map.put("event_status",190007);
            map.put("from",1);
            map.put("UpdateBy",userName);
            map.put("UpdateTime",getCurrentTime());
            Map<String, Object> resultMap = disposalLog(eventId, "应急演练");
            EiInfo info = ServiceYJYL01.getYLStatusInfo(map);
            if(info.getStatus()==-1){
                throw new PlatException("演练计划结束失败！");
            }else{
                //演练计划状态修改成功-生成处置日志
                disposalLog(eventId,resultMap,false,userName);
            }
        }
        return filePath;
    }

    //容错电话、消息数据库为空
    public Object delEmet(Object o){
        if(o==null || "".equals(o.toString())){
            return "{\"deptPerson\":[],\"dept\":[],\"text\":[],\"group\":[]}";
        }
        return o;
    }

    /**
     * 初始化事件信息，仅为事件初始方法下子方法
     * @param inInfo EiInfo 数据列表
     */
    public Map<Object, Object> initEventInfo(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        int eventSource = Integer.parseInt(attr.get("eventSource").toString());
        //调用接口获取应急事件详细信息,接口由应急事件、应急演练提供
        Map map = queryEventInfoFromSource(inInfo);
        if(map.isEmpty()){
            return new HashMap<>();
        }

        String fdPlan = "";
        String eventPlanJson = "";
        //添加标识，并将字段名统一转换（应急事件来源及应急演练来源字段名大概率不完全相同）
        HashMap<Object, Object> eventInfo = new HashMap<>();
        if(eventSource == 1){
            //应急事件返回数据字段名转为内部字段名
            eventPlanJson = nullToStr(map.get("fdPlan"));
            eventInfo.put("eventId",map.get("fdUuid"));//事件ID
            eventInfo.put("eventName",map.get("fdName"));//事件名称
            eventInfo.put("eventTime",map.get("fdTime"));//事件发生时间
            eventInfo.put("lineId",map.get("fdLine"));//事件线路编号
            eventInfo.put("eventLevel",map.get("fdRespT"));//事件等级
            eventInfo.put("eventPlace",map.get("fdPlace"));//事件发生地点
            eventInfo.put("eventDisc",map.get("fdMsgDesc"));//事件详细描述
            eventInfo.put("planName",nullToStr(map.get("fdPlanName")));//预案名称
            eventInfo.put("lineName",map.get("lineName"));//线路名称
            eventInfo.put("noticeHRInfo",delEmet(map.get("eventPhoneNotify")));//电话通知人员信息JSON
            eventInfo.put("responseHRInfo",delEmet(map.get("eventEmergencyNotify")));//响应人员信息JSON
            eventInfo.put("eventRecords",map.get("eventRecords"));//信息发布历史
        }else {
            //应急演练返回数据字段名转为内部字段名
            eventPlanJson = map.get("plan").toString();
            eventInfo.put("eventId",map.get("uuid"));//演练ID
            eventInfo.put("eventName",map.get("name"));//演练名称
            eventInfo.put("eventTime",map.get("time"));//演练时间
            eventInfo.put("eventLevel",map.get("level"));//事件等级
            eventInfo.put("eventPlace",map.get("fdPlace"));//事件发生地点
            eventInfo.put("eventDisc",map.get("msgDesc"));//事件详细描述
            eventInfo.put("planName",map.get("fdPlanName"));//预案名称
            eventInfo.put("lineName",map.get("lineName"));//线路名称
            eventInfo.put("noticeHRInfo",delEmet(map.get("phone")));//电话通知人员信息JSON
            eventInfo.put("responseHRInfo",delEmet(map.get("emergency")));//响应人员信息JSON、
            eventInfo.put("eventRecords",map.get("eventRecords"));//信息发布历史
        }
        if (!"".equals(eventPlanJson)){
            Map<String,List> planMap = JSONObject.parseObject(eventPlanJson, Map.class);
            List<String> planIdsList =  (List<String>)planMap.get("planIdsList");
            fdPlan = planIdsList.stream().collect(Collectors.joining(","));
        }

        eventInfo.put("planId",fdPlan);//预案ID
        eventInfo.put("eventSource",eventSource);
        eventInfo.put("commander","");
        eventInfo.put("commanderTel","");
        eventInfo.put("director","");
        eventInfo.put("directorTel","");
        //事件信息写入缓存
        insertEventToRedis(inInfo.getString("eventId"),eventInfo);

        return eventInfo;
    }

    /**
     * 从事件来源查询事件信息   00-应急事件  01-应急演练
     * @param inInfo EiInfo 数据列表
     *
     */
    public Map queryEventInfoFromSource(EiInfo inInfo){
        if(Integer.parseInt(inInfo.getAttr().get("eventSource").toString()) == 1){
            inInfo.set(EiConstant.serviceName, "YJCZ01");
            inInfo.set(EiConstant.methodName, "queryEventDates");
            EiInfo outInfo = XLocalManager.call(inInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            EiBlock result = outInfo.getBlock("result");
            List<Map> rows = result.getRows();
            if(rows.size() > 0){
                return rows.get(0);
            }
        }else {
            inInfo.set(EiConstant.serviceName, "YJYL01");
            inInfo.set(EiConstant.methodName, "queryEventDates");
            EiInfo outInfo = XLocalManager.call(inInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            EiBlock result = outInfo.getBlock("result");
            List<Map> rows = result.getRows();
            if(rows.size() > 0){
                return rows.get(0);
            }

        }
        return new HashMap<>();
    }

    public List<Map> queryMsg(String eventId,Map eventInfo){
        List<Map> responseHRList = new ArrayList<>();
        try{
            Map<Object, Object> noticeHRInfo = new HashMap<>();
            Map<Object, Object> responseHRInfo = new HashMap<>();
            //获取人员名单
            HashMap<Object, Object> map = new HashMap<>();

            Object noticeStr = eventInfo.get("noticeHRInfo");
            Object responseStr = eventInfo.get("responseHRInfo");
            if(!isEmit(noticeStr)){
                noticeHRInfo = JSON.parseObject(eventInfo.get("noticeHRInfo").toString(),Map.class);
            }else{
                noticeHRInfo.put("deptPerson",new ArrayList<String>());
                noticeHRInfo.put("dept",new ArrayList<String>());
                noticeHRInfo.put("text",new ArrayList<String>());
                noticeHRInfo.put("group",new ArrayList<String>());
            }
            if(!isEmit(responseStr)){
                responseHRInfo = JSON.parseObject(eventInfo.get("responseHRInfo").toString(),Map.class);
            }else{
                responseHRInfo.put("deptPerson",new ArrayList<String>());
                responseHRInfo.put("dept",new ArrayList<String>());
                responseHRInfo.put("text",new ArrayList<String>());
                responseHRInfo.put("group",new ArrayList<String>());
            }

            map.put("phoneAddressValue",noticeHRInfo);
            map.put("msgAddressValue",responseHRInfo);

            EiInfo eiInfo = new EiInfo();
            eiInfo.set("data",map);
            eiInfo.set(EiConstant.serviceId, "S_XF_XG_08");//S_XF_XG_08 根据部门编号或专业编号查询人员工号和人员id
    //        eiInfo.set(EiConstant.serviceId, "S_YJ_TEST_01");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            Map data = new HashMap<>();
            Map phonePersonNoList = new HashMap<>();
            Map msgPersonNoList  = new HashMap<>();
            if (outInfo.getStatus() < 0) {
                logger.error(outInfo.getMsg());
            }else{
                data=(Map) outInfo.getAttr().get("data");
                phonePersonNoList =(Map) data.get("phonePersonList");
                msgPersonNoList =(Map) data.get("msgPersonList");
            }

            List<Map> phonePersonList = new ArrayList<>();
            List<Map> msgPersonList = new ArrayList<>();

            if(phonePersonNoList.get("ids")!=null){
                try{
                    EiInfo phoneEiInfo = new EiInfo();
                    phoneEiInfo.set("dingNumbers",phonePersonNoList.get("ids"));
                    phoneEiInfo.set(EiConstant.serviceId, "S_XF_XG_09");//根据人员工号获取人员信息
                    //        phoneEiInfo.set(EiConstant.serviceId, "S_YJ_TEST_02");
                    EiInfo phoneOutInfo = XServiceManager.call(phoneEiInfo);
                    if (phoneOutInfo.getStatus() < 0) {
                        logger.error(phoneOutInfo.getMsg());
                    }else{
                        phonePersonList = (List) phoneOutInfo.getAttr().get("data");
                    }
                }catch (Exception e){
                    phonePersonList = new ArrayList<>();
                }
            }

            if(msgPersonNoList.get("ids")!=null){
                try{
                    EiInfo msgEiInfo = new EiInfo();
                    msgEiInfo.set("dingNumbers",msgPersonNoList.get("ids"));
                    msgEiInfo.set(EiConstant.serviceId, "S_XF_XG_09");
                    //        msgEiInfo.set(EiConstant.serviceId, "S_YJ_TEST_02");
                    EiInfo msgOutInfo = XServiceManager.call(msgEiInfo);
                    if (msgOutInfo.getStatus() < 0) {
                        logger.error(msgOutInfo.getMsg());
                    }else{
                        msgPersonList = (List) msgOutInfo.getAttr().get("data");
                    }
                }catch (Exception e){
                    msgPersonList = new ArrayList<>();
                }
            }

            Set<Object> phoneSet = new HashSet<>();
            for (Map item : phonePersonList) {
                if (!phoneSet.contains(item.get("id"))) {
                    phoneSet.add(item.get("id"));
                    HashMap<Object, Object> tempMap = personMap(item, eventId, 60001);
                    responseHRList.add(tempMap);
                }
            }

            Set<Object> msgSet = new HashSet<>();
            for (Map value : msgPersonList) {
                if (!msgSet.contains(value.get("id"))) {
                    msgSet.add(value.get("id"));
                    HashMap<Object, Object> tempMap = personMap(value, eventId, 60002);
                    responseHRList.add(tempMap);
                }
            }
        }catch (Exception e){
            responseHRList = new ArrayList<>();
        }
        return responseHRList;
    }

    //消息、电话通知人员格式化
    public HashMap<Object, Object> personMap(Map map,String eventId,Integer type){
        HashMap<Object, Object> tempMap = new HashMap<>();
        tempMap.put("eventId",eventId);
        tempMap.put("recordUuid",getUuid());
        tempMap.put("userId",map.get("id"));
        tempMap.put("post",getStrValue(map.get("post")));
        tempMap.put("person",map.get("name"));
        tempMap.put("tel",map.get("phone"));
        tempMap.put("type",type);
        tempMap.put("noticeTime","");
        tempMap.put("respondTime","");
        tempMap.put("arrivalTime","");
        return tempMap;
    }

    /**
     * 初始化响应人员列表，仅为事件初始方法下子方法
     * @param eventInfo Map 数据列表
     */
    public void initResponseHR(Map eventInfo){
        String eventId = eventInfo.get("eventId").toString();
        List<Map> responseHRList = queryMsg(eventId,eventInfo);
        if(responseHRList.size()>0){
            insertResponseHRRecordToDatabase(responseHRList);
        }
    }

    /**
     * 初始化处置步骤列表，仅为事件初始方法下子方法
     * @param attr Map<Object, Object> 数据列表
     */
    public void initPlanStep(Map<Object, Object> attr){
        String eventId = attr.get("eventId").toString();
        Object planId = attr.get("planId");
        if(planId != null){
            //获取处置预案内容,现用临时预案代替
            EiInfo phoneEiInfo = new EiInfo();
            phoneEiInfo.set("planUuids",planId);
            //获取多预案及多步骤  -- 原接口"S_YJ_YGCX_02"
            phoneEiInfo.set(EiConstant.serviceId, "S_YJ_YGCX_03");
            EiInfo planInfo = XServiceManager.call(phoneEiInfo);
            List<Map> planList = (List)planInfo.get("planStep");
            List<Map> planStepList = new ArrayList<>();
            for(Map map:planList){
                HashMap<Object, Object> tempMap = new HashMap<>();
                tempMap.put("eventId",eventId);
                tempMap.put("recordUuid",getUuid());
                tempMap.put("planName",map.get("planName"));
                tempMap.put("post",map.get("job"));
                tempMap.put("content",nullToStr(map.get("desc")));
                tempMap.put("timeNode",toInt(map.get("timeNode")));
                tempMap.put("stepNumber",toInt(map.get("stepNumber")));
                tempMap.put("complete",0);
                planStepList.add(tempMap);
            }
            insertPlanStepsToDatabase(planStepList);
        }
    }

    /* Convert to Integer type */
    public Integer toInt(Object o) {
        if (o == null || o.toString().isEmpty()) {
            return 0;
        }
        return Integer.valueOf(o.toString());
    }

    /* 返回空字符串 */
    public String nullToStr(Object o){
        return o == null? "":o.toString();
    }

    /**
     * 完成预案中步骤，并新增相应处置记录
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo completePlanStep(EiInfo inInfo){
        String eventId = inInfo.getAttr().get("eventId").toString();
        String recordId = inInfo.getAttr().get("recordId").toString();

        List<Map> planStep = queryPlanStepFromDatabase(eventId);

        for (int i = 0; i < planStep.size(); i++) {
            if(planStep.get(i).get("recordUuid").equals(recordId)){
                planStep.get(i).put("complete",1);
                updatePlanStepToDatabase(planStep.get(i));
                addHandleRecord(eventId,planStep.get(i).get("content").toString(),70001,planStep.get(i).get("post").toString());
                break;
            }
        }

        return inInfo;
    }

    /**
     * 新增处置记录
     */
    public int addHandleRecord(String eventId,String content,int type,String person){
        String recordUuid = getUuid();
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        map.put("recordUuid",recordUuid);
        map.put("type",type);
        map.put("person",person);
        map.put("time",getCurrentTime());
        map.put("content",content);
        insertHandleRecordToDatabase(map);
        addHandleRecordoRedis(eventId,map);
        pushMessage("hasHandleRecordUpdate",eventId);
        return 0;
    }

    public EiInfo handleSituBefore(EiInfo info){
        info.get("");//queryEventFromRedis
        return addHandleSituation(info);
    }

    //判断是否将事件描述同步到现场处置情况
    public synchronized boolean isAddEventDescToRedis(String eventId){
        String desc = getValRedis(PrefixPattern.DPATTERN.getPattern() + eventId);
        if("c".equals(desc)){
            return true;
        }else{
            saveToRedis(PrefixPattern.DPATTERN.getPattern() + eventId,"c");
        }
        return false;
    }

    /**
     * 新增现场处置情况记录 (S_YJ_CZ_19)
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo addHandleSituation(EiInfo inInfo){
        //读取数据
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        String name = attr.get("name").toString();
        String post = getStrValue(attr.get("post"));
        String content = attr.get("content").toString();
        String annex = attr.get("annex").toString();

        String currentTime = getCurrentTime();
        String recordUuid = getUuid();
        //构造缓存数据
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        map.put("recordUuid",recordUuid);
        map.put("name",name);
        map.put("post",post);
        map.put("identity","NOCC");
        map.put("dataTime",currentTime);
        map.put("content",content);
        map.put("source","NOCC");
        map.put("annex",annex);
        map.put("headImg","");
        //记录写入缓存
        addHandleSituationToRedis(eventId,map);
        pushMessage("hasHandleSituationUpdate",eventId);
        //记录写入数据库
        insertHandleSituationToDatabase(map);
        //记录发送至智能应急调度系统
        inInfo.set("recordUuid",recordUuid);
        sendHandleSituationToAPP(inInfo);
        return inInfo;
    }

    /**
     * 新增预案
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo addPlan(EiInfo inInfo){
        //调用接口获取处置预案,接口由应急预案提供
        initPlanStep(inInfo.getAttr());
        //解析预案，按各岗位拆分步骤

        //处置步骤列表写入缓存

        //处置步骤列表写入数据库

        return inInfo;
    }

    /**
     * 更新响应人员列表，用于事件源中新增、删除响应人员
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo updateResponseHRList(EiInfo inInfo){
        pushMessage("hasResponseHRUpdate","");
        return inInfo;
    }

    /**
     * 全部处置步骤完成情况检查，返回第一条未完成处置步骤
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo planStepCheck(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
//        String messageUser = attr.get("messageUser").toString();

        List<Map> planStepList = queryPlanStepFromDatabase(eventId);

        inInfo.set("complete",true);
        for (int i = 0; i < planStepList.size(); i++) {
            if(planStepCheckPost.contains(planStepList.get(i).get("post").toString())){
                if("0".equals(planStepList.get(i).get("complete").toString())){
                    inInfo.set("complete",false);
                    inInfo.set("stepContent",planStepList.get(i).get("content"));
                    break;
                }
            }
        }

        pushMessage("checkPlanStep",eventId);
        return inInfo;
    }

    /**
     * 某调度处置步骤完成情况检查，返回第一条未完成处置步骤
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo planStepCheckByPost(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        String post = attr.get("post").toString();

        List<Map> planStepList = queryPlanStepFromDatabase(eventId);

        inInfo.set("complete",true);
        for (int i = 0; i < planStepList.size(); i++) {
            if(post.equals(planStepList.get(i).get("post").toString())){
                if("0".equals(planStepList.get(i).get("complete").toString())){
                    inInfo.set("complete",false);
                    inInfo.set("stepContent",planStepList.get(i).get("content"));
                    break;
                }
            }
        }

        return inInfo;
    }

    /**
     * 某条处置步骤完成情况检查
     * @param inInfo EiInfo 数据列表

     */
    public EiInfo onePlanStepCheck(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        String recordUuid = attr.get("recordUuid").toString();
        List<Map> planStepList = queryPlanStepFromDatabase(eventId);

        inInfo.set("complete",true);
        for (int i = 0; i < planStepList.size(); i++) {
            if(recordUuid.equals(planStepList.get(i).get("recordUuid"))){
                if("0".equals(planStepList.get(i).get("complete").toString())){
                    inInfo.set("complete",false);
                    inInfo.set("stepContent",planStepList.get(i).get("content"));
                    break;
                }
            }
        }

        return inInfo;
    }

    /* 刷新事件等级 */
    public EiInfo refershEventLevel(EiInfo info){
        String eventId = info.getAttr().get("eventId").toString();
        Map eventInfo = queryEventFromRedis(eventId);
        String newEventLevel = eventInfo.get("eventLevel").toString();
        info.set("newEventLevel",newEventLevel);
        return info;
    }

    /* 查询事件等级 */
    public EiInfo queryEventLevel(EiInfo info){
        String eventId = info.get("eventId").toString();
        String eventSource = info.get("eventSource").toString();
        Map<String,String> param = new HashMap<>();
        param.put("eventId",eventId);
        List<Map<String,Object>> query = new ArrayList();
        if("1".equals(eventSource)){
            query = dao.query("YJCZ02.querySjEventLevel", param);
        }else{
            query = dao.query("YJCZ02.queryYlEventLevel", param);
        }
        if(query.size()>0){
            Object eventLevel = query.get(0).get("eventLevel");
            info.set("eventLevel",eventLevel);
        }else{
            info.set("eventLevel",360005);
        }
        return info;
    }

    /**
     * 事件等级变更
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo updateEventLevel(EiInfo inInfo){
        String eventId = inInfo.getAttr().get("eventId").toString();

        Map eventInfo = queryEventFromRedis(eventId);

        String oldEventLevel = eventInfo.get("eventLevel").toString();
        String newEventLevel = inInfo.getAttr().get("eventLevel").toString();
        addHandleRecord(eventId,"事件等级由"+eventLevelEnumDict.get(oldEventLevel)+
                "变更为"+eventLevelEnumDict.get(newEventLevel),70001,inInfo.getString("user"));
        //更新缓存中事件消息，临时代码
        eventInfo.put("eventLevel",newEventLevel);
        //更新应急事件表中数据
        HashMap<String, Object> map = new HashMap<>();
        String eventSource = inInfo.getString("eventSource");
        if("1".equals(eventSource)){
            map.put("fdUuid",eventId);
            map.put("fdRespT",Integer.parseInt(newEventLevel));
            map.put("fdDisposalT","30002");
            map.put("fdUpdateBy",inInfo.getString("userName"));
            map.put("fdUpdateTime",getCurrentTime());
            map.put("needPushCZ02","false");
            List<Map<String,Object>> inputList = new ArrayList<>();
            inputList.add(map);
            EiInfo eiInfo = updateEvents(inputList);
            changeIsRefsh("1");
        }else{
            //* 应急处置修改应急演练数据接口
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("fdUuid",eventId);//需要修改的演练ID
            eiInfo.set("drillLevel",Integer.parseInt(newEventLevel));//需要修改的演练等级
            eiInfo.set("UpdateBy",inInfo.getString("userName"));//
            eiInfo.set("UpdateTime",getCurrentTime());//
            eiInfo.set("from","yjcz");//
            eiInfo.set(EiConstant.serviceId,"S_YJ_YL02");
            EiInfo planInfo = XServiceManager.call(eiInfo);
        }

        insertEventToRedis(eventId,eventInfo);
        pushMessage("eventLevelUpdate",eventId);
        return inInfo;
    }


    /**
     * 前端获取指令列表
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getCommanderChange(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        String commanderType = attr.get("commanderType").toString();

        List<Map> commanderChange = queryCommanderChangeFromDatabase(eventId);

        ArrayList<Object> list = new ArrayList<>();
        for (int i = 0; i < commanderChange.size(); i++) {
            if (commanderChange.get(i).get("type").toString().equals(commanderType)){
                list.add(commanderChange.get(i));
            }
        }

        inInfo.set("commander",list);
        return inInfo;
    }

    /*读取唯一人员：姓名+联系方式+类型*/
    public List<Map> getHrOneInfo(List<Map> responseHR,String type){
        List<Map> list = new ArrayList<>();
        Map<String,Integer> publicMap = new HashMap<>();//去重
        for (int i = 0; i < responseHR.size(); i++) {
            Map map = responseHR.get(i);
            String hrOneInfo = map.get("person").toString()+map.get("tel").toString()+map.get("type").toString();
            if(publicMap.containsKey(hrOneInfo)){
                continue;
            }else {
                publicMap.put(hrOneInfo,0);
            }
            if(type.equals(map.get("type").toString())){
                list.add(map);
            }
        }
        return list;
    }

    // 通知人员从库刷新到缓存
    public void initAllResponList(String eventId) {
        List<Map> responseHR = getUserFromRedis(eventId);
        if(responseHR.size() <= 0) {
            responseHR = queryResponseHRRecordFromDatabase(eventId);
            addUsersRedis(eventId, responseHR);
        }
    }

    /**
     * 获取电话通知人员列表 + 响应人员列表 + 到达人员列表
     */
    public EiInfo getAllResponList(EiInfo info){
        String eventId = info.getString("eventId");
        List<Map> responseHR = getUserFromRedis(eventId);
        List<Map> responseHRFromDatabase = queryResponseHRRecordFromDatabase(eventId);
        if(responseHR.size() <= 0) {
            addUsersRedis(eventId, responseHRFromDatabase);
        }
        List<Map> list = getHrOneInfo(responseHRFromDatabase,"60001");
        List<Map> list2 = getHrOneInfo(responseHRFromDatabase,"60002");
        List<Map> list3 = getHrOneInfo(responseHRFromDatabase,"60002");

        EiInfo eiInfo = new EiInfo();
        //电话通知人员
        EiBlock block = new EiBlock("noticeSituation");
        block.addRows(sortSignInPerson(list, "noticeTime"));
        eiInfo.addBlock(block);
        //响应人员
        EiBlock block2 = new EiBlock("respondSituation");
        block2.addRows(sortSignInPerson(list2, "respondTime"));
        eiInfo.addBlock(block2);
        //到达人员
        EiBlock block3 = new EiBlock("arrivalSituation");
        block3.addRows(sortSignInPerson(list3, "arrivalTime"));
        eiInfo.addBlock(block3);

        return eiInfo;
    }

    /**
     * 前端获取电话通知人员列表
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getCellNoticeHRList(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        List<Map> responseHR = getUserFromRedis(eventId);

        List<Map> list = getHrOneInfo(responseHR,"60001");

        EiInfo eiInfo = new EiInfo();
        EiBlock block = new EiBlock("noticeSituation");
        block.addRows(sortSignInPerson(list, "noticeTime"));
        eiInfo.addBlock(block);
        return eiInfo;
    }

    /**
     * 前端获取响应人员列表
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getResponseHRList(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        List<Map> responseHR = getUserFromRedis(eventId);

        List<Map> list = getHrOneInfo(responseHR,"60002");

        EiInfo eiInfo = new EiInfo();
        EiBlock block = new EiBlock("respondSituation");
        block.addRows(sortSignInPerson(list, "respondTime"));
        eiInfo.addBlock(block);
        return eiInfo;
    }

    /**
     * 前端获取到达人员列表
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getArrivalHRList(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        List<Map> responseHR = getUserFromRedis(eventId);
        List<Map> list = getHrOneInfo(responseHR,"60002");

        EiInfo eiInfo = new EiInfo();
        EiBlock block = new EiBlock("arrivalSituation");
        block.addRows(sortSignInPerson(list, "arrivalTime"));
        eiInfo.addBlock(block);
        return eiInfo;
    }

    //现场人员到岗信息
    public EiInfo getDpCellNoticeHRNum(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        List<Map> responseHR = getUserFromRedis(eventId);
        List<Map> list = getHrOneInfo(responseHR,"60001");
        int has = 0;
        int noHas = 0;
        for(Map map:list){
            if(map.get("noticeTime")==null || "".equals(map.get("noticeTime").toString())) {
                noHas+=1;
            }else{
                has+=1;
            }
        }
        inInfo.set("has",has);
        inInfo.set("noHas",noHas);
        return inInfo;
    }
    /**
     * 前端获取处置记录列表
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getHandleRecord(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        String tabTag = attr.get("tabTag").toString();

        List<Map> handleRecord = getHandleRecordFromRedis(eventId);

        List<Object> list = new ArrayList<>();
        for (int i = handleRecord.size()-1; i >= 0; i--) {
            if(handleRecord.get(i).get("type").toString().equals(tabTag)){
                list.add(handleRecord.get(i));
            }
        }

        EiInfo eiInfo = new EiInfo();
        EiBlock block = new EiBlock("handleRecord");

        block.addRows(list);

        eiInfo.addBlock(block);
        return eiInfo;
    }

    /**
     * 前端获取现场处置情况列表(旧方法)
     * @param inInfo EiInfo 数据列表
    public EiInfo getHandleSituation(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        List<Map> handleSituationList = queryHandleSituationFromDatabase(eventId);
        ArrayList<Object> list = new ArrayList<>();
        for(int i=0;i<handleSituationList.size();i++){
            Map handleSituationMap = handleSituationList.get(i);
            List annexList = JSONObject.parseObject(handleSituationMap.get("annex").toString(), List.class);
            handleSituationMap.put("annex",annexList);

            list.add(handleSituationMap);
        }
        inInfo.set("handleSituation",list);
        return inInfo;
    }
     */
    /*
    * 前端获取现场处置情况列表
    * @param inInfo EiInfo 数据列表
    */
    public EiInfo getHandleSituation(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        List<Map> mapList = getHandleSituationFromRedis(eventId);
        ArrayList<Object> list = new ArrayList<>();
        for(Map map:mapList){
            Object o = map.get("content");
            if(o!=null){
                String htmlText = o.toString().replaceAll("(\\r\\n|\\r|\\n)", "<br>");
                map.put("content",htmlText);
            }
            List annexList = JSONObject.parseObject(map.get("annex").toString(), List.class);
            map.put("annex",annexList);
            list.add(map);
        }
        inInfo.set("handleSituation",list);
        return inInfo;
    }

    public List<String> upAndDown(String str){
        List<String> resu = new ArrayList<>();
        switch (str){
            case "up":
                resu.add("0");break;
            case "down":
                resu.add("1");break;
            default:
                resu.add("0");
                resu.add("1");
        }
        return resu;
    }

    /**
     * pcc发布
     * @param info
     * @return
     */
    public EiInfo pccRelease(EiInfo info){
        String eventId = info.getString("eventId");
        String eventSource = info.getString("eventSource");

        boolean stationAll = "0".equals(info.getString("stationAll"));//是否全线网
        boolean positionAll = "0".equals(info.getString("positionAll"));//是否全区域
        boolean lineAll = "0".equals(info.getString("lineAll"));//是否全线路

        String startTime = getCurrentTime();//开始时间
        String endTime = getTimePlus(1);//结束时间
        String lineId = "";//线路编号
        List<String> lineArr = new ArrayList<>();//车站编号
        List<String> positionArr = new ArrayList<>();//上下行
        List<String> carArr = new ArrayList<>();//列车编号

        String msgType = "";
        String msgName = "";
        String msgState = "";

        Map<String,String> parMap = new HashMap<>();
        //1-处理应急事件
        if("1".equals(eventSource)){
            parMap.put("fdUuid",eventId);
            List<Map<String,Object>> result = dao.queryAll("YJCZ01.query", parMap);
            Map<String, Object> map = result.get(0);

            lineId = map.get("fdLine").toString();
            Object fdDirection = map.get("fdDirection");

            msgType = map.get("fdMsgType").toString();
            msgName = map.get("fdMsgName").toString();
            msgState = map.get("fdMsgStage").toString();

            //上下行为空 则是车站
            if(isEmit(fdDirection)){
                String fdStationNumber = getStrValue(map.get("fdStationNumber"));
                lineArr.add(fdStationNumber);
            }else{
                String fdStartStation = getStrValue(map.get("fdStartStation"));
                String fdEndStation = getStrValue(map.get("fdEndStation"));
                lineArr.add(fdStartStation);
                lineArr.add(fdEndStation);
                positionArr = upAndDown(fdDirection.toString());
            }
        }else{
            parMap.put("drillUUID",eventId);
            List<Map<String,Object>> result = dao.queryAll("YJYL01.queryDrillInfo", parMap);
            Map<String, Object> map = result.get(0);

            lineId = map.get("line").toString();
            Object fdDirection = map.get("direction");

            msgType = map.get("msgType").toString();
            msgName = map.get("msgName").toString();
            msgState = map.get("msgStage").toString();

            //上下行为空 则是车站
            if(isEmit(fdDirection)){
                String fdStationNumber = getStrValue(map.get("stationNumber"));
                lineArr.add(fdStationNumber);
            }else{
                String fdStartStation = getStrValue(map.get("Sstation"));
                String fdEndStation = getStrValue(map.get("Estation"));
                lineArr.add(fdStartStation);
                lineArr.add(fdEndStation);
                positionArr = upAndDown(fdDirection.toString());
            }
        }

        carArr.add(lineId);

        Map<String,Object> data = new HashMap<>();
        Map<String,Object> car = new HashMap<>();
            car.put("all",stationAll);
            car.put("arr",carArr);
        Map<String,Object> station = new HashMap<>();
            station.put("all",stationAll);
            List<Map<String,Object>> line = new ArrayList<>();
                Map<String,Object> lineMap = new HashMap<>();
                lineMap.put("all",lineAll);
                lineMap.put("lineId",lineId);
                lineMap.put("arr",lineArr);
                line.add(lineMap);
            station.put("line",line);
            Map<String,Object> position = new HashMap<>();
                position.put("all",positionAll);
                position.put("arr",positionArr);
            station.put("position",position);

        data.put("car",car);
        data.put("station",station);
        data.put("model","180002");
        data.put("content","");
        data.put("startDatetime",startTime);
        data.put("endDatetime",endTime);
        //tType-模板类型 tName-模板名称 tPhase-发布阶段
        data.put("tType",msgType);
        data.put("tName",msgName);
        data.put("tPhase",msgState);
        info.set("data",data);
        return info;
    }

    //事故概况
    public String sjgk(EiInfo info){
        String eventId = info.getString("eventId");
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        //需要获取排除掉Nocc的信息
        List<Map> list = dao.query("YJCZ02.queryHSToBulletin", map);
        //返回的文本
        StringBuilder sb = new StringBuilder();
        //处理occ消息格式
        for(Map mp:list){
            String dataTime = mp.get("dataTime").toString();//发来时间
            String content = mp.get("content").toString();//发来内容
            String identity = mp.get("identity").toString();//发来者
            //截取时分
            String hhmm = "";
            String[] s1 = dataTime.split(" ");
            if(s1.length>1){
                hhmm = s1[1].substring(0, 5);//获得时分
            }
            //根据模板拼接返回内容
            // sb.append("\t").append(hhmm).append("分，").append(identity).append("报：").append(content).append("\n");
            sb.append("\t").append(content).append("\n");
        }
        return sb.toString();
    }

    /**
     * 获取并处事件快报弹窗内容
     * @param info eventId事件ID
     * @return EiInfo
     */
    public EiInfo querybulletinText(EiInfo info){
        return eventReportDel(info);
    }

    public String timeDel(String time){
        StringBuilder sb = new StringBuilder();
        String year = time.substring(0,4);
        String month = time.substring(5,7);
        String day = time.substring(8,10);
        String hour = time.substring(11,13);
        String minue = time.substring(14,16);
        sb.append(year).append("年").append(month).append("月").append(day).append("日").append(hour).append("时").append(minue).append("分");
        return sb.toString();
    }

    /* 事件快报弹窗所需数据 */
    public EiInfo eventReportDel(EiInfo info) {
        Map<String,String> parmeMap = new HashMap<>();
        List<Map<String,String>> parmeList = new ArrayList<>();
        String eventSource = info.getString("eventSource");
        String eventId = info.getString("eventId");

        Map eventInfo = queryEventFromRedis(eventId);
        String situation = sjgk(info); // 事故概况
        String commander1 = getCommander(info).getString("commander");

        // 获取nocc控制中心和occ控制中心的数据
        String noccTabTag = "70001";
        String occTabTag = "70002";
        List<Map> handleRecord = getHandleRecordFromRedis(eventId);
        List<Object> noccAndOccRecords = new ArrayList<>();
        for (int i = handleRecord.size() - 1; i >= 0; i--) {
            if(handleRecord.get(i).get("type").toString().equals(occTabTag) || handleRecord.get(i).get("type").toString().equals(noccTabTag)){
                noccAndOccRecords.add(handleRecord.get(i));
            }
        }
        // 拼接 time 和 content 字段，二者之间加一个空格，每条记录后加换行符
        StringBuilder noccAndOccRecordsStr = new StringBuilder();
        for (Object obj : noccAndOccRecords) {
            // 强制转换成 Map 以便获取字段
            Map record = (Map) obj;
            String time = record.get("time").toString();
            String content = record.get("content").toString();
            noccAndOccRecordsStr.append(time).append(" ").append(content).append("\n");
        }

        if(!mapIsEmpty(eventInfo)) {
            String currTime = timeDel(getCurrentTime());
            parmeMap.put("reportTime", currTime);
            parmeMap.put("eventTime", timeDel(eventInfo.get("eventTime").toString()));
            parmeMap.put("endingTime", currTime);
            String eventPlace = "南宁轨道交通" + eventInfo.get("eventPlace").toString();
            parmeMap.put("eventPlace", eventPlace);
            String name = "";
            if(!commander1.isEmpty()){
                String[] commander = commander1.split(" ");//指挥长信息
                if(commander.length>1){
                    name = commander[0] + commander[1];
                }
            }
            parmeMap.put("reporterAndPhoneNum", name);
            parmeMap.put("situation", situation);
            parmeMap.put("noccAndOccRecords", noccAndOccRecordsStr.toString());
            parmeList.add(parmeMap);
        }
        info.set("command", "export_report");
        info.set("data",parmeList);
        return info;
    }

    public String reportTime() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd HH:mm:ss");
        return dateFormat.format(date);
    }

    //事件快报历史保存
    public EiInfo eventReportSave(EiInfo info){
        String eventId = info.getString("eventId");
        Map<String,String> map = new HashMap<>();
        map.put("data",info.getString("data"));
        map.put("time",reportTime());
        String valRedis = getValRedis(PrefixPattern.KPATTERN.getPattern() + eventId);
        List<Map> list = new ArrayList<>();
        if(valRedis != null){
            list = JSONObject.parseArray(valRedis,Map.class);
        }
        list.add(map);
        saveToRedis(PrefixPattern.KPATTERN.getPattern()+eventId,JSON.toJSONString(list));
        return info;
    }

    //读取事件快报历史
    public EiInfo readEventReport(EiInfo info){
        String eventId = info.getString("eventId");
        String valRedis = getValRedis(PrefixPattern.KPATTERN.getPattern() + eventId);
        List<Map> list = new ArrayList<>();
        if(valRedis != null){
            list = JSONObject.parseArray(valRedis,Map.class);
        }
        info.set("report",list);
        return info;
    }

    /**
     * 事件快报生成
     * @param info
     */
    public EiInfo eventReportBorn(EiInfo info){
//        info = eventReportDel(info);
        List<Map<String,String>> parmeList = new ArrayList<>();
        Map<String,String> parmeMap = new HashMap<>();
        String currTime = timeDel(getCurrentTime());
        parmeMap.put("naturalHazard", info.get("naturalHazard").toString());
        parmeMap.put("accidentDisaster", info.get("accidentDisaster").toString());
        parmeMap.put("publicHealthEvent", info.get("publicHealthEvent").toString());
        parmeMap.put("socialSecurityIncident", info.get("socialSecurityIncident").toString());
        parmeMap.put("eventTime", info.get("eventTime").toString());
        parmeMap.put("eventPlace", info.get("eventPlace").toString());
        parmeMap.put("reportTime", currTime);
        parmeMap.put("reporterAndPhoneNum", info.get("reporterAndPhoneNum").toString());
        parmeMap.put("eventUnitProfile", info.get("eventUnitProfile").toString());
        parmeMap.put("eventSituation", info.get("eventSituation").toString());
        parmeMap.put("eventCourse", info.get("eventCourse").toString());
        parmeMap.put("measuresAndPersonnelPresence", info.get("measuresAndPersonnelPresence").toString());
        parmeMap.put("deathCount", info.get("deathCount").toString());
        parmeMap.put("injuryCount", info.get("injuryCount").toString());
        parmeMap.put("economicLoss", info.get("economicLoss").toString());
        parmeMap.put("otherSituations", info.get("otherSituations").toString());
        parmeList.add(parmeMap);
        info.set("data", parmeList);
        info.set("command", "export_report");
        EiInfo fileInfo = callXService(info, "S_FS_98");

        String fileName = "事故快报-" + getTimeStamp()+ ".docx";
        String fileNameOSS = getTimeStamp() + ".docx";

        writeFileToOSS(fileInfo.get("reportByte").toString(), fileNameOSS, "CMP");
        writeFileToFileServe(fileInfo.get("reportByte").toString(), fileName);
//        String fileUrl = bornFilePath("EventReport/", fileName);
        EiInfo outInfo = new EiInfo();
//        outInfo.set("fileUrl",fileUrl);
        return outInfo;
    }

    /**
     * 前端获取处置步骤列表
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getPlanStep(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("eventId").toString();
        //事件时间
        String timeStr = attr.get("eventHappenTime").toString();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date time = null;
        try {
            time = sdf.parse(timeStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        long timeInMillis = time.getTime();
//        long nodeInMillis = node * 60 * 1000;
        long currentTimeInMillis = System.currentTimeMillis();


        List<Map> planStep = queryPlanStepFromDatabase(eventId);
        List<String> planName = new ArrayList<>();
        List<List<Map>> planList = new ArrayList<>();

        for(int i=0;i<planStep.size();i++){
            int index = planName.indexOf(planStep.get(i).get("planName").toString());
            if(index == -1){
                planName.add(planStep.get(i).get("planName").toString());
                planList.add(new ArrayList<>());
                index = planList.size()-1;
            }
            Map map = planStep.get(i);
            Object node = map.get("node");
            if(node == null || "".equals(node.toString())){
                map.put("node","0");
            }else{
                map.put("node",node.toString());
            }
            planList.get(index).add(map);
        }

        HashMap<Object, Object> thePlans = new HashMap<>();
        List<Object> plan = new ArrayList<>();
        for(int i=0;i<planList.size();i++){
            HashMap<Object, Object> tempMap = new HashMap<>();
            tempMap.put("planName",planName.get(i));

            int nodeInMillis  = 0;
            for(Map map:planList.get(i)){
                nodeInMillis += Integer.parseInt(map.get("node").toString())*60000;
                boolean b = currentTimeInMillis > (timeInMillis + nodeInMillis);
                if(b){
                    map.put("node","high");
                }
            }

            tempMap.put("planContent",planList.get(i));
            plan.add(tempMap);
        }

        //读取预案选项
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("planLine",140001);
        eiInfo.set(EiConstant.serviceId, "S_YJ_YGCX_01");
        EiInfo planInfo = XServiceManager.call(eiInfo);
        List<Map> planLists = (List)planInfo.getAttr().get("planList");

        List<Map> planSelList = new ArrayList<>();
        for (Map map : planLists) {
            String planNam = map.get("planName").toString();
            if (!planName.contains(planNam)) {
                planSelList.add(map);
            }
        }

        thePlans.put("plan",plan);
        inInfo.set("thePlans",thePlans);
        inInfo.set("planSelects",planSelList);

        return inInfo;
    }

    public static int convertToMinutes(String input) {
        int num = 0 ;
        if(input == null || input == ""){
            num = 0;
        }else{
            num = Integer.parseInt(input);
        }

        if (num < 60) {
            return 0;
        } else {
            return (num - 60) / 60 + 1;
        }
    }

    /**
     *前端获取事件影响
     Object time = info.get("time"); //时间戳
     Object lineId = info.get("line_id"); //线路号
     Object type = info.get("type"); //车次号变化状态
     Object rtuId = info.get("rtu_id"); //集中站站号
     Object noccWindow = info.get("nocc_window"); //NOCC车次窗编号
     Object noccWindowOffset = info.get("nocc_window_offset"); //列车在车次窗中的位置
     Object devType = info.get("dev_type"); //列车所在的设备的类型
     Object devName = info.get("dev_name"); //列车所在的设备的名称
     Object trainIndex = info.get("train_index"); //列车标示号，全线唯一（若无法提供，缺省值为0）
     Object groupId = info.get("group_id"); //列车编组号
     Object trainId = info.get("train_id"); //表号
     Object globalId = info.get("global_id"); //车次号
     Object destinationId = info.get("destination_id"); //目的地号
     Object rollingstock = info.get("rollingstock"); //编组数量
     Object driverId = info.get("driver_id"); //司机号
     Object routeId = info.get("route_id"); //运行路径号（若无法提供，缺省值为0）
     Object otpTime = info.get("otp_time"); //计划偏离时间
     Object mode = info.get("mode"); //列车状态，见附录列车状态定义
     Object arriveTime = info.get("arrive_time"); //列车到点
     Object departTime = info.get("depart_time"); //列车发点
     Object rate = info.get("rate"); //满载率
     Object speed = info.get("speed"); //速度
     Object spare = info.get("spare"); //预留
     */
    public EiInfo getEventImpact(EiInfo inInfo){
        String eventId = inInfo.getString("eventId");
        //列车信息更新消息
        try {
            Object lineId = inInfo.get("lineId");
            Map<String, Object> curImpact = kafkaCache.get(lineId);
            int maxTime = 0, sTime = 0;
            //找出最大列车对应的延误时间
            if(!isEmit(curImpact)){
                Object otp_time = curImpact.get("otp_time");
                if(!isEmit(otp_time)){
                    int newTime = Integer.parseInt(otp_time.toString());
                    if(newTime>maxTime){
                        maxTime = newTime/60;//暂定取整数分钟
                        sTime = newTime%60;//获取到秒
                    }
                }
            }
            String delayTime = String.valueOf(maxTime);
            inInfo.set("eventImpact","列车延误："+delayTime+"分钟 "+sTime+"秒钟");
        }catch (Exception e){
            inInfo.set("eventImpact","列车延误：0分钟 0秒钟");
        }
        return inInfo;
    }

    public void refshKafka(){
        EiInfo info = new EiInfo();
        Map<String,String> parMap = new HashMap<>();
        parMap.put("key","kafa");
        info.set("data",parMap);
        wsMes(info);
    }


    /**
     * 缓存kafka数据进临时缓存
     * 做分线路入库
     */
    public void saveTokafkaCache(List<Map<String,Object>> result){
        if(result.size()>0){
            Map<String, Object> map = result.get(0);
            if(map != null){
                Object line_id = map.get("line_id");
                if(line_id != null){
                    kafkaCache.put(line_id.toString(),map);
                    if(kafkaNum>=1000){
                        kafkaNum = 0;
                        refshKafka();
                    }else{
                        kafkaNum +=1;
                    }
                }
            }
        }
    }

    //初始化缓存信息
    public static void initKafkaCache(){
        Jedis jedis = null;
        try {
            jedis = pool.getResource();
            String ckxEventImpact = jedis.get("ckxEventImpact");
            if(ckxEventImpact==null || "null".equals(ckxEventImpact)){
                kafkaCache = new HashMap<>();
            }else{
                kafkaCache = JSONObject.parseObject(ckxEventImpact, Map.class);
            }
        }catch (Exception e) {
            // 处理异常
            kafkaCache = new HashMap<>();
            logger.error("Redis数据读取错误{}", e.getMessage());
        } finally {
            if(jedis != null){
                jedis.close();
            }
        }
    }


    /* 释放连接资源 */
    private void close(Jedis jedis) {
        if (jedis != null) {
        //  jedis.quit();
            jedis.close();
        }
    }

    /**
     * 缓存事件延误信息到redis
     * @param key
     * @param value
     */
    public void saveToRedis(String key,String value) {
        Jedis jedis = null;
        try {
            jedis = pool.getResource();
            jedis.set(key,value);
        }catch (PlatException platException) {
            logger.error("redis服务重新启动...");
        }finally {
            close(jedis);
        }
    }

    public String getValRedis(String key){
        String value = "";
        Jedis jedis = null;
        try {
            jedis = pool.getResource();
            value = jedis.get(key);
        }catch (Exception e) {
            // 处理异常
            logger.error("Redis数据读取错误{}", e.getMessage());
        } finally {
            close(jedis);
        }
//        if("OK".equals(value)){
//            value = jedis.get(key);
//            if("OK".equals(value)){
//                return null;
//            }
//        }
        return value;
    }

    /**
     * 接收kafka事件影响信息接口（S_YJ_CZ_22）
     * @param info
     * @return
     */
    public EiInfo getKafkaEventImpact(EiInfo info){
        List<Map<String,Object>> result = new ArrayList<>();
        try {
            result = info.getBlock("result").getRows();
            saveTokafkaCache(result);
        }catch (Exception e){
            //容错处理
            logger.error("kafka数据解析异常");
        }
        return info;
    }

    /**
     * 前端获取现场指挥长信息
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getCommander(EiInfo inInfo){
        String eventId = inInfo.getAttr().get("eventId").toString();
        Map eventInfo = queryEventFromRedis(eventId);
        inInfo.set("commander","");
        if(!isEmit(eventInfo.get("commander").toString())){
            inInfo.set("commander",eventInfo.get("commander").toString()+" "+eventInfo.get("commanderTel").toString());
        }else{
            HashMap<Object, Object> map = new HashMap<>();
            map.put("eventId",eventId);
            map.put("type",50002);
            List<Map<String,String>> mapList = (List) dao.query("YJCZ02.queryChiefOrHead", map);
            if(mapList.size()>0){
                Map<String, String> endMap = mapList.get(0);
                String replace = endMap.get("person").replace("_"," ");
                inInfo.set("commander",replace);
            }
        }
        return inInfo;
    }

    /**
     * 前端获取抢险负责人信息
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getDirector(EiInfo inInfo){
        String eventId = inInfo.getAttr().get("eventId").toString();
        Map eventInfo = queryEventFromRedis(eventId);
        inInfo.set("director","");
        if(!isEmit(eventInfo.get("director").toString())){
            inInfo.set("director",eventInfo.get("director").toString()+" "+eventInfo.get("directorTel").toString());
        }else{
            HashMap<Object, Object> map = new HashMap<>();
            map.put("eventId",eventId);
            map.put("type",50001);
            List<Map<String,String>> mapList = (List) dao.query("YJCZ02.queryChiefOrHead", map);
            if(mapList.size()>0){
                Map<String, String> endMap = mapList.get(0);
                String replace = endMap.get("person").replace("_"," ");
                inInfo.set("director",replace);
            }
        }
        return inInfo;
    }

    /**
     * 前端获取时间节点列表
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo getCountDownNodeList(EiInfo inInfo){
        String eventId = inInfo.getString("eventId");
        //读取预案名称
        Map parseMap = queryEventFromRedis(eventId);
        String planName = parseMap.get("planName").toString();

        Map<String,String> queryMap = new HashMap<String,String>(){{
            put("eventId",eventId);put("planName",planName);
        }};

        List<Map> timeNodeMap = dao.query("YJCZ02.queryPlanStepNode",queryMap);

        List<Map> resultList = new ArrayList<>();
        for (Map item : timeNodeMap) {
            String timeNode = item.get("timeNode").toString();
            int i = minuteToSecond(timeNode);
            item.put("time", i);
            resultList.add(item);
        }

        inInfo.set("timeNode",resultList);
        return inInfo;
    }

    /**
     * 人员名单根据签到时间排序
     * @param responseHR List<Map> 数据列表
     * @param timeKey String 排序时间类型在数据列表中key值
     */
    public List<Map> sortSignInPerson (List<Map> responseHR,String timeKey){
        List<Map> list = new ArrayList<>();
        while (responseHR.size() > 0){
            String time = responseHR.get(0).get(timeKey).toString();
            int index = list.size();
            if(!"".equals(time)){
                for (int i = 0; i < list.size(); i++) {
                    if(list.get(i).get(timeKey).toString().equals("") || list.get(i).get(timeKey).toString().compareTo(time) > 0){
                        index = i;
                        break;
                    }
                }
            }
            list.add(index,responseHR.get(0));
            responseHR.remove(0);
        }
        return list;
    }




    /**
     * 新增负责人变更记录记录
     */
    public void addCommanderChangeRecord(String eventId,int type,String personInfo,String content){
        String recordUuid = getUuid();
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        map.put("recordUuid",recordUuid);
        map.put("type",type);
        map.put("person",personInfo);
        map.put("datetime",getCurrentTime());
        map.put("content",content);
        map.put("annex","[]");
        insertCommanderChangeToDatabase(map);
        if(type == 50002){
            pushMessage("hasCommanderUpdate",eventId);
        }else if(type == 50001){
            pushMessage("hasDirectorUpdate",eventId);
        }
    }

    /**
     * 接收应急智能调度系统发送的负责人变更记录
     * serviceId S_YJ_CZ_01
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo receiveCommanderChange(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("event_uuid").toString();

        Map eventInfo = queryEventFromRedis(eventId);

        Object commander = attr.get("commander_name");
        Object commanderTel = attr.get("commander_telephone");
        Object director = attr.get("leader_name");
        Object directorTel = attr.get("leader_telephone");

        if(!mapIsEmpty(eventInfo)){
            String oriCommander = eventInfo.get("commander").toString();
//            String oriCommanderTel = eventInfo.get("commanderTel").toString();
            String oriDirector = eventInfo.get("director").toString();
//            String oriDirectorTel = eventInfo.get("directorTel").toString();

            if(!isEmpty(commander)){
                if(!commander.equals(oriCommander)){
                    StringBuilder sb = new StringBuilder();
                    if(isEmpty(oriCommander)){
                        sb.append(commander).append("被授权为现场指挥长");
                    }else{
                        sb.append("现场指挥长变更为：").append(commander);
                    }

                    if(!isEmpty(commanderTel)){
                        eventInfo.put("commanderTel",commanderTel.toString());
                        sb.append("，联系方式为：").append(commanderTel);
                    }
                    eventInfo.put("commander",commander.toString());
                    insertEventToRedis(eventId,eventInfo);
                    addCommanderChangeRecord(eventId,50002,commander+"_"+commanderTel,sb.toString());
                }
            }
            if(!isEmpty(director)){
                if(!director.equals(oriDirector)){
                    StringBuilder sb = new StringBuilder();
                    if(isEmpty(oriDirector)){
                        sb.append(director).append("被授权为抢险负责人");
                    }else{
                        sb.append("抢险负责人变更为：").append(director);
                    }

                    if(!isEmpty(directorTel)) {
                        eventInfo.put("directorTel", directorTel);
                        sb.append("，联系方式为：").append(directorTel);
                    }
                    eventInfo.put("director",director.toString());
                    insertEventToRedis(eventId,eventInfo);
                    addCommanderChangeRecord(eventId,50001,director+"_"+directorTel,sb.toString());
                }
            }

        }else{
            HashMap<Object, Object> map = new HashMap<>();
            map.put("eventId",eventId);
            List<Map<String,String>> mapList = (List) dao.query("YJCZ02.queryChiefOrHead", map);
            boolean zh = true;
            boolean fz = true;
            Map<String,String> zhMap = new HashMap<>();
            Map<String,String> fzMap = new HashMap<>();

            for(Map<String,String> mp:mapList){
                if(zh&&"50002".equals(mp.get("type"))){
                    zhMap = mp;
                    zh = false;
                }
                if(fz&&"50001".equals(mp.get("type"))){
                    fzMap = mp;
                    fz = false;
                }
            }

            if(!isEmpty(commander)){
                StringBuilder sb = new StringBuilder();
                if(zh) {
                    sb.append(commander).append("被授权为现场指挥长");
                    sb.append("，联系方式为：").append(commanderTel);
                    addCommanderChangeRecord(eventId,50002,commander+"_"+commanderTel,sb.toString());
                }else{
                    String[] split = zhMap.get("person").split("_");
                    if(!split[0].equals(commander)){
                        sb.append("现场指挥长变更为：").append(commander);
                        sb.append("，联系方式为：").append(commanderTel);
                        addCommanderChangeRecord(eventId,50002,commander+"_"+commanderTel,sb.toString());
                    }
                }
            }

            if(!isEmpty(director)) {
                StringBuilder sb = new StringBuilder();
                if(fz) {
                    sb.append(director).append("被授权为抢险负责人");
                    sb.append("，联系方式为：").append(directorTel);
                    addCommanderChangeRecord(eventId, 50001, director + "_" + directorTel, sb.toString());
                }else{
                    String[] split = fzMap.get("person").split("_");
                    if(!split[0].equals(director)){
                        sb.append("抢险负责人变更为：").append(director);
                        sb.append("，联系方式为：").append(directorTel);
                        addCommanderChangeRecord(eventId, 50001, director + "_" + directorTel, sb.toString());
                    }
                }
            }
        }

        return inInfo;
    }

    public boolean ifNotice(String key){
        return getEventStateRedis(key)==null;
    }

    public String getStrValue(Object o){
        return o==null?"":o.toString();
    }

    public void initNotices(String eventId,List<Map> mesList){
        List<Map> resultList = new ArrayList<>();
        List<Map> responseHR = getUserFromRedis(eventId);

        for (int i = 0; i < responseHR.size(); i++) {
            Map hrMap = responseHR.get(i);
            for(Map attr: mesList) {
                String userId = getStrValue(attr.get("user_id"));
                String answerSituation = getStrValue(attr.get("answer_situation"));
                String answerTime = getStrValue(attr.get("answer_time"));
                String readSituation = getStrValue(attr.get("read_situation"));
                String readTime = getStrValue(attr.get("read_time"));
                if(userId.equals(hrMap.get("userId"))){
                    if ("60001".equals(hrMap.get("type").toString())) {
                        if(answerSituation.equals("已接")){
                            hrMap.put("noticeTime",answerTime);
                        }else if(answerSituation.equals("未接")){
                            hrMap.put("noticeTime","");
                        }
                    }else{
                        if("60002".equals(attr.get("type").toString())){
                            hrMap.put("arrivalTime",answerTime);
                        }else{
                            if(readSituation.equals("已读")){
                                hrMap.put("respondTime",readTime);
                            }else if(readSituation.equals("未读")){
                                hrMap.put("respondTime","");
                            }
                        }
                    }
                    updateResponseHRRecordToDatabase(hrMap);
                }
            }
            resultList.add(hrMap);
        }
        addUsersRedis(eventId,resultList);
        delReisKeys(PrefixPattern.LPATTERN.getPattern()+eventId);
    }

    public EiInfo testaa(EiInfo info){
        String sleep = info.get("sleep").toString();
        List<Map> list = (List)info.get("list");
        for(Map map:list){
            EiInfo eiInfo = new EiInfo();
            eiInfo.setAttr(map);
            receiveNoticeSignIn(eiInfo);
        }
        return info;
    }

    /**
     * 接收应急智能调度系统发送的人员通知签到信息（电话通知时间）
     * serviceId S_YJ_CZ_02
     */
    public synchronized EiInfo receiveNoticeSignIn(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("event_uuid").toString();
        if(ifNotice(eventId)){
            List<Map> maps = getLUserFromRedis(eventId);
            attr.put("type","60001");
            maps.add(attr);
            addLUsersRedis(eventId,maps);
        }else{
            String userId = attr.get("user_id").toString();
            String answerSituation = attr.get("answer_situation").toString();
            String answerTime = attr.get("answer_time").toString();
            List<Map> resultList = new ArrayList<>();
            List<Map> responseHR = getUserFromRedis(eventId);
            for (int i = 0; i < responseHR.size(); i++) {
                Map hrMap = responseHR.get(i);
                if("60001".equals(hrMap.get("type").toString())){
                    if(userId.equals(hrMap.get("userId"))){
                        if(answerSituation.equals("已接")){
                            hrMap.put("noticeTime",answerTime);
                        }else if(answerSituation.equals("未接")){
                            hrMap.put("noticeTime","");
                        }
                        updateResponseHRRecordToDatabase(hrMap);
                    }
                }
                resultList.add(hrMap);
            }
            addUsersRedis(eventId,resultList);
            pushMessage("hasNoticeSignIn",eventId);
        }

        return inInfo;
    }

    /**
     * 接收应急智能调度系统发送的人员到场签到信息
     * serviceId S_YJ_CZ_03
     * @param inInfo EiInfo 数据列表
     */
    public synchronized EiInfo receiveRespondSignIn(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("event_uuid").toString();
        if(ifNotice(eventId)){
            List<Map> maps = getLUserFromRedis(eventId);
            attr.put("type","60002");
            maps.add(attr);
            addLUsersRedis(eventId,maps);
        }else {
            String userId = attr.get("user_id").toString();
            String answerTime = attr.get("answer_time").toString();
            List<Map> resultList = new ArrayList<>();
            List<Map> responseHR = getUserFromRedis(eventId);
            for (int i = 0; i < responseHR.size(); i++) {
                Map hrMap = responseHR.get(i);
                if("60002".equals(hrMap.get("type").toString())){
                    if(userId.equals(responseHR.get(i).get("userId"))){
                        hrMap.put("arrivalTime",answerTime);
                        updateResponseHRRecordToDatabase(hrMap);
                    }
                }
                resultList.add(hrMap);
            }
            addUsersRedis(eventId,resultList);
            pushMessage("hasArrivalSignIn",eventId);
        }
        return inInfo;
    }

    /**
     * 接收应急智能调度系统发送的人员响应签到信息
     * serviceId S_YJ_CZ_04
     * @param inInfo EiInfo 数据列表
     */
    public synchronized EiInfo receiveArrivalSignIn(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("event_uuid").toString();
        if(ifNotice(eventId)){
            List<Map> maps = getLUserFromRedis(eventId);
            attr.put("type","60003");
            maps.add(attr);
            addLUsersRedis(eventId,maps);
        }else {
            String userId = attr.get("user_id").toString();
            String readSituation = attr.get("read_situation").toString();
            String readTime = attr.get("read_time").toString();
            List<Map> resultList = new ArrayList<>();
            List<Map> responseHR = getUserFromRedis(eventId);
            for (int i = 0; i < responseHR.size(); i++) {
                Map hrMap = responseHR.get(i);
                if("60002".equals(hrMap.get("type").toString())){
                    if(userId.equals(hrMap.get("userId"))){
                        if(readSituation.equals("已读")){
                            hrMap.put("respondTime",readTime);
                        }else if(readSituation.equals("未读")){
                            hrMap.put("respondTime","");
                        }
                        updateResponseHRRecordToDatabase(hrMap);
                    }
                }
                resultList.add(hrMap);
            }
            addUsersRedis(eventId,resultList);
            pushMessage("hasRespondSignIn",eventId);
        }
        return inInfo;
    }

    /**
     * 接收应急智能调度系统发送的应急处置记录
     * serviceId S_YJ_CZ_05
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo receiveHandleRecord(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        List<Map> data =(List) attr.get("data");

        for (int i = 0; i < data.size(); i++) {
            String recordUuid = getUuid();
            HashMap<Object, Object> map = new HashMap<>();
            String eventId = data.get(i).get("event_uuid").toString();
            map.put("eventId",data.get(i).get("event_uuid").toString());
            map.put("recordUuid",recordUuid);
            map.put("type",70002);
            map.put("person",getStrValue(data.get(i).get("post")));
            map.put("time",data.get(i).get("disposal_time").toString());
            map.put("content",data.get(i).get("disposal_content").toString());
            addHandleRecordoRedis(eventId, map);
            pushMessage("hasHandleRecordUpdate",eventId);
            insertHandleRecordToDatabase(map);
        }

        return inInfo;
    }

    //临时上传内网地址
//    private final String FILE_URL = "http://*************/ossrest/api/object/CMP/";
    private final String FILE_URL = "http://*************/ossrest/api/object/CMP/";

    public String bornFilePath(String path,String fileName){
        return FILE_URL+path+fileName+"?tenant=1";
    }

    public String bornDdFilePath(String path,String fileName){
        StringBuilder sb = new StringBuilder();
        String property = PlatApplicationContext.getProperty("iplat4j.admin.type");
        sb.append("http://*************/ossrest/api/object/").append(property).append("/").append(path).append("/").append(fileName).append("?tenant=1");
        return sb.toString();
    }

    //历史发布记录同步
    public void initInfoHistery(String eventId,Object eventRecords){
        try{
            EiInfo info = new EiInfo();
            Map<String, List> infoRecords = JSON.parseObject(eventRecords.toString(), new TypeReference<Map<String, List>>(){});
            List<String> nocc = infoRecords.get("nocc");
            List<String> occ = infoRecords.get("occ");
            List noccList = new ArrayList<>();
            List occList = new ArrayList<>();
            info.set("isList","1");
            if(nocc.size()>0){
                info.set("records",nocc);
                info.set(EiConstant.serviceId,"S_XF_FB_06");
                EiInfo outInfo = XServiceManager.call(info);
                noccList = (List)outInfo.get("data");
            }
            if(occ.size()>0){
                info.set("records",occ);
                info.set(EiConstant.serviceId,"S_XF_FB_06");
                EiInfo o2 = XServiceManager.call(info);
                occList = (List)o2.get("data");
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            List<Map> mapList = getHandleSituationFromRedis(eventId);
            for(int i=0;i<occList.size();i++){
                Map o = (Map)occList.get(i);
                Map<String,String> map = new HashMap<>();
                map.put("eventId",eventId);
                map.put("recordUuid",o.get("uuid").toString());
                map.put("name",getStrValue(o.get("publishName")));
                map.put("post",getStrValue(o.get("publishPost")));
                map.put("identity","OCC调度");
                map.put("dataTime",o.get("publishTime").toString());
                map.put("content",o.get("content").toString());
                map.put("source","OCC");
                map.put("headImg","");
                map.put("annex","[]");
                mapList.add(map);
            }

            for(int i=0;i<noccList.size();i++){
                Map o = (Map)noccList.get(i);
                HashMap<Object, Object> map = new HashMap<>();
                map.put("eventId",eventId);
                map.put("recordUuid",o.get("uuid").toString());
                map.put("name",getStrValue(o.get("publishName")));
                map.put("post","系统管理员");
                map.put("identity","NOCC");
                map.put("dataTime",o.get("publishTime").toString());
                map.put("content",o.get("content").toString());
                map.put("source","NOCC");
                map.put("annex","[]");
                map.put("headImg","");
                mapList.add(map);
            }

            List<Map> sortedList = mapList.stream()
                    .sorted(Comparator.comparing(map -> LocalDateTime.parse((String) map.get("dataTime"), formatter)))
                    .collect(Collectors.toList());
            for(Map ma: sortedList){
                insertHandleSituationToDatabase(ma);
            }
            insertHandleSituationToRedis(eventId,sortedList);
            pushMessage("hasHandleSituationUpdate",eventId);
        }catch (Exception e){
            logger.error("信息发布历史同步失败"+e);
        }
    }

    /**
     * 接收应急智能调度系统移动端发送的现场处置情况信息
     * serviceId S_YJ_CZ_06
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo receiveHandleSituationFromMobile(EiInfo inInfo){
        try {
            Map attr = inInfo.getAttr();
            String eventId = attr.get("event_uuid").toString();
            String recordUuid = "";
            if(attr.get("recordUuid")==null){
                recordUuid = getUuid();
            }else{
                recordUuid = attr.get("recordUuid").toString();
            }

            Object feedbackText = attr.get("feedback_text");
            if(feedbackText==null){
                feedbackText = "";
            }
            HashMap<Object, Object> map = new HashMap<>();
            map.put("eventId",eventId);
            map.put("recordUuid",recordUuid);
            map.put("name",attr.get("feedback_name").toString());
            map.put("post",getStrValue(attr.get("feedback_post")));
            map.put("identity",getStrValue(attr.get("feedback_identity")));
            map.put("dataTime",getCurrentTime());
            map.put("content",feedbackText.toString());
            map.put("source","APP");
            map.put("headImg","");

            ArrayList<Object> list = new ArrayList<>();
            Object fpicture = attr.get("feedback_picture");
            if(fpicture != null && !"".equals(fpicture.toString())){
                List<Map<String, String>> jsonList = JSONObject.parseObject(fpicture.toString(), List.class);
                for(Map<String, String> fileMap:jsonList){
                    String fileName = fileMap.get("fileName");
                    String picturePath = bornDdFilePath(fileMap.get("bucketName"),fileName);
                    HashMap<String, Object> tempMap = new HashMap<>();
                    tempMap.put("type","img");
                    tempMap.put("path",picturePath);
                    tempMap.put("bucketName",fileMap.get("bucketName"));
                    tempMap.put("fileName",fileName);
                    list.add(tempMap);
                }
            }

            Object fvideo = attr.get("feedback_video");
            if(fvideo != null && !"".equals(fvideo.toString())){
                List<Map<String, String>> jsonList = JSONObject.parseObject(fvideo.toString(), List.class);
                for(Map<String, String> fileMap:jsonList){
                    String fileName = fileMap.get("fileName");
                    String videoPath = bornDdFilePath(fileMap.get("bucketName"),fileName);
                    HashMap<Object, Object> tempMap = new HashMap<>();
                    tempMap.put("type","video");
                    tempMap.put("path",videoPath);
                    tempMap.put("bucketName",fileMap.get("bucketName"));
                    tempMap.put("fileName",fileName);
                    list.add(tempMap);
                }
            }

            map.put("annex",JSON.toJSONString(list));
            Object fhead = attr.get("head_sculpture");
            if(fhead != null){
                map.put("headImg",fhead.toString());
            }
            insertHandleSituationToDatabase(map);
            addHandleSituationToRedis(eventId,map);//情况加入缓存
            pushMessage("hasHandleSituationUpdate",eventId);
        }catch (Exception e){
            logger.error("消息添加失败：{}", e.getMessage());// 输出日志
        }
        return inInfo;
    }

    /* 从图片中提取base64数据信息*/
    public String getBase64Str(String str){
        String[] split = str.split(",");
        if(split.length>=2){
            return split[1];
        }
        return str;
    }

    /**
     * 接收应急智能调度系统OCC端发送的现场处置情况信息
     * serviceId S_YJ_CZ_07
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo receiveHandleSituationFromOCC(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("event_uuid").toString();
        String recordUuid = "";
        if(attr.get("recordUuid")==null){
            recordUuid = getUuid();
        }else{
            recordUuid = attr.get("recordUuid").toString();
        }

        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        map.put("recordUuid",recordUuid);
        map.put("name",attr.get("dispatch_name").toString());
        map.put("post", getStrValue(attr.get("dispatch_post")));
        map.put("identity", getStrValue(attr.get("dispatch_identity")));
        map.put("dataTime",getCurrentTime());
        map.put("content",attr.get("command_description").toString());
        map.put("source","OCC");

        ArrayList<Object> list = new ArrayList<>();
        Object fpicture = attr.get("feedback_picture");
        if(fpicture != null && !"".equals(fpicture.toString())){
            List<Map<String, String>> jsonList = JSONObject.parseObject(fpicture.toString(), List.class);
            for(Map<String, String> fileMap:jsonList){
                String fileName = fileMap.get("fileName");
                String picturePath = bornDdFilePath(fileMap.get("bucketName"),fileName);
                HashMap<String, Object> tempMap = new HashMap<>();
                tempMap.put("type","img");
                tempMap.put("path",picturePath);
                tempMap.put("bucketName",fileMap.get("bucketName"));
                tempMap.put("fileName",fileName);
                list.add(tempMap);
            }
        }
        map.put("annex",JSON.toJSONString(list));

        Object fhead = attr.get("head_sculpture");
        if(fhead != null && !"".equals(fhead.toString())){
            map.put("headImg",fhead.toString());
        }else{
            map.put("headImg","");
        }
        insertHandleSituationToDatabase(map);
        addHandleSituationToRedis(eventId,map);
        pushMessage("hasHandleSituationUpdate",eventId);
        return inInfo;
    }

    /**
     * 接收应急智能调度系统发送的事件结束信息
     * serviceId S_YJ_CZ_12
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo receiveEventEndFromOCC(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        String eventId = attr.get("event_uuid").toString();
        String eventSource = attr.get("category").toString();

        inInfo.set("eventId",eventId);
        inInfo.set("eventSource",Integer.parseInt(eventSource));
        inInfo.set("isDd","1");
        endEventBus(inInfo);

        return inInfo;
    }

    /**
     * 发送现场处置情况信息至应急智能调度系统
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo sendHandleSituationToAPP(EiInfo inInfo){
        Map attr = inInfo.getAttr();
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("event_uuid",attr.get("eventId").toString());
        eiInfo.set("command_time",getCurrentTime());
        eiInfo.set("command_description",attr.get("content").toString());
        eiInfo.set("dispatch_post",attr.get("post").toString());
        eiInfo.set("category",Integer.parseInt(attr.get("eventSource").toString()));
        eiInfo.set("recordUuid",attr.get("recordUuid").toString());
        if("1".equals(inInfo.getString("XFFB"))){
            eiInfo.set("info_range",2);
        }else{
            eiInfo.set("info_range",1);
        }
        eiInfo.set(EiConstant.serviceId, "S_YJ_ZN_01");
        EiInfo outInfo = XServiceManager.call(eiInfo);

        return outInfo;
    }

    /**
     * 发送重播指令至APP
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo sendRedialInstructToAPP(EiInfo inInfo){
        Map attr = inInfo.getAttr();

        EiInfo eiInfo = new EiInfo();
        eiInfo.set("event_uuid",attr.get("eventId").toString());
        eiInfo.set("category",Integer.parseInt(attr.get("eventSource").toString()));

        eiInfo.set(EiConstant.serviceId, "S_YJ_ZN_02");
        EiInfo outInfo = XServiceManager.call(eiInfo);

        return outInfo;
    }

    /**
     * 发送事件结束通知至智能应急调度系统
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo sendEventEndToAPP(EiInfo inInfo){
        Map attr = inInfo.getAttr();

        EiInfo eiInfo = new EiInfo();
        eiInfo.set("event_uuid",attr.get("eventId").toString());
        eiInfo.set("category",Integer.parseInt(attr.get("eventSource").toString()));

        eiInfo.set(EiConstant.serviceId, "S_YJ_ZN_03");
        EiInfo outInfo = XServiceManager.call(eiInfo);

        return outInfo;
    }

    //信息发布-主动查询  S_YJ_CZ_18
    public EiInfo mssSendBtn(EiInfo info){
        String eventSource = info.get("eventSource").toString();
        Map<String,String> map = new HashMap<>();
        EiInfo outInfo = new EiInfo();
        if("2".equals(eventSource)){
            LocalDate date = LocalDate.now();//获取当天日期。
            outInfo.set("inqu_status-0-drillEndDate",date.toString());
            outInfo.set("inqu_status-0-YLState","190007");//默认不查询表fd_disposal_t字段值为190007的数据
            outInfo.set("result-limit",1000);
            outInfo = super.query(outInfo, "YJYL01.query", null, false, null, "inqu_status", "result", "result");
            //生成序号
            List<Map<String,Object>> result = outInfo.getBlock("result").getRows();
            List<Map<String,Object>> backList = new ArrayList<>();
            for(Map<String,Object> mp:result){
                Map<String,Object> map1 = new HashMap<>();
                map1.put("eventId",mp.get("uuid"));
                map1.put("eventName",mp.get("drillName"));
                if("190003".equals(mp.get("status").toString())){
                    backList.add(map1);
                }
            }
            info.set("eventList",backList);
        }else{
            List query = dao.query("YJCZ02.queryEventMss", map);
            info.set("eventList",query);
        }
        return info;
    }

    /**
     * 提供智能调度文件生成接口（S_YJ_CZ_20）
     */
    public EiInfo importFileGen(EiInfo info){
        String bucketName = info.get("bucketName").toString();
        String newFileName = info.get("newFileName").toString();
        String base64 = info.get("base64").toString();
        writeFileToOSS(base64,newFileName,bucketName);
        info.setStatus(0);
        return info;
    }

    /**
     * 提供智能调度文件导出接口（S_YJ_CZ_21）
     */
    public EiInfo exportFileGen(EiInfo info){
        String bucketName = info.get("bucketName").toString();
        String fileName = info.get("fileName").toString();
        byte[] fileByOSS = getFileByOSS(bucketName, fileName);
        String base64 = Base64.getEncoder().encodeToString(fileByOSS);
        info.set("base64",base64);
        return info;
    }

    /**
     * 原子操作,根据事件ID查询响应人员表中记录
     * @param eventId String 数据列表
     */
    public List<Map> queryResponseHRRecordFromDatabase(String eventId){
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId", eventId);
        return (List<Map>) dao.query("YJCZ02.queryResponseHRRecord", map);
    }

    /**
     * 原子操作,根据事件ID查询处置记录表中记录
     * @param eventId String 数据列表
     */
    public List<Map> queryHandleRecordFromDatabase(String eventId){
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        return (List<Map>) dao.query("YJCZ02.queryHandleRecord", map);
    }

    public List<Map> queryHandleRecordFromDatabaseTest(EiInfo eiInfo){
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId", eiInfo.get("eventId"));
        return (List<Map>) dao.query("YJCZ02.queryHandleRecord", map);
    }

    /**
     * 原子操作,根据事件ID查询指令记录表中记录
     * @param eventId String 数据列表

     */
    public List<Map> queryCommanderChangeFromDatabase(String eventId){
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        return (List<Map>) dao.query("YJCZ02.queryCommanderChange", map);
    }

    /**
     * 原子操作,根据事件ID查询现场处置情况记录表中记录
     * @param eventId String 数据列表

     */
    public List<Map> queryHandleSituationFromDatabase(String eventId){
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        return (List<Map>) dao.query("YJCZ02.queryHandleSituation", map);
    }

    /**
     * 原子操作,根据事件ID查询处置步骤表中记录
     * @param eventId String 数据列表

     */
    public List<Map> queryPlanStepFromDatabase(String eventId){
        HashMap<Object, Object> map = new HashMap<>();
        map.put("eventId",eventId);
        return (List<Map>) dao.query("YJCZ02.queryPlanStep", map);
    }


    /**
     * 原子操作,向响应人员表中插入一条记录
     * @param data Map 数据列表

     */
    public void insertResponseHRRecordToDatabase(Map data){
        dao.insert("YJCZ02.insertResponseHRRecord",data);
    }
    /**
     * 原子操作,向响应人员表中插入一吨记录
     * @param data Map 数据列表
     */
    public void insertResponseHRRecordToDatabase(List data){
        if(data.size()>0){
            dao.insert("YJCZ02.insertResponseHRRecordList",data);
        }
    }

    /**
     * 原子操作,向处置记录表中插入记录
     * @param data Map 数据列表
     */
    public void insertHandleRecordToDatabase(Map<Object, Object> data){
        dao.insert("YJCZ02.insertHandleRecord",data);
    }

    /**
     * 原子操作,向指令记录表中插入记录
     * @param data Map 数据列表

     */
    public void insertCommanderChangeToDatabase(Map data){
        dao.insert("YJCZ02.insertCommanderChange",data);
    }

    /**
     * 原子操作,向现场处置情况记录表中插入记录
     * @param data Map 数据列表
     */
    public void insertHandleSituationToDatabase(Map data){
        dao.insert("YJCZ02.insertHandleSituation",data);
    }

    /**
     * 原子操作,向处置步骤表中插入记录
     * @param data Map 数据列表
     */
    public void insertPlanStepToDatabase(Map data){
        dao.insert("YJCZ02.insertPlanStep",data);
    }

    /**
     * 原子操作,批量插入应急步骤
     * @param data Map 数据列表
     */
    public void insertPlanStepsToDatabase(List data){
        if(data.size()>0){
            dao.insert("YJCZ02.insertPlanStepList",data);
        }
    }

    /**
     * 原子操作,更新响应人员表中一条记录
     * @param data Map 数据列表
     */
    public void updateResponseHRRecordToDatabase(Map data){
        dao.update("YJCZ02.updateResponseHRRecord",data);
    }

    /**
     * 原子操作,更新处置步骤表中一条记录
     * @param data Map 数据列表
     */
    public void updatePlanStepToDatabase(Map data){
        dao.update("YJCZ02.updatePlanStep",data);
    }



    /**
     * 从数据库读取响应人员信息写入缓存,并返回相应数据
     */
    public List<Map> rereadResponseHRToRedis(String eventId){
        List<Map> maps = queryResponseHRRecordFromDatabase(eventId);
        return maps;
    }

    /**
     * 从数据库读取处置记录信息写入缓存,并返回相应数据

     */
    public List<Map> rereadHandleRecordToRedis(String eventId){
        List<Map> maps = queryHandleRecordFromDatabase(eventId);
//        insertListToRedis(eventId+"Handle",maps);
        return maps;
    }

    /**
     * 从数据库读取负责人变更记录信息写入缓存,并返回相应数据
     */
    public List<Map> rereadCommanderChangeToRedis(String eventId){
        List<Map> maps = queryCommanderChangeFromDatabase(eventId);
//        insertListToRedis(eventId+"commander",maps);
        return maps;
    }

    /**
     * 从数据库读取现场处置情况信息写入缓存,并返回相应数据
     */
    public List<Map> rereadHandleSituationToRedis(String eventId){
        List<Map> maps = queryHandleSituationFromDatabase(eventId);
//        insertListToRedis(eventId+"Situation",maps);
        return maps;
    }

    /**
     * 从数据库读取处置预案信息写入缓存,并返回相应数据
     */
    public List<Map> rereadPlanStepToRedis(String eventId){
        List<Map> maps = queryPlanStepFromDatabase(eventId);
//        insertListToRedis(eventId+"PlanStep",maps);
        return maps;
    }



    //消息ID下保存的消息标志
    private Map<String,Set<String>> messageContainer  = new HashMap<String,Set<String>>();
    //保存的消息ID列表
    private List<String> messageUserList = new ArrayList<String>();
    //保存的最近连接的消息ID
    private List<String> connectMessageUserRecord = new ArrayList<String>();
    //后端是否有新消息产生标志符
    private boolean hasMessage = false;
    //一次消息产生后可供读取次数，通常设置为缓存中消息ID数的2倍
    private int messageCount = 0;

    //获取当前物理IP
    public EiInfo getIPInfo(EiInfo info){
        String ip  = IPS[0];
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();

            outerLoop:
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()){
                    InetAddress inetAddress = inetAddresses.nextElement();
                    String hostAddress = inetAddress.getHostAddress();
                    if(IPS[0].equals(hostAddress) || IPS[1].equals(hostAddress)){
                        ip  = hostAddress;
                        break outerLoop;
                    }
                }
            }

        } catch (Exception e) {
            //固定IP保证手动刷新
            ip  = IPS[0];
        }
        info.set("ip", ip);
        return info;
    }

    /**
     * 添加消息标志
     * 当前已使用消息标准：
     */
    public void pushMessage(String message,String eventId){
        EiInfo info = new EiInfo();
        Map<String,String> parMap = new HashMap<>();
        parMap.put("key",message);
        parMap.put("eventId",eventId);
        info.set("data",parMap);
        wsMes(info);
    }

    /**
     * 添加消息标志，同上，添加消息ID字段参数，添加消息标准时会屏蔽该ID
     * 使用场景为，由本ID前端发起的消息，前端无需再次获取，为避免重复操作，屏蔽该ID
     * 当前已使用消息标准：
     */
    public void pushMessage(String message,String messageUser,String eventId){
        hasMessage = true;
        messageCount = messageUserList.size()*2;
        for (int i = 0; i < messageUserList.size(); i++) {
            if(messageUser.equals(messageUserList.get(i))){
                continue;
            }
            messageContainer.get(messageUserList.get(i)).add(message);
        }
    }

    /**
     * 前端获取消息ID
     */
    public EiInfo getMessageUser(EiInfo inInfo){
        String uuid = getUuid();
        addMessageUser(uuid);
        inInfo.set("messageUser",uuid);
        return inInfo;
    }

    /**
     * 后端缓存中添加新消息ID
     */
    public void addMessageUser(String messageUser){
        messageContainer.put(messageUser,new HashSet<>());
        messageUserList.add(messageUser);
        connectMessageUserRecord.add(messageUser);
    }

    /**
     * 清理已离线的消息ID
     * 离线判断逻辑：保存最近100次访问的消息ID，若缓存中存在某ID而近100次访问中无此ID则认定为已离线
     */
    public void clearOfflineMessageUser(){
        for (int i = 0; i < messageUserList.size(); i++) {
            if(!connectMessageUserRecord.contains(messageUserList.get(i))){
                messageContainer.remove(messageUserList.get(i));
                messageUserList.remove(i);
                i--;
            }
        }
        connectMessageUserRecord.clear();
    }

    /**
     * 供前端轮询调用，获取消息ID下保存的全部消息标志
     */
    public EiInfo getMessage(EiInfo inInfo){
        //若有新消息则查询消息并返回，若无新消息直接略过
        if(hasMessage){
            String messageUser = inInfo.getString("messageUser");
            //判断前端传入的消息ID是否在活跃ID列表中
            if(messageContainer.containsKey(messageUser)){
                inInfo.set("hasMessage",true);
                Set<String> messageSet = messageContainer.get(messageUser);
                //读取保存的消息标志
                for(String ST : messageSet){
                    inInfo.set(ST,true);
                }
                //消息可供读取次数减一
                messageCount--;
                //清空该ID下保存的消息标志，避免一个消息重复读取，导致前端执行重复操作
                messageSet.clear();
            }
            //若消息ID不在活跃列表中，判断为某页面断线重连
            //可能是页面因网络断线后又接上，期间后台保存的ID被清除且页面未刷新（刷新页面会获取新ID）
            else {
                //重新将ID添加至活跃ID列表
                addMessageUser(messageUser);
                inInfo.set("hasMessage",true);
                //返回一个特殊消息标志，表示重新与后台获得连接
                inInfo.set("reconnect",true);
            }
        }
        //保存本次请求的消息ID
        connectMessageUserRecord.add(inInfo.getAttr().get("messageUser").toString());
        //若保存的最近请求的消息ID数大于100(根据需要配置)，则检查消息ID的在线情况并清除不在线的消息ID
        if(connectMessageUserRecord.size() >= 100){
            clearOfflineMessageUser();
        }
        //若消息可供读取次数小于0，则认定本次新消息已被读取完成，将有新消息标志设置为假
        if(messageCount < 0){
            hasMessage = false;
            messageCount = 0;
        }
        return inInfo;
    }

    /* 刷新消息队列 */
    public EiInfo refreshPushMessage(EiInfo info){
        String mss = info.getString("mss");
        pushMessage(mss,info.getString("eventId"));
        return info;
    }

    /**
     * 更新响应人员列表
     * @param eventInfo Map 数据列表
     */
    public void refreshHR(String eventId ,Map eventInfo){
        List<Map> responseHRList = queryMsg(eventId,eventInfo);//调用事件、演练接口
        //查询数据库通讯人员信息
        //List<Map> hrTimeList = dao.query("YJCZ02.queryResponseHRTime",eventInfo);
        //List<Map> hrTimeList = queryResponseHRRecordFromDatabase(eventId);
        List<Map> hrTimeList = getUserFromRedis(eventId);
        //后删除
        dao.delete("YJCZ02.delResponseHR",eventInfo);

        Map<String,Map> phoneMap = new HashMap<>();
        Map<String,Map> mesMap = new HashMap<>();

        for(Map mp:hrTimeList){
            if("60001".equals(mp.get("type").toString())){
                phoneMap.put(mp.get("userId").toString(),mp);
            }else{
                mesMap.put(mp.get("userId").toString(),mp);
            }
        }
        //处理好的数据集
        List<Map> resuList = new ArrayList<>();
        for(Map mp:responseHRList){
            String type = mp.get("type").toString();
            String key = mp.get("userId").toString();
            if("60001".equals(type)){
                if(phoneMap.containsKey(key)){
                    Map userMap = phoneMap.get(key);
                    mp.put("noticeTime",userMap.get("noticeTime").toString());
                    mp.put("respondTime",userMap.get("respondTime").toString());
                    mp.put("arrivalTime",userMap.get("arrivalTime").toString());
                }else{
                    mp.put("noticeTime","");
                    mp.put("respondTime","");
                    mp.put("arrivalTime","");
                }
            }else {
                if (mesMap.containsKey(key)) {
                    Map userMap = mesMap.get(key);
                    mp.put("noticeTime", userMap.get("noticeTime").toString());
                    mp.put("respondTime", userMap.get("respondTime").toString());
                    mp.put("arrivalTime", userMap.get("arrivalTime").toString());
                } else {
                    mp.put("noticeTime", "");
                    mp.put("respondTime", "");
                    mp.put("arrivalTime","");
                }
            }
            resuList.add(mp);
        }
        insertResponseHRRecordToDatabase(resuList);
        addUsersRedis(eventId,resuList);
        pushMessage("hasResponseHRUpdate",eventId);
    }

    /**
     * 暴露给应急事件或应急演练的接口事件--他们修改事件，由此接口通知我(S_YJ_CZ_17)
     */
    public EiInfo refershEvent(EiInfo info){
        String eventId = info.getString("eventId");
        try {
            String editHR = info.getString("editHR");
            Map map = queryEventFromRedis(eventId);
            if(map != null){
                //原等级
                String oldLevel = map.get("eventLevel").toString();
                Map<Object, Object> eventInfo = initEventInfo(info);
                eventInfo.put("commander",map.get("commander"));
                eventInfo.put("commanderTel",map.get("commanderTel"));
                eventInfo.put("director",map.get("director"));
                eventInfo.put("directorTel",map.get("directorTel"));
                insertEventToRedis(eventId,eventInfo);
                if("0".equals(editHR)){
                    refreshHR(eventId,eventInfo);
                }
                pushMessage("refershEvent",eventId);
                //最新等级
                String newLevel = eventInfo.get("eventLevel").toString();
                if(!oldLevel.equals(newLevel)){
                    String editUser = info.getString("editUser");
                    if(editUser != null && !"".equals(editUser)){
                        addHandleRecord(eventId,"事件等级由"+eventLevelEnumDict.get(oldLevel)+
                                "变更为"+eventLevelEnumDict.get(newLevel),70001,editUser);
                    }
                }
            }
        }catch (Exception e){
            addHandleRecord(eventId,"事件修改失败！",70001,"系统管理员");
        }

        return info;
    }

    /**
     * 检测到事件有变动，前端刷新数据
     * @param info
     * @return
     */
    public EiInfo refershCallBack(EiInfo info){
        String eventId = info.getString("eventId");
        Map map = queryEventFromRedis(eventId);
        info.set("event",map);
        return info;
    }


    private boolean isEmit(Object o) {
        if (o == null) {
            return true;
        }
        if (o instanceof String) {
            String str = (String) o;
            return str.isEmpty();
        }
        return false;
    }


    /**
     * @description: 调用微服务方法
     * @param inInfo serviceId
     * @return outInfo
     */
    public EiInfo callXService(EiInfo inInfo, String serviceId){
        inInfo.set(EiConstant.serviceId, serviceId);
        EiInfo outInfo = XServiceManager.call(inInfo);
        //注意必须对outInfo的status状态进行校验
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }

    /**
     * 将聊天文件流写进oss(方案一：同步)
     * @param base64 传进来的数据值
     * @param fileName 文件名称（包含类型）
     */
    private void writeFileToOSS(String base64,String fileName,String ossPath){
        try {
            // base64编码转字节流
            byte[] bytes =Base64.getDecoder().decode(base64);
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", bytes);
            fileInfo.set("bucketName", ossPath);
            fileInfo.set("newFileName", fileName);
            callXService(fileInfo, "S_RF_03");
        }catch (Exception e){
            throw new PlatException("文件存储异常");
        }
    }

    private void writeFileToFileServe(String base64, String fileName){
        try {
            // base64编码转字节流
            byte[] bytes =Base64.getDecoder().decode(base64);
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", bytes);
            fileInfo.set("fileName", fileName);
            fileInfo.set("path","应急处置/");
            callXService(fileInfo, "S_RF_02");
        }catch (Exception e){
            throw new PlatException("文件存储异常");
        }
    }

    private byte[] getFileByOSS(String ossPath,String fileName){
        EiInfo fileInfo = new EiInfo();
        fileInfo.set("bucketName", ossPath);
        fileInfo.set("fileName", fileName);
        EiInfo outInfo = callXService(fileInfo, "S_RF_04");
        byte[] pictureByte = outInfo.toJSON().getBytes("fileData");
        return pictureByte;
    }

    /*文件从oss流向fileServe*/
    public EiInfo ossToFileServe(EiInfo info){
        try {
            String path = info.getString("path");
            String name = info.getString("name");
            byte[] bytes = getFileByOSS(path, name);
            String fileName = getTimeStamp()+"."+name.split(".")[1];
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", bytes);
            fileInfo.set("fileName", fileName);
            fileInfo.set("path","应急处置/");
            callXService(fileInfo, "S_RF_02");
        }catch (Exception e){
            info.setStatus(EiConstant.STATUS_FAILURE);
            throw new PlatException("文件存储异常");
        }
        info.setStatus(EiConstant.STATUS_SUCCESS);
        return info;
    }

    public EiInfo getFileFromFileServe(EiInfo info) {
        info.set(EiConstant.serviceId, "S_RF_01");
        EiInfo outInfo = XServiceManager.call(info);
        return outInfo;
    }

    /**
     * 将聊天文件流写进oss(方案一：同步)
     * @param base64 传进来的数据值
     * @param fileName 文件名称
     * @param fileType 文件类型
     */
    private void writeFileToOSS(String base64,String fileName,String fileType,String a){
        try {
            // base64编码转字节流
            byte[] bytes =Base64.getDecoder().decode(base64);
            // 拼接文件地址
            String filePath = fileName + "." + fileType;
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", bytes);
            fileInfo.set("bucketName","CMP");
            fileInfo.set("newFileName", filePath);
            callXService(fileInfo, "S_RF_03");
        }catch (Exception e){
            throw new PlatException("文件存储异常");
        }
    }

    public static <T> List<Map<String, Object>> toListMap(List<T> list){
        List<Map<String, Object>> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSONObject.toJSONString(item), new TypeReference<Map<String, Object>>(){})));
        return result;
    }

    public boolean isEmpty(Object obj){
        return obj == null || "".equals(obj.toString());
    }

    public boolean mapIsEmpty(Map obj){
        return obj == null || obj.isEmpty();
    }

    //读取当前登录人中文名
    public EiInfo getCurRedis(EiInfo info){
        String ckxEventImpact = getValRedis("ckxEventImpact");
        info.set("ckxEventImpact",ckxEventImpact);
        return info;
    }
    /*
    * 获取当前系统类型，区分测试环境与正式环境
    * */
    public EiInfo getSystemType(EiInfo info){
        String eventId = info.getString("eventId")+"@"+info.getString("eventSource");
        saveToRedis("curevent",eventId);
        info.set("systemType",PlatApplicationContext.getProperty("iplat4j.admin.type"));
        return info;
    }

    //临时7楼大屏切换方案
    public EiInfo lsChangeDP(EiInfo info){
        try {
            Jedis jedis = new Jedis("***********",6379);
            jedis.set("sjModelState",info.getString("sjModelState"));
            jedis.close();
            info.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e){
            info.setStatus(EiConstant.STATUS_FAILURE);
        }
        return info;
    }


    public EiInfo getCurEvent(EiInfo info){
        String curevent = getValRedis("curevent");
        String[] split = curevent.split("@");
        info.set("eventId",split[0]);
        info.set("eventSource",split[1]);
        return info;
    }

    //初始化大屏应急投递
    public EiInfo addCurDpEvent(EiInfo info){
        //物资图层、人员定位图层
        List<String> model = new ArrayList<String>(){{add("material");add("person");}};
        Map<String,Object> map = new HashMap<>();
        map.put("eventId",info.getString("eventId"));
        map.put("eventSource",info.getString("eventSource"));
        map.put("layerArr",model);
        saveToRedis("curDPSJ",JSON.toJSONString(map));
        info.set("systemType",PlatApplicationContext.getProperty("iplat4j.admin.type"));
        return info;
    }

    //提供GIS大屏事件信息 S_YJ_CZ_31
    public EiInfo curDpToGis(EiInfo info){
        String curevent = getValRedis("curDPSJ");
        if(curevent !=null) {
            Map map = JSONObject.parseObject(curevent, Map.class);
            info.setAttr(map);
        }
        return info;
    }

    //读取大屏应急基本信息
    public EiInfo getCurDpEvent(EiInfo info){
        String curevent = getValRedis("curDPSJ");
        if(curevent !=null){
            Map map = JSONObject.parseObject(curevent, Map.class);
            String eventId = map.get("eventId").toString();
            Map eventInfo = queryEventFromRedis(eventId);
            String levl = eventLevelEnumDict.get(eventInfo.get("eventLevel").toString());

            info.set("eventId",eventId);
            info.set("lineId",eventInfo.get("lineId"));
            info = getEventImpact(info);
            info.set("eventName",eventInfo.get("eventName"));
            info.set("eventLevel",levl);
            info.set("eventTime",eventInfo.get("eventTime"));
            info.set("eventPlace",eventInfo.get("eventPlace"));
            info.set("eventDisc",eventInfo.get("eventDisc"));
            info.set("eventDisc",eventInfo.get("eventDisc"));
        }
        return info;
    }

    //文件迁移接口
    public EiInfo removeFileGen(EiInfo info){
        List<Map> mapList = (List<Map>)info.get("flist");

        String ossPath = info.get("ossPath").toString();
        for(Map mp:mapList){
            String bucketName = mp.get("bucketName").toString();
            String fileName = mp.get("fileName").toString();
            byte[] fileByOSS = getFileByOSS(bucketName, fileName);
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", fileByOSS);
            fileInfo.set("bucketName", ossPath);
            fileInfo.set("newFileName", fileName);
            callXService(fileInfo, "S_RF_03");
        }
        info.set("succe","succe---");
        return info;
    }

    //信息广播
    public EiInfo wsMes(EiInfo info){
        info.set("topic", "pcwsckx");
        //开启新的事务
        info.set(EiConstant.serviceName, "RTMQ02");
        info.set(EiConstant.methodName, "sendWSMsg");
        EiInfo eiInfo = XLocalManager.callNewTx(info);
        return info;
    }

    //心跳检测
    public EiInfo heartCheck(EiInfo info){
        Map<String,String> parMap = new HashMap<>();
        parMap.put("key","heart");
        parMap.put("hcode",info.getString("hcode"));
        info.set("data",parMap);
        wsMes(info);
        return info;
    }

    //定时缓存数据入redis TASK_YJ_CZ_01-S_YJ_CZ_24
    public EiInfo taskCache(EiInfo info){
        try {
            Map<String,String> parMap = new HashMap<>();
            parMap.put("key","kafa");
            info.set("data",parMap);
            wsMes(info);
            String s = JSONObject.toJSONString(kafkaCache);
            saveToRedis("ckxEventImpact",s);
        }catch (Exception e){
            logger.error("列车事件影响定时缓存错误");
        }
        return info;
    }

    //提供信息发布读取事件模板、通知人员信息接口（S_YJ_CZ_25）
    public EiInfo xfFb1(EiInfo info){
        String eventId = info.getString("eventId");
        String eventSource = info.getString("eventSource");
        Map<String,String> map = new HashMap<>();
        map.put("eventId",eventId);
        if("1".equals(eventSource)){
            map.put("yjsj","1");
        }else{
            map.put("yjyl","1");
        }
        List<Map> queryXFFB = dao.query("YJCZ02.queryXFFB", map);
        info.set("dataList",queryXFFB.get(0));
        return info;
    }


    /**
     * 接收应急智能调度系统现场处置情况信息删除命令
     * serviceId S_YJ_CZ_26
     * @param inInfo EiInfo 数据列表
     */
    public EiInfo rmoveHandleSituation(EiInfo inInfo) {
        try {
            String recordUuid = inInfo.getString("recordUuid");
            String eventId = inInfo.getString("event_uuid");
            HashMap<Object, Object> map = new HashMap<>();
            map.put("recordUuid",recordUuid);
            map.put("eventId",eventId);
            int delHandleSituation = dao.delete("YJCZ02.delHandleSituation", map);
            List<Map> mapList = getHandleSituationFromRedis(eventId);
            List<Map> filteredList = mapList.stream()
                    .filter(m -> !(recordUuid.equals(m.get("recordUuid").toString())&&eventId.equals(m.get("eventId").toString())))
                    .collect(Collectors.toList());
            insertHandleSituationToRedis(eventId, filteredList);
            inInfo.set("msg","删除成功");
            pushMessage("hasHandleSituationUpdate",eventId);
        }catch (Exception e){
            inInfo.set("msg","删除失败"+e);
            logger.error("消息添加失败：{}", e.getMessage());// 输出日志
        }
        return inInfo;
    }

    //根据事件编号读取应急事件或应急演练全部数据（S_YJ_CZ_27）
    public EiInfo lyf01(EiInfo info){
        String eventId = info.getString("eventId");
        String eventSource = info.getString("eventSource");
        Map<String,String> map = new HashMap<>();
        map.put("eventId",eventId);
        if("1".equals(eventSource)){
            map.put("yjsj","1");
        }else{
            map.put("yjyl","1");
        }
        List<Map> queryXFFB = dao.query("YJCZ02.queryXFFB2", map);
        info.set("dataList",queryXFFB);
        return info;
    }

    // NOCC消息删除（S_YJ_CZ_28）
    public EiInfo infoDelete(EiInfo info) {
        String eventId = info.getString("event_uuid");
        String recordUuid = info.getString("recordUuid");

        HashMap<Object, Object> map = new HashMap<>();
        map.put("recordUuid",recordUuid);
        map.put("eventId",eventId);
        int delHandleSituation = dao.delete("YJCZ02.delHandleSituation", map);
        List<Map> mapList = getHandleSituationFromRedis(eventId);
        List<Map> filteredList = mapList.stream()
                .filter(m -> !(recordUuid.equals(m.get("recordUuid").toString())&&eventId.equals(m.get("eventId").toString())))
                .collect(Collectors.toList());
        insertHandleSituationToRedis(eventId, filteredList);
        pushMessage("hasHandleSituationUpdate",eventId);

        info.set(EiConstant.serviceId, "S_YJ_CZ_28");
        EiInfo outInfo = XServiceManager.call(info);
        outInfo.set("msg","删除成功");
        return info;
    }

    // 初始化来自OCC的发起事件(S_YJ_CZ_29)
    public EiInfo occSaveInit(EiInfo info) throws InterruptedException {
        String eventId = info.getString("eventId");
        info.set("eventSource","1");
        getEvent(info);
        saveToRedis("occ"+eventId,"1");
        return info;
    }

    // 读取OCC事件状态
    public EiInfo occGetInit(EiInfo info){
        String eventId = info.getString("eventId");
        String valRedis = getValRedis("occ" + eventId);
        info.set("occState",valRedis);
        return info;
    }

    public EiInfo delOcc(EiInfo info){
        String eventId = info.getString("eventId");
        delReisKeys("occ"+eventId);
        Map<String,String> parMap = new HashMap<>();
        parMap.put("key","occ");
        parMap.put("eventId","eventId");
        info.set("data",parMap);
        wsMes(info);
        addHandleRecord(eventId,info.getString("user")+"：点击介入处置",70001,info.getString("user"));
        return info;
    }

    /**
     * 原子操作,根据事件ID查询缓存中事件信息
     * @param eventId String 数据列表
     */
    public Map queryEventFromRedis(String eventId){
        String valRedis = getValRedis(PrefixPattern.EPATTERN.getPattern() + eventId);
        if(valRedis != null){
            return JSON.parseObject(valRedis, Map.class);
        }else {
            return null;
        }
    }
    /**
     * 原子操作,向缓存中插入一条事件信息
     * @param eventInfo Map 数据列表
     */
    public void insertEventToRedis(String key,Map eventInfo){
        saveToRedis(PrefixPattern.EPATTERN.getPattern()+key,JSON.toJSONString(eventInfo));
    }

    /*初始化现场处置情况*/
    public void initHandleSituationToRedis(String eventId){
        List<Map> mapList = getHandleSituationFromRedis(eventId);
        if(mapList.size()==0){
            List<Map> handleSituationList = queryHandleSituationFromDatabase(eventId);
            if(handleSituationList.size()>0){
                insertHandleSituationToRedis(eventId,handleSituationList);
            }else{
                List<Map> newList = new ArrayList<>();
                insertHandleSituationToRedis(eventId,newList);
            }
        }
    }
    /*新增现场处置情况-单个*/
    public void addHandleSituationToRedis(String eventId,Map<Object, Object> map){
        List<Map> mapList = getHandleSituationFromRedis(eventId);
        mapList.add(map);
        insertHandleSituationToRedis(eventId,mapList);
    }
    /*新增现场处置情况-多个*/
    public void insertHandleSituationToRedis(String eventId,List<Map> mapList){
        saveToRedis(PrefixPattern.MPATTERN.getPattern()+eventId,JSON.toJSONString(mapList));
    }
    //读取处置情况数据来自缓存
    public List<Map> getHandleSituationFromRedis(String eventId){
        String valRedis = getValRedis(PrefixPattern.MPATTERN.getPattern()+eventId);
        if(valRedis==null){
            return new ArrayList<>();
        }
        return JSONObject.parseObject(valRedis, new TypeReference<List<Map>>() {});
    }

    /*初始化处置记录*/
    public void initHandleRecord(String eventId) {
        List<Map> mapList = getHandleRecordFromRedis(eventId);
        if(mapList.size() == 0) {
            List<Map> handleRecord = queryHandleRecordFromDatabase(eventId);
            saveHandleRecordToRedis(eventId, handleRecord);
        }
    }

    /*新增现场处置记录-单个*/
    public void addHandleRecordoRedis(String eventId,Map<Object, Object> map) {
        List<Map> mapList = getHandleRecordFromRedis(eventId);
        mapList.add(map);
        saveHandleRecordToRedis(eventId, mapList);
    }

    public void saveHandleRecordToRedis(String key, List<Map> mapList) {
        saveToRedis(PrefixPattern.RPATTERN.getPattern() + key, JSON.toJSONString(mapList));
    }

    //读取处置记录数据来自缓存
    public List<Map> getHandleRecordFromRedis(String eventId){
        String valRedis = getValRedis(PrefixPattern.RPATTERN.getPattern()+eventId);
        if(valRedis==null){
            return new ArrayList<>();
        }
        return JSONObject.parseObject(valRedis, new TypeReference<List<Map>>() {});
    }

    //新增人员响应情况
    public void addUsersRedis(String eventId, List<Map> mapList){
        saveToRedis(PrefixPattern.UPATTERN.getPattern()+eventId,JSON.toJSONString(mapList));
    }

    //读取人员响应情况
    public List<Map> getUserFromRedis(String eventId){
        String valRedis = getValRedis(PrefixPattern.UPATTERN.getPattern()+eventId);
        if(valRedis==null){
            return new ArrayList<>();
        }
        return JSONObject.parseObject(valRedis,new TypeReference<List<Map>>() {});
    }

    //临时员响应情况
    public void addLUsersRedis(String eventId, List<Map> mapList){
        saveToRedis(PrefixPattern.LPATTERN.getPattern()+eventId,JSON.toJSONString(mapList));
    }

    //读取临时人员响应情况
    public List<Map> getLUserFromRedis(String eventId){
        String valRedis = getValRedis(PrefixPattern.LPATTERN.getPattern()+eventId);
        if(valRedis==null){
            return new ArrayList<>();
        }
        return JSONObject.parseObject(valRedis,new TypeReference<List<Map>>() {});
    }


    public String getEventStateRedis(String eventId){
        return getValRedis(PrefixPattern.SPATTERN.getPattern()+eventId);
    }

    public void addEventStateToRedis(String key,String state){
        saveToRedis(PrefixPattern.SPATTERN.getPattern()+key,state);
    }

    public boolean getInfoHisteryRedis(String eventId){
        String valRedis = getValRedis(PrefixPattern.IPATTERN.getPattern()+eventId);
        if(valRedis==null){
            return true;
        }
        return false;
    }

    public void addInfoHisterToRedis(String eventId){
        saveToRedis(PrefixPattern.IPATTERN.getPattern()+eventId,"ILY");
    }

    public void delReisKey(String key){
        Jedis jedis = pool.getResource();
        try {
            jedis.del(key);
        }catch (Exception e){
            logger.error("缓存清除失败");
        }finally {
            close(jedis);
        }
    }

    public void delReisKeys(String... keys){
        Jedis jedis = pool.getResource();
        try {
            jedis.del(keys);
        }catch (Exception e){
            logger.error("缓存清除失败");
        }finally {
            close(jedis);
        }
    }

    public void removeEventCach(String eventId){
        try {
            List<PrefixPattern> patterns = Arrays.asList(
                    PrefixPattern.EPATTERN,
                    PrefixPattern.MPATTERN,
                    PrefixPattern.RPATTERN,
                    PrefixPattern.UPATTERN,
                    PrefixPattern.SPATTERN,
                    PrefixPattern.KPATTERN,
                    PrefixPattern.LPATTERN,
                    PrefixPattern.IPATTERN,
                    PrefixPattern.DPATTERN
            );
            String[] keyPatterns = patterns.stream()
                    .map(pattern -> pattern.getPattern() + eventId)
                    .toArray(String[]::new);
            delReisKeys(keyPatterns);
        }catch(Exception e){
            logger.error("缓存清除失败");
        }
    }

    public EiInfo removeEventCach(EiInfo info){
        Object e1 = info.get("eventId");
        Jedis jedis = pool.getResource();
        try {
            if(e1 == null){
                List<String> patterns = Arrays.asList(PrefixPattern.E_PATTERN.getPattern(),
                        PrefixPattern.M_PATTERN.getPattern(),PrefixPattern.R_PATTERN.getPattern(),PrefixPattern.U_PATTERN.getPattern(),PrefixPattern.S_PATTERN.getPattern());
                for (String pattern : patterns) {
                    String cursor = "0";
                    do {
                        ScanParams params = new ScanParams().match(pattern).count(100);
                        ScanResult<String> scanResult = jedis.scan(cursor, params);
                        List<String> keysToDelete = scanResult.getResult();
                        if (!keysToDelete.isEmpty()) {
                            jedis.del(keysToDelete.toArray(new String[0]));
                        }
                        cursor = scanResult.getStringCursor();
                    } while (!cursor.equals("0"));
                }
            }else{
                removeEventCach(e1.toString());
            }
        }catch (Exception e){
            logger.error("缓存清除失败");
        }finally {
            close(jedis);
        }
        return info;
    }

    public EiInfo getCachs(EiInfo info){
        String e1 = info.get("eventId").toString();
        info.set("likelyRedis",queryEventFromRedis(e1));
        info.set("msgRedis",getHandleSituationFromRedis(e1));
        info.set("recordRedis",getHandleRecordFromRedis(e1));
        info.set("userRedis",getUserFromRedis(e1));
        info.set("kafkaCache",kafkaCache);
        int numActive = pool.getNumActive();
        int numIdle = pool.getNumIdle();
        String pool = "--["+numActive+","+numIdle+"]--";
        info.set("pool",pool);
        return info;
    }

}


