package com.baosight.cmp.yj.yg.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baosight.cmp.common.CYUtils;
import com.baosight.cmp.common.util.eiinfo.EiInfoUtils;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ei.it.domain.EIIT00;
import gudusoft.gsqlparser.EIndexType;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class ServiceYJYG01 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    public String tranLineIdToName(int a){
        List<Map<String,String>> result = CYUtils.queryCode("nocc.yj.yg01");
        String fin = "未找到匹配线路名";
        for(int i=0;i<result.size();i++){
            int b = Integer.valueOf(result.get(i).get("value"));
            if(a==b){
                fin = result.get(i).get("label");
            }
        }
        return fin;
    }

    /**
     * getPlanTree01
     * 生成数字化预案tab页的树
     *
     * @param inInfo*
     * @return *
     */
    public EiInfo getPlanTree01(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        // EiConstant.queryBlock => inqu_status,表示通用查询blockId名称
        // "node" => 表示一次加载结点名称，在前端的ROOT中定义其值
        String blockId = inInfo.getString(EiConstant.queryBlock + EiConstant.separator+"0-node");
//        String blockId = "tree01";


        // 返回的的blockId的名称是传入的结点其值
        // 如：inqu_status-0-node => "$", 则返回的的block名称是"$"
        outInfo.addBlock(getNoccCascade(blockId));
        return outInfo;
    }

    /**
     * getPlanTree01
     * 生成专项预案tab页的树
     *
     * @param inInfo*
     * @return *
     */
    public EiInfo getPlanTree02(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        // EiConstant.queryBlock => inqu_status,表示通用查询blockId名称
        // "node" => 表示一次加载结点名称，在前端的ROOT中定义其值
        String blockId = inInfo.getString(EiConstant.queryBlock + EiConstant.separator+"0-node");

        // 返回的的blockId的名称是传入的结点其值
        // 如：inqu_status-0-node => "$", 则返回的的block名称是"$"
//        String blockId = "tree02";
        outInfo.addBlock(getPDFCascade(blockId));
        return outInfo;
    }

    /**
     * getNoccCascade
     * （页面功能）根据查询结果生成数字化预案树
     *
     * @param inInfo*
     * planLine:预案类型枚举值
     * @return *
     */
    public EiBlock getNoccCascade(String blockId) {
        EiBlock block = new EiBlock(blockId);

        block.addMeta(new EiColumn("label"));
        block.addMeta(new EiColumn("parent"));
        block.addMeta(new EiColumn("text"));
        block.addMeta(new EiColumn("hasChildren"));
        EiInfo info = new EiInfo();
        int rowNum =0;
        List<Map<String,String>> result = CYUtils.queryCode("nocc.yj.yg01");
        for(int i=0;i<result.size();i++) {
            Map planGroupTypeMap = result.get(i);
            int planGroupType = Integer.parseInt(planGroupTypeMap.get("value").toString());
            String planGroupTypeName = planGroupTypeMap.get("label").toString();
            block.setCell(rowNum, "label", planGroupType);
            block.setCell(rowNum, "parent", "root");
            block.setCell(rowNum, "text", planGroupTypeName);
            block.setCell(rowNum, "indexLevel", 1);
            block.setCell(rowNum++, "hasChildren", 0);
            Map map = new HashMap();
            map.put("planGroupType", planGroupType);
            List<Map> result2 = dao.query("YJYG01.queryOneLevelNode", map);
            List<List<Map>> indexList = new ArrayList<>();
            indexList.add(result2);
            int k=0;
            while (k<5){
                List<Map> childList = getChildIndexList(result2);
                if(childList.size() == 0){
                    k=5;
                }else{
                    result2 = childList;
                    indexList.add(childList);
                }
            }
            for(int m=0;m<indexList.size();m++){
                List<Map> list1 = indexList.get(m);
                for(int n=0;n<list1.size();n++){
                    block.setCell(rowNum, "label", list1.get(n).get("planGroupUuid"));
                    block.setCell(rowNum, "parent", list1.get(n).get("planGroupParentNodeUuid"));
                    block.setCell(rowNum, "text", list1.get(n).get("planGroupName"));
                    block.setCell(rowNum++, "hasChildren", hasChird(list1.get(n)));
                }
            }
        }
        return block;
    }

    /**
     * getPDFCascade
     * （页面功能）根据查询结果生成专项预案树
     *
     * @param inInfo*
     * planLine:预案类型枚举值
     * @return *
     */
    public EiBlock getPDFCascade(String blockId) {
        EiBlock block = new EiBlock(blockId);

        block.addMeta(new EiColumn("label"));
        block.addMeta(new EiColumn("parent"));
        block.addMeta(new EiColumn("text"));
//        block.addMeta(new EiColumn("leavelNumber"));
        block.addMeta(new EiColumn("hasChildren"));
        Map map=new HashMap();
        int rowNum =0;
        map.put("planGroupType",140007);
        List<Map> oneLevelNode = dao.queryAll("YJYG01.queryOneLevelNode",map);
//        for(int i=0;i<oneLevelNode.size();i++){
//            block.setCell(rowNum, "label", oneLevelNode.get(i).get("planGroupUuid"));
//            block.setCell(rowNum, "parent", "root");
//            block.setCell(rowNum, "text", oneLevelNode.get(i).get("planGroupName"));
//            block.setCell(rowNum, "leavelNumber", 1);
//            block.setCell(rowNum++, "hasChildren", 0);
//
//        }
        List<List<Map>> indexList = new ArrayList<>();
        indexList.add(oneLevelNode);
        int k=0;
        while (k<5){
            List<Map> childList = getChildIndexList(oneLevelNode);
            if(childList.size() == 0){
                k=5;
            }else{
                oneLevelNode = childList;
                indexList.add(childList);
            }
        }
        for(int i=0;i<indexList.size();i++){
            List<Map> list1 = indexList.get(i);
            for(int j=0;j<list1.size();j++){
                block.setCell(rowNum, "label", list1.get(j).get("planGroupUuid"));
                block.setCell(rowNum, "parent", list1.get(j).get("planGroupParentNodeUuid"));
                block.setCell(rowNum, "text", list1.get(j).get("planGroupName"));
                block.setCell(rowNum++, "hasChildren", hasChird(list1.get(j)));
            }
        }

        return block;
    }

    //检查是否存在下一级目录
    private int hasChird(Map map){
        Map map1 = new HashMap();
        map1.put("parentUuid",map.get("planGroupUuid"));
        List<Map> list = dao.query("YJYG01.queryParentOrChird",map1);
        if(list.size()>0){
            return 0;
        }else {
            return 1;
        }
    }

    //查询下一级目录
    private List<Map> getChildIndexList(List<Map> list){
        List<Map> fin = new ArrayList<>();
        for(int i=0;i<list.size();i++){
            Map map = new HashMap();
            map.put("parentUuid",list.get(i).get("planGroupUuid"));
            List<Map> result = dao.queryAll("YJYG01.queryParentOrChird",map);
            fin.addAll(result);
        }
        return fin;
    }

    public EiInfo newAddNode(EiInfo nodeInfo) {
        String parentUuid =  nodeInfo.get("label").toString();
        Map map = new HashMap();
        map.put("parentUuid",parentUuid);
        map.put("planGroupUuid", IdUtil.simpleUUID());
        map.put("planGroupName",nodeInfo.get("text"));
        dao.insert("YJYG01.addRDFNode",map);
        return nodeInfo;
    }

    public EiInfo modifyNode(EiInfo nodeInfo) {
        String parentUuid =  nodeInfo.get("label").toString();
        Map map = new HashMap();

        map.put("planGroupUuid", parentUuid);
        map.put("planGroupName",nodeInfo.get("text"));
        dao.update("YJYG01.updatePDFNode",map);
        return nodeInfo;
    }

    public EiInfo nodeDeleteCheck(EiInfo nodeInfo) {
        String parentUuid =  nodeInfo.get("label").toString();
        Map map1 = new HashMap();
        map1.put("parentUuid", parentUuid);
        List<Map> result =  dao.query("YJYG01.queryParentOrChird",map1);
        if(result.size()>=1){
            nodeInfo.set("hasChild",true);
        }
        Map map2 = new HashMap();
        map2.put("planType",140007);
        result =  dao.query("YJYG01.queryParentOrChird",map2);
        if(result.size()==1){
            nodeInfo.set("lastOneNode",true);
        }
        return nodeInfo;
    }

    public EiInfo deleteNode(EiInfo nodeInfo) {
        String parentUuid =  nodeInfo.get("label").toString();
        Map map = new HashMap();
        String fin = "节点删除失败";

        map.put("planGroupUuid", parentUuid);

        List<Map> result = dao.query("YJYG01.queryNoccPDFCurrent",map);
        if(result.size()>=1){
            fin = "节点删除失败,该节点下存在未删除的预案";
        }else {
            dao.delete("YJYG01.deletPDFNode",map);
            fin = "节点删除成功";
        }
        nodeInfo.set("fin",fin);
        return nodeInfo;
    }
//    public void addNode(Map<String,String> nodeInfo) {
//        dao.insert("DQ02.SQL", nodeInfo);
//        dao.insert("DQ02.SQL", nodeInfo);
//    }

    /**
     * getNoccContentInfo
     * （页面功能）根据选择的预案查询预案详细步骤
     *
     * @param inInfo*
     * planLine:预案类型枚举值
     * @return *
     */
    public EiInfo getNoccContentInfo(EiInfo eiInfo){
        EiBlock verBlock = eiInfo.getBlock("planContent");
        int limit;
        int offset;
        List<Map> result = new ArrayList<>();
        try {
            offset = verBlock.getInt(EiConstant.offsetStr);
            limit = verBlock.getInt(EiConstant.limitStr);
        } catch (Exception e) {
            limit = 10;
            offset = 0;
        }
        try {
            String planGroupUuid = eiInfo.get("planGroupUuid").toString();
            Map params = new HashMap();
            params.put("planGroupUuid",planGroupUuid);
            List<Map> planCurrent = dao.query("YJYG01.queryPlanStepUsed", params);
            if(planCurrent.size()>0){
                params.put("planCurrentUuid",planCurrent.get(0).get("planUuid"));
                List<Map> planStep = dao.query("YJYG01.queryPlanStep",params);
                result = planStep;
            }

        } catch (Exception e) {
            String planCurrentUuid = eiInfo.get("planCurrentUuid").toString();
            Map params = new HashMap();
            params.put("planCurrentUuid",planCurrentUuid);
            List<Map> planStep = dao.query("YJYG01.queryPlanStep",params);
            result = planStep;
        }
        verBlock.set(EiConstant.countStr,result.size());
        verBlock.addRows(gridPage(result, limit, offset));
//        Map params = new HashMap();
//        params.put("planGroupUuid",eiInfo.get("planContent"));
//        List<Map> result = dao.query("YJYG01.queryPlanStep",params);
        return eiInfo;
    }

    /**
     * getVerInfo
     * （页面功能）查询所有预案组
     *
     * @param inInfo*
     * planLine:预案类型枚举值
     * @return *
     */
    public EiInfo getVerInfo(EiInfo eiInfo){
        EiBlock verBlock = eiInfo.getBlock("planVer");
        int limit;
        int offset;
        try {
            offset = verBlock.getInt(EiConstant.offsetStr);
            limit = verBlock.getInt(EiConstant.limitStr);
        } catch (Exception e) {
            limit = 10;
            offset = 0;
        }
        String planGroupUuid = "a";
        Map groupUuid = new HashMap();
        try{
            planGroupUuid = eiInfo.get("planGroupUuid").toString();

        }catch (Exception e){}
        groupUuid.put("planGroupUuid",planGroupUuid);
        List<Map> planCurrent = dao.query("YJYG01.queryNoccCurrent", groupUuid);
        for(int i=0;i<planCurrent.size();i++){
            Map map = new HashMap();
            map = planCurrent.get(i);
            map.put("index",i+1);
        }
        verBlock.set(EiConstant.countStr,planCurrent.size());
        verBlock.addRows(gridPage(planCurrent, limit, offset));
        return eiInfo;
    }

    /**
     * getVerInfo
     * 根据预案名称搜索专项预案
     */
    public EiInfo getPDFVerInfo(EiInfo eiInfo){
        EiBlock verBlock = eiInfo.getBlock("planVer2");
        int limit;
        int offset;
        try {
            offset = verBlock.getInt(EiConstant.offsetStr);
            limit = verBlock.getInt(EiConstant.limitStr);
        } catch (Exception e) {
            limit = 10;
            offset = 0;
        }
        String planGroupUuid = "";
        String key = "";
        Map groupUuid = new HashMap();
        try{
            planGroupUuid = eiInfo.getString("planGroupUuid");
            key = eiInfo.getString("key");

        }catch (Exception e){}
        if("".equals(key)||null==key){
            groupUuid.put("planGroupUuid",planGroupUuid);
        }
        groupUuid.put("key",key);
        List<Map> planCurrent = dao.query("YJYG01.queryNoccPDFCurrent", groupUuid);
        for(int i=0;i<planCurrent.size();i++){
            Map map = new HashMap();
            map = planCurrent.get(i);
            map.put("index",i+1);
        }
        verBlock.set(EiConstant.countStr,planCurrent.size());
        verBlock.addRows(gridPage(planCurrent, limit, offset));
        return eiInfo;
    }

    /**
     * queryPlanName
     * 根据预案类型查询已发布预案
     *
     * @param inInfo*
     * planLine:预案类型枚举值
     * @return *
     */
    public EiInfo queryPlanName(EiInfo info){
        String planLine1 = info.getString("planLine");
        int planLine = 0;
        if(planLine1!=null){
            planLine = Integer.parseInt(planLine1);
        }
//        int planLine = Integer.valueOf(info.get("planLine").toString());

        Map map = new HashMap();
        map.put("planGroupType",planLine);
        List<Map> planList = new ArrayList<>();
        List<Map> finList = new ArrayList<>();
        if(planLine != 140007){
            planList = dao.query("YJYG01.queryUpPlanNameList", map);
            for(int k=0;k<planList.size();k++){
                Map planName = new HashMap();
                planName.put("planName",planList.get(k).get("planName"));
                planName.put("planUuid",planList.get(k).get("planUuid"));
                planName.put("planVersion",planList.get(k).get("planVersion"));
                finList.add(planName);
            }
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("planLine",planLine);
        outInfo.set("planLineCN",tranLineIdToName(planLine));
        outInfo.set("planList",finList);
        return outInfo;
    }

    /**
     * queryPlanStep
     * 根据uuid查询预案
     *
     * @param inInfo*
     * @return *
     */
    public EiInfo queryPlanStep(EiInfo info){
        EiInfo outInfo = new EiInfo();
        List<Map> planNameList = dao.query("YJYG01.queryPlanNameByPlanUuid",info.getAttr());
        List<Map> planStep = new ArrayList<>();
        if(planNameList.size()>0){
            info.set("planCurrentUuid",info.get("planUuid"));
            List<Map> stepList = dao.queryAll("YJYG01.queryPlanStep",info.getAttr());
            for(int i=0;i<stepList.size();i++){
                Map step = new HashMap();
                step.put("stage",stepList.get(i).get("stage"));
                step.put("job",stepList.get(i).get("job"));
                step.put("stepNumber",Integer.valueOf(stepList.get(i).get("stepNumber").toString()));
                step.put("desc",stepList.get(i).get("desc"));
                step.put("timeNode",Integer.valueOf(stepList.get(i).get("post").toString()));
                planStep.add(step);
            }
            outInfo.set("planUuid",info.get("planUuid"));
            outInfo.set("planName",planNameList.get(0).get("planName"));
            outInfo.set("planStep",planStep);
        }
        else{
            outInfo.set("result","该uuid无匹配的预案");
        }
        return outInfo;
    }

    public List<Map> getSPlanFromSource(){
        Map map = new HashMap();
        List<Map> planFinList = new ArrayList<>();
        List<Map> planList = dao.query("YJYG01.queryUpPlanNameList",map);
        for(int i=0;i<planList.size();i++){
            Map plan = new HashMap();
            plan.put("plan_id",planList.get(i).get("planUuid"));
            plan.put("plan_type",planList.get(i).get("planGroupType").toString());
            plan.put("plan_name",planList.get(i).get("planName"));
            Map planUuid = new HashMap();
            List<Map> plan_content = new ArrayList<>();
            planUuid.put("planUuid",planList.get(i).get("planUuid"));
            List<Map> planStepList = dao.query("YJYG01.queryPlanStepJobGroup", planUuid);
            for(int j=0;j<planStepList.size();j++){
                List<Map> post = dao.query("YJYG01.queryPlanStep",planStepList.get(j));
                Map planContent = new HashMap();
                planContent.put("post",planStepList.get(j).get("job"));
                List stepList = new ArrayList();
                for(int k=0;k<post.size();k++){
                    Map step =new HashMap();
                    step.put("content",post.get(k).get("desc"));
                    step.put("dispose_time",post.get(k).get("post"));
                    stepList.add(step);
                }
                planContent.put("post_content",stepList);
                plan_content.add(planContent);
            }
            plan.put("plan_content",plan_content);
            planFinList.add(plan);
        }
        return planFinList;
    }

    /*推送数字化预案至智能应急调度系统*/
    public EiInfo putAllPlanNameList(List<Map> planFinList){
        EiInfo outInfo = new EiInfo();
        outInfo.set("list",planFinList);
        outInfo.set(EiConstant.serviceId, "S_YJ_YGTS_02");
        outInfo = XServiceManager.call(outInfo);
        return outInfo;
    }

    public List<Map> getPlanFromSource(){
        Map map = new HashMap();
        List<Map> planFinList = new ArrayList<>();
        return dao.query("YJYG01.queryPDFPlanNameList",map);
    }

    /*推送专项预案至智能应急调度系统*/
    public EiInfo putAllPDFPlanNameList(List<Map> planList){
        EiInfo info = new EiInfo();
        List<Map> planFinList = new ArrayList<>();
        for(int i=0;i<planList.size();i++){
            Map plan = new HashMap();
            plan.put("plan_id",planList.get(i).get("plan_id"));
            plan.put("plan_type",planList.get(i).get("plan_type").toString());
            plan.put("plan_name",planList.get(i).get("plan_name"));
            plan.put("plan_content",planList.get(i).get("plan_content").toString());
            planFinList.add(plan);
        }

        info.set("list",planFinList);
        info.set(EiConstant.serviceId, "S_YJ_YGTS_01");
        info = XServiceManager.call(info);
        return info;
    }

    private static List<Map> gridPage(List<Map> list, int limit, int offset) {
        int count = list.size();//总条数
        int toIndex = 0;
        int lastIndex = offset + limit;
        toIndex = lastIndex >= count ? count : offset + limit;
        return list.subList(offset, toIndex);
    }

    /**
     * 导入实现
     * @param info
     * @return
     */
    public EiInfo importFile(EiInfo info){
        //1.通过urlStr(filePath)从file server上获取文件字节流
        //调用微服务S_RF_01
        int lineNumber = Integer.valueOf(info.get("lineNumber").toString());
        String finResult = "导入结果为:";
        int failedNum =0;
        EiInfo outInfo  = EiInfoUtils.callParam("S_RF_01",info).build();
        outInfo.set("command", "import_plan");//根据不同模板不同command，上一方法同样
        //调用微服务S_FS_01,获取解析后数据“data”
        try {
        EiInfo eiInfo1 = EiInfoUtils.callParam("S_FS_01",outInfo).build();
        List<Map> planList = (List<Map>) eiInfo1.get("data");
        //循环处理多个预案，数据落库
        for(int i=0;i<planList.size();i++) {
            Map map =planList.get(i);
            EiInfo eiInfo = new EiInfo();
            eiInfo.setAttr(map);
            // 查询group表数据,判断是否存在同级同名预案
//            List groupList = dao.query("YJYG01.queryParentOrChird",eiInfo.getAttr());
//            if (groupList.size()>0){
//                HashMap hashMap = (HashMap) groupList.get(0);
//                Integer planLevel = Integer.parseInt(hashMap.get("planGroupLevel").toString());
            String[] planFirstType1 = map.get("class").toString().split("，");
            for (int j = 0; j < planFirstType1.length; j++) {
                String GroupparentUuid = "";
                if (isPlanGroupExist(lineNumber, planFirstType1[j], j + 1)) {
                } else {
                    if (j >= 1) {
                        Map groupMap1 = new HashMap();
                        groupMap1.put("name", planFirstType1[j - 1]);
                        groupMap1.put("planType", lineNumber);
                        List<Map> list1 = dao.query("YJYG01.queryParentOrChird", groupMap1);
                        GroupparentUuid = list1.get(0).get("planGroupUuid").toString();
                    } else {
                        GroupparentUuid = Integer.toString(lineNumber);
                    }
                    Map groupMap = new HashMap();
                    String UUIDs = CYUtils.getUUID();
                    groupMap.put("planGroupUuid", UUIDs);
                    groupMap.put("lineNumber", lineNumber);
                    groupMap.put("parentUuid", GroupparentUuid);
                    groupMap.put("planGroupName", planFirstType1[j]);
                    groupMap.put("nodeLevel", j + 1);
                    groupMap.put("createdName", "admin");//暂定admin
                    groupMap.put("createdTime", CYUtils.getCurrentNow());
                    EiInfo ciInfo = new EiInfo();
                    ciInfo.set("map", groupMap);
                    ciInfo.set(EiConstant.serviceName, "YJYG01");
                    //设置方法名
                    ciInfo.set(EiConstant.methodName, "addPlanGroupNode");
                    EiInfo diInfo = XLocalManager.call(ciInfo);

                    // 添加current表数据（加版本）
//                        dao.insert("YJYG01.insertPlanCurrent",eiInfo.getAttr());
//                        eiInfo.set("updateName","admin");
//                        eiInfo.set("updateTime",CYUtils.getCurrentNow());
//                        // 修改上版本状态，发布最新版
//                        dao.update("YJYG01.updatePlanCurrent",eiInfo.getAttr());
//                        // 添加step表数据（加步骤）
//                        List<Map> stepList = (List)map.get("step");
//                        stepList.stream().forEach(map1 -> {
//                            map.put("createdName","admin");
//                            map.put("createdTime",CYUtils.getCurrentNow());
//                        });
//                        dao.insert("YJYG01.insertPlanStep",stepList);
                }

            }
            Map map1= new HashMap();
            map1.put("name",planFirstType1[planFirstType1.length-1]);
            map1.put("planType",lineNumber);
            List<Map> planGroupList1 = dao.query("YJYG01.queryParentOrChird",map1);
            String fdPlanGroupUuid;
            if(planGroupList1.size()>=1){
                fdPlanGroupUuid = planGroupList1.get(0).get("planGroupUuid").toString();
                if(isCurrentExist(fdPlanGroupUuid,map.get("version").toString()) || isCurrentUsed(fdPlanGroupUuid)){
                    finResult = finResult + planFirstType1[planFirstType1.length-1] + "导入失败;";
                    failedNum++;
                } else {
                    String UUIDs1= CYUtils.getUUID();
                    Map map4 = new HashMap();
                    map4.put("planGroupUuid",fdPlanGroupUuid);
                    EiInfo jiInfo = new EiInfo();
                    jiInfo.set("map", map4);
                    jiInfo.set(EiConstant.serviceName, "YJYG01");
                    //设置方法名
                    jiInfo.set(EiConstant.methodName, "updateNOCCCurrent");
                    EiInfo kiInfo = XLocalManager.call(jiInfo);
                    Map map2 = new HashMap();
                    map2.put("UUIDs",UUIDs1);
                    map2.put("planType",lineNumber);
                    map2.put("planStatus",150001);
                    map2.put("planGroupUuid",fdPlanGroupUuid);
                    map2.put("createdName",map.get("editor").toString());
                    map2.put("version",map.get("version").toString());
                    map2.put("content",map.get("content").toString());
                    map2.put("number",map.get("number").toString());
                    map2.put("createdTime",CYUtils.getCurrentNow());
                    map2.put("updateName",map.get("editor").toString());
                    map2.put("updateTime",CYUtils.getCurrentNow());
                    EiInfo fiInfo = new EiInfo();
                    fiInfo.set("map", map2);
                    fiInfo.set(EiConstant.serviceName, "YJYG01");
                    //设置方法名
                    fiInfo.set(EiConstant.methodName, "addNOCCCurrent");
                    EiInfo giInfo = XLocalManager.call(fiInfo);
                    Map map3 = new HashMap();
                    map3.put("UUIDs",UUIDs1);

                    List<Map>  step = (List)map.get("step");
                    for(Map smap:step){
                        if(smap.get("time")==null || "".equals(smap.get("time").toString())){
                            smap.put("time",0);
                        }
                    }
                    map3.put("stepList",map.get("step"));
                    EiInfo hiInfo = new EiInfo();
                    hiInfo.set("map", map3);
                    hiInfo.set(EiConstant.serviceName, "YJYG01");
                    //设置方法名
                    hiInfo.set(EiConstant.methodName, "addNOCCCurrentStep");
                    EiInfo iiInfo = XLocalManager.call(hiInfo);
                }
            }

        }
//        String fileName = outInfo.getString("fileName");
        List<Map> sPlanFromSource = getSPlanFromSource();
        putAllPlanNameList(sPlanFromSource);
        if(failedNum == 0){
            finResult = "导入全部成功;";
        }
        }catch (Exception e){
            finResult = "预案模板格式错误，请检查文件内容";
        }
        info.set("finResult",finResult);
        return info;
    }

    public EiInfo importPDFFile(EiInfo info) throws InterruptedException {
        int lineNumber = 140007;
        String GroupUuid = "";
        List<String> fileNameList = new ArrayList<>();
        String dataList = info.getString("dataList");
        List<Map> list = JSON.parseObject(dataList, List.class);
        String loginName = UserSession.getLoginName();
        List<byte[]> byteList = new ArrayList<>();
        try {
            for(Map map:list){
                String fileName = map.get("fileName").toString();
                GroupUuid = map.get("sceneUuid").toString();
                EiInfo newInfo = new EiInfo();
                newInfo.set("urlStr",map.get("urlStr").toString());
                newInfo.set("fileName",map.get("fileName").toString());
                newInfo.set(EiConstant.serviceId, "S_RF_30");
                EiInfo outInfo = XServiceManager.call(newInfo);
                byte[] file = outInfo.toJSON().getBytes("file");//获得文件流
                byteList.add(file);
                fileNameList.add(fileName);
            }
        }catch (Exception e){
            //操作
            info.setStatus(EiConstant.STATUS_FAILURE);
            return info;
        }

        List<Map> planFromSource = getPlanFromSource();
        List<Map<String,Object>> sqlMap = new ArrayList<>();
        for(int i=0;i<byteList.size();i++){
            //其它操作
            EiInfo eiInfo = new EiInfo();
            String UUIDs = CYUtils.getUUID();
            String newFileName = UUIDs +".pdf";
            eiInfo.set("file",byteList.get(i));
            //2.将字节流写入OSS文件系统
            eiInfo.set("bucketName", "CMP/YJYG01");
            eiInfo.set("newFileName",newFileName);
            eiInfo.set(EiConstant.serviceId,"S_RF_31"); // RF02->ossUpload
            eiInfo = XServiceManager.call(eiInfo);

            Map map = new HashMap();
            Map map1 = new HashMap();
            map1.put("bucketName","CMP/YJYG01");
            map1.put("fileName",newFileName);
            JSONObject fdsrc = new JSONObject(map1);
            map.put("filename",fileNameList.get(i));

            map.put("uuid", UUIDs);
            map.put("GroupUuid", GroupUuid);
            map.put("planType", lineNumber);
            map.put("version", "V1.1");
            map.put("loginName", loginName);
            map.put("updateTime", CYUtils.getCurrentNow());
            map.put("fdsrc", fdsrc.toJSONString());

            Map plan = new HashMap();
            plan.put("plan_id",UUIDs);
            plan.put("plan_type",lineNumber);
            plan.put("plan_name",fileNameList.get(i));
            plan.put("plan_content",fdsrc.toJSONString());
            planFromSource.add(plan);
            sqlMap.add(map);
        }
        dao.insertBatch("YJYG01.addpdfPlan",sqlMap);
        Thread.sleep(1000);
        EiInfo jiInfo = new EiInfo();
        putAllPDFPlanNameList(planFromSource);
        info.setStatus(EiConstant.STATUS_SUCCESS);
        return info;
    }

    public EiInfo importPDFFile22(EiInfo info) throws InterruptedException {
        int lineNumber = Integer.valueOf(info.get("lineNumber").toString());
        String fileName = info.getString("fileName");
        String GroupUuid = info.getString("sceneUuid");
//        EiInfo outInfo  = EiInfoUtils.callParam("S_RF_01",info).build();
        info.set(EiConstant.serviceId,"S_RF_01");
        EiInfo outInfo = XServiceManager.call(info);
        byte[] file = outInfo.toJSON().getBytes("file");
        EiInfo eiInfo = new EiInfo();
        String newFileName = getSecondTime() + "" +".pdf";
        eiInfo.set("file",file);
        //2.将字节流写入OSS文件系统
        eiInfo.set("bucketName", "CMP");
        eiInfo.set("newFileName",newFileName);
        eiInfo.set(EiConstant.serviceId,"S_RF_03"); // RF02->ossUpload
        outInfo = XServiceManager.call(eiInfo);
        Map map = new HashMap();
        Map map1 = new HashMap();
        map1.put("bucketName","CMP");
        map1.put("fileName",newFileName);
        JSONObject fdsrc = new JSONObject(map1);
        map.put("filename",fileName);
        String UUIDs = CYUtils.getUUID();
        map.put("uuid", UUIDs);
        map.put("GroupUuid", GroupUuid);
        map.put("planType", lineNumber);
        map.put("fdsrc", fdsrc.toJSONString());
        EiInfo linInfo = new EiInfo();
        linInfo.set("data",map);
        linInfo.set(EiConstant.serviceName, "YJYG01");
        linInfo.set(EiConstant.methodName, "insertPDFplanScense");
        linInfo = XLocalManager.callNewTx(linInfo);
        Thread.sleep(1000);
        EiInfo jiInfo = new EiInfo();
        jiInfo.set(EiConstant.serviceName, "YJYG01");
        //设置方法名
        jiInfo.set(EiConstant.methodName, "putAllPDFPlanNameList");
        EiInfo kiInfo = XLocalManager.callNewTx(jiInfo);
        if(kiInfo.getStatus() < 0){
            throw new PlatException(kiInfo.getMsg());
        }
        return info;
    }

    public EiInfo insertPDFplanScense(EiInfo info){
        Map map = info.getMap("data");
        dao.insert("YJYG01.addpdfPlan",map);
        return info;
    }


    public static List<String> findNamesByUuid(String uuid, List<Map<String, String>> list) {
        List<String> names = new ArrayList<>();
        for (Map<String, String> map : list) {
            if (uuid.equals(map.get("groupId"))) {
                names.add(map.get("name"));
                names.addAll(findNamesByUuid(map.get("nodeId"), list));
            }
        }
        return names;
    }

    /**
     * 查询导出所需的父类树
     * @param groupId 预案组id
     * @return 父类树
     */
    public String[] queryExpostGroup(String groupId,List<Map<String,String>> stepList){
        String[] backArr = {"",""};
        Map param= new HashMap();
        param.put("planCurrentUuid",groupId);
        if(stepList.size()==0){
            stepList = dao.query("YJYG01.queryGroupxport",param);
        }
        List<String> backList = findNamesByUuid(groupId, stepList);
        StringBuilder sb = new StringBuilder();
        for(int i=backList.size()-1;i>=0;i--){
            sb.append(backList.get(i)).append("，");
        }
        if(backList.size()>0){
            backArr[0] = backList.get(0);
        }
        backArr[1] = sb.toString().substring(0,sb.length()-1);
        return backArr;
    }

    public static List<Map<String, String>> findMapsByParentId(List<Map<String, String>> allMap, String parentId) {
        List<Map<String, String>> matchedMaps = new ArrayList<>();
        for (Map<String, String> map : allMap) {
            if (parentId.equals(map.get("nodeId"))) {
                matchedMaps.add(map);
            }
        }
        return matchedMaps;
    }

    public static List<Map<String, String>> findLeafMaps(List<Map<String, String>> allMap, List<Map<String, String>> childMaps) {
        List<Map<String, String>> leafMaps = new ArrayList<>();
        for (Map<String, String> map : childMaps) {
            String uuid = map.get("groupId");
            List<Map<String, String>> grandchildMaps = findMapsByParentId(allMap, uuid);
            if (grandchildMaps.isEmpty()) {
                leafMaps.add(map);
            } else {
                leafMaps.addAll(findLeafMaps(allMap, grandchildMaps));
            }
        }
        return leafMaps;
    }

    /**
     * 多层级数字化预案导出工作
     */
    public EiInfo exportPlans(EiInfo info){
        try {
            List<Map<String, Object>> backList = new ArrayList<>();
            String planUuid = info.get("nodeOne").toString();//一级节点
            String planGroupUuid = info.get("nodeTwo").toString();//次级节点
            //读取全部组预案
            Map<String,Object> param= new HashMap<>();
            List<Map<String, String>> stepList = dao.query("YJYG01.queryGroupxport",param);
            param.put("planStatus",150001);//上线的版本
            List<Map<String, String>> currentList = dao.query("YJYG01.queryNoccNewestCurrent",param);
            //所需匹配的层级id
            String nodeId =planGroupUuid;
            if("".equals(planGroupUuid)){
                nodeId = planUuid;
            }
            List<Map<String, String>> matchedMaps = findMapsByParentId(stepList,nodeId);
            List<Map<String, String>> result = new ArrayList<>();
            for (Map<String, String> map : matchedMaps) {
                String uuid = map.get("groupId");
                List<Map<String, String>> childMaps = findMapsByParentId(stepList, uuid);
                if (childMaps.isEmpty()) {
                    result.add(map);
                } else {
                    result.addAll(findLeafMaps(stepList, childMaps));
                }
            }
            if(result.size()==0){
                for(Map<String,String> map:stepList){
                    if(nodeId.equals(map.get("groupId"))){
                        result.add(map);
                        break;
                    }
                }
            }
            for(Map<String,String> ma:currentList){
                String planId = ma.get("planGroupUuid");
                String curFdId = ma.get("planUuid");
                for(Map<String, String> map:result){
                    String groupId = map.get("groupId");
                    if(groupId.equals(planId)){
                        Map<String, Object> curmap = dataProcessing(curFdId, groupId, ma.get("planNumber"), ma.get("planUpdateDesc"), ma.get("planVersion"), ma.get("updateMan"), stepList);
                        backList.add(curmap);
                        break;
                    }
                }
            }
            saveToFileServe(backList);
            info.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e){
            info.setStatus(EiConstant.STATUS_FAILURE);
        }
        return info;
    }

    /**
     * 数字化预案导出数据条件抽取处理
     * @param planUuid 预案id
     * @param planGroupUuid
     * @param planNumber
     * @param planUpdateDesc
     * @param planVersion
     * @return
     */
    public Map<String, Object> dataProcessing(Object planUuid,Object planGroupUuid,Object planNumber,
                                                    Object planUpdateDesc,Object planVersion,Object editor,List<Map<String,String>> groupList){
        //获取分类
        String[] claseArr = queryExpostGroup((String) planGroupUuid,groupList);
        Map<String,String> param= new HashMap<>();
        param.put("planCurrentUuid",planUuid.toString());
        List<Map> stepList = dao.query("YJYG01.queryPlanexport",param);

        Map<String, Object> curmap = new HashMap<>();
        curmap.put("name",claseArr[0]);
        curmap.put("class",claseArr[1]);
        curmap.put("number",planNumber);
        curmap.put("version",planVersion);
        curmap.put("content",planUpdateDesc);
        curmap.put("editor",editor);
        curmap.put("step",stepList);
        return curmap;
    }

    /**
     * 数字化预案写进fileserve服务
     * @param backList 处理完成后的数据
     */
    public void saveToFileServe(List<Map<String, Object>> backList){
        EiInfo infoFile = new EiInfo();
        infoFile.set("command","export_plan");
        infoFile.set("data",backList);
        EiInfo fileInfo = callXService(infoFile, "S_FS_98");//S_FS_98
        String base64 = fileInfo.get("planByte").toString();
        String fileName = "预案模板NOCC"+getTimeStamp()+".xlsx";
        writeFileToFileServe(base64,fileName);
    }

    /**
     * 单板本数字化预案导出
     * @param info
     * @return
     */
    public EiInfo exportPlan(EiInfo info){
        try {
            Object planUuid = info.get("planUuid");//版本预案id
            Object planGroupUuid = info.get("planGroupUuid");//预案所属组
            Object planNumber = info.get("planNumber");//预案类
            Object planUpdateDesc = info.get("planUpdateDesc");//预案主要更新内容
            Object planVersion = info.get("planVersion");//预案版本
            Object planEditor = info.get("updateMan");//预案bianzhiren
            List<Map<String,String>> groupList = new ArrayList<>();
            Map<String, Object> curmap = dataProcessing(planUuid,planGroupUuid,planNumber,planUpdateDesc,planVersion,planEditor,groupList);
            List<Map<String, Object>> backList = new ArrayList<>();
            backList.add(curmap);
            saveToFileServe(backList);
            info.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e){
            info.setStatus(EiConstant.STATUS_FAILURE);
        }
        return info;
    }



    /**
     * 专项预案导出
     */
    public EiInfo exportPlanPDF(EiInfo info){
        try {
            Object planName = info.get("planName");//预案名称
            Object planSrc = info.get("planSrc");//预案oss信息
            Map map = JSONObject.parseObject(planSrc.toString(), Map.class);
            Object bucketName = map.get("bucketName");
            String fileName = map.get("fileName").toString();
            byte[] fileByOSS = getFileByOSS(bucketName.toString(), fileName);
            String newName = planName+getTimeStamp()+".pdf";
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", fileByOSS);
            fileInfo.set("fileName", newName);
            fileInfo.set("path","预案管理/");
            callXService(fileInfo, "S_RF_02");
            info.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e){
            info.setStatus(EiConstant.STATUS_FAILURE);
        }
        return info;
    }

    public String getTimeStamp() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return dateFormat.format(date);
    }

    public EiInfo callXService(EiInfo inInfo, String serviceId){
        inInfo.set(EiConstant.serviceId, serviceId);
        EiInfo outInfo = XServiceManager.call(inInfo);
        //注意必须对outInfo的status状态进行校验
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }

    private void writeFileToFileServe(String base64,String fileName){
        try {
            byte[] bytes =Base64.getDecoder().decode(base64);
            EiInfo fileInfo = new EiInfo();
            fileInfo.set("file", bytes);
            fileInfo.set("fileName", fileName);
            fileInfo.set("path","预案管理/");
            callXService(fileInfo, "S_RF_02");
        }catch (Exception e){
            throw new PlatException("文件存储异常");
        }
    }

    private byte[] getFileByOSS(String ossPath,String fileName){
        EiInfo fileInfo = new EiInfo();
        fileInfo.set("bucketName", ossPath);
        fileInfo.set("fileName", fileName);
        EiInfo outInfo = callXService(fileInfo, "S_RF_04");
        byte[] pictureByte = outInfo.toJSON().getBytes("fileData");
        return pictureByte;
    }
    public String getSecondTime() {
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return date.format(formatter1);
    }

    //添加节点
    public EiInfo addPlanGroupNode(EiInfo info){
        Map map = info.getMap("map");
        dao.insert("YJYG01.addPlanGroupNode",map);
        return info;
    }
    //添加数字化预案
    public EiInfo addNOCCCurrent(EiInfo info){
        Map map = info.getMap("map");
        dao.insert("YJYG01.insertPlanCurrent",map);
        return info;
    }

    //更新数字化预案状态
    public EiInfo updateNOCCCurrent(EiInfo info){
        Map map = info.getMap("map");
        dao.insert("YJYG01.updatePlanCurrent",map);
        return info;
    }

    //添加数字化预案步骤
    public EiInfo addNOCCCurrentStep(EiInfo info){
        Map map = info.getMap("map");
        List<Map> stepList = (List<Map>) map.get("stepList");
        for (int i =0;i<stepList.size();i++){
            Map map2 = new HashMap();
            map2 = stepList.get(i);
            map2.put("UUIDs",map.get("UUIDs").toString());

        }
        dao.insert("YJYG01.insertPlanStep",stepList);
        return info;
    }

    //删除数字化预案
    public EiInfo deleteCurrent(EiInfo info){
        String planId = info.getString("planUuid");
        List<Map> planFromSource = getSPlanFromSource();
        planFromSource = planFromSource.stream()
                .filter(map -> !planId.equals(map.get("plan_id")))
                .collect(Collectors.toList());
        putAllPlanNameList(planFromSource);

        List<String> plans = new ArrayList<>();
        plans.add(planId);
        EiInfo outInfo = new EiInfo();
        outInfo.set("plan_id",plans);
        outInfo.set("plan_type","sz");
        outInfo.set(EiConstant.serviceId, "S_XF_XG_13");
        outInfo = XServiceManager.call(outInfo);

        dao.insert("YJYG01.deleteCurrent",info.getAttr());
        return info;
    }

    //删除现场化预案
    public EiInfo deleteScene(EiInfo info){
        String planId = info.getString("planUuid");
        List<String> strs = Arrays.asList(planId.split(","));
        List<Map> planFromSource = getPlanFromSource();
        List<Map> filteredPlans = planFromSource.stream()
                .filter(map -> {
                    String planIdInMap = (String) map.get("plan_id");
                    return !strs.contains(planIdInMap);
                })
                .collect(Collectors.toList());
//        planFromSource = planFromSource.stream()
//                .filter(map -> !planId.equals(map.get("plan_id")))
//                .collect(Collectors.toList());
        EiInfo info1 = new EiInfo();
        info1.set("planUuid",strs );
        putAllPDFPlanNameList(filteredPlans);

        /*
        EiInfo outInfo = new EiInfo();
        outInfo.set("plan_id",strs);
        outInfo.set("plan_type","zx");
        outInfo.set(EiConstant.serviceId, "S_XF_XG_13");
        outInfo = XServiceManager.call(outInfo);
         */

        dao.insert("YJYG01.deleteScene",info1.getAttr());
        return info;
    }

    //修改现场化预案编号
    public EiInfo updatePDFPlanId(EiInfo info){
        dao.update("YJYG01.updatePdfPlanId",info.getAttr());
        return info;
    }

    private boolean isPlanGroupExist(int lineNumber,String planClass, int planLevel){
        Map map = new HashMap();
        map.put("planLevel",planLevel);
        map.put("name",planClass);
        map.put("planType",lineNumber);
        List list =dao.query("YJYG01.queryParentOrChird",map);
        if(list.size()>=1){
            return true;
        }
        return false;
    }

    private boolean isCurrentExist(String fdGroupUuid,String fdVersion){
        Map map = new HashMap();
        map.put("planGroupUuid",fdGroupUuid);
//        map.put("planGroupType",lineNumber);
        map.put("planVersion1",fdVersion);
        List list = dao.query("YJYG01.queryNoccCurrent",map);
        if(list.size()>=1){
            return true;
        }
        return false;
    }

    private boolean isCurrentUsed(String fdGroupUuid){
        Map map = new HashMap();
        map.put("planGroupUuid",fdGroupUuid);
//        map.put("planGroupType",lineNumber);
        List<Map> list = dao.query("YJYG01.queryNoccNewestCurrent",map);
        if(list.size()>=1){
           Map map1 = list.get(0);
           List<Map> list1 = dao.query("YJYG01.queryUsedCurrent",map1);
            if(list1.size()>=1){
                return true;
            }else {
                return false;
            }
        }else {
            return false;
        }
    }

    public Integer getPlanType(String str){
        if (str.contains("NOCC")){
            return 140001;
        }else if (str.contains("1号线")){
            return 140002;
        }else if (str.contains("2号线")){
            return 140003;
        }else if (str.contains("3号线")){
            return 140004;
        }else if (str.contains("4号线")){
            return 140005;
        }else if (str.contains("5号线")){
            return 140006;
        }
        return 0;
    }
    /*
     * 获取当前系统类型，区分测试环境与正式环境
     * */
    public EiInfo getSystemType(EiInfo info){
        info.set("systemType",PlatApplicationContext.getProperty("iplat4j.admin.type"));
        return info;
    }

    public EiInfo getAllPaln(EiInfo info){
        List<Map> sz = new ArrayList<>();
        Map map = new HashMap();
        List<Map> planList = dao.query("YJYG01.queryUpPlanNameList",map);
        for(int i=0;i<planList.size();i++) {
            Map plan = new HashMap();
            plan.put("plan_id", planList.get(i).get("planUuid"));
            plan.put("plan_type", planList.get(i).get("planGroupType").toString());
            plan.put("plan_name", planList.get(i).get("planName"));
            sz.add(plan);
        }
        List<Map> zx = getPlanFromSource();
        info.set("szPlan",sz);
        info.set("zxPlan",zx);
        return info;
    }

}
