package com.baosight.cmp.yj.yl.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.baosight.cmp.common.CYUtils;
import com.baosight.cmp.common.util.eiinfo.EiInfoUtils;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.baosight.cmp.common.CYUtils.queryLine;
import static com.baosight.cmp.common.CYUtils.queryStation;


public class ServiceYJYL01 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceYJYL01.class);
    private static Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");

    static String httpurl ="http://localhost:8180/nnaq_war_exploded/service/";
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
//        try {
//            inInfo = yjyl01DoPost7dayTime();//获取安全系统进7天的演练数据
//            if(inInfo.getStatus()!=-1){
//                yjylDrillUpdate(inInfo);
//            }
//            inInfo.set("result-limit",15);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        inInfo.set("result-limit",15);
        return query(inInfo);
    }
    /**
     * 定时任务：每小时获取从安全系统更新一次应急演练数据。
     * 平台微服务：S_YL_DS_1
     * 平台定时任务编号:TASK_YJ_YL_01
     * 平台触发器编号:TASK_YJ_YL_01
     * 定时任务：每1小时执行一次
     * 0 0 * * * ?
     *
     * @param inInfo
     * @return
     */
    public EiInfo YjylTimedTasks(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            inInfo = yjyl01DoPost7dayTime();//获取安全系统进7天的演练数据
            if(inInfo.getStatus()!=-1){
                yjylDrillUpdate(inInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            outInfo.setMsg(e.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            return outInfo;
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return outInfo;
    }
    /**演练超时
     * 定时任务：每天排查一次是否有演练日期过期又没有演练的数据，如有这修改状态为演练超时。
     * 平台微服务：S_YL_DS_2
     * 平台定时任务编号:TASK_YJ_YL_02
     * 平台触发器编号:TASK_YJ_YL_02
     * 定时任务：每天执行一次
     * 0 0 2 * * ?
     *
     * @param inInfo
     * @return
     */
    public EiInfo YjylCsTimedTasks(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        LocalDate date = LocalDate.now();//获取当天日期。
        outInfo.set("inqu_status-0-drillCSEndDate",date.toString());//演练超时：日期为当天之前的数据
        outInfo.set("inqu_status-0-YLState","190007");//默认不查询表fd_disposal_t字段值为190007的数据
        outInfo = super.query(outInfo, "YJYL01.query", null, false, null, "inqu_status", "result", "result");
        List<Map<String,Object>> YLCS= outInfo.getBlock("result").getRows();
        for (int i =0;i<YLCS.size();i++){
            Map map = new HashMap();
            map.put("fdUuid",YLCS.get(i).get("uuid"));
            map.put("drillStatus",190007);
            map.put("drillStatus2",190007);
            map.put("UpdateBy",UserSession.getLoginName());
            map.put("UpdateTime",CYUtils.getCurrentNow());
            dao.update("YJYL01.updateDrillStatus",map);//修改演练表的状态----修改了drill表的fd_extend1和fd_drill_status字段的状态
            dao.update("YJYL01.updateDrillInfoState",map);//修改演练信息表的状态----修改了drillinfo表的fd_disposal_t字段的状态
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return outInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
//        String limit = Optional.ofNullable(inInfo.getBlock("result").get("limit")).map(Object::toString).orElse("15");
//        inInfo.addBlock("result").set("showCount","true");
//        inInfo.addBlock("result").set("limit",limit);
        LocalDate date = LocalDate.now();//获取当天日期。
        inInfo.set("inqu_status-0-drillEndDate",date.toString());
        inInfo.set("inqu_status-0-YLState","190007");//默认不查询表fd_disposal_t字段值为190007的数据
        inInfo = super.query(inInfo, "YJYL01.query", null, false, null, "inqu_status", "result", "result");
        //生成序号
        List<Map<String,Object>> result = inInfo.getBlock("result").getRows();
        for (int i = 0; i < result.size(); i++) {
            result.get(i).put("fdNum", (int) inInfo.get("result-offset") + i + 1);
        }
        inInfo.setStatus(1);
//        YjylCsTimedTasks(inInfo);
        return inInfo;
    }

    /**
     * 演练信息表新增演练数据
     * @param inInfo
     * @return
     */
    public EiInfo insert(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            List<Map<String,Object>> result = inInfo.getBlock("result").getRows();
            if (CollectionUtil.isEmpty(result)){
                throw new PlatException("数据缺少，请重新尝试！");
            }
            Map<String, Object> drillInfo = result.get(0);
            dao.insert("YJYL01.insertYL",drillInfo);//往演练信息表插入已确认的演练数据。
        }catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    /**
     * 演练信息表修改状态
     * @param inInfo
     * @return
     */
    public EiInfo updateDrillInfo(EiInfo inInfo){
        Map<String, Object> map =new HashMap<>();
        map.put("fdUuid",inInfo.get("inqu_status-0-fdUuid"));
        List<Map<String, Object>> list= dao.query("YJYL01.queryDrillInfo3", map);
        map.put("drillStatus",190002);
        if(list.size()>0){
            //判断演练表是否有数据有则是修改演练信息状态。
            dao.update("YJYL01.updateDrillInfo",map);
        }else {
            //判断演练信息表是否有数据，没有则新增
            dao.insert("YJYL01.insert",map);
        }
        return inInfo;
    }

    /**
     * 同步近7天的安全系统演练数据
     * 微服务：S_YJ_YL01
     * @throws Exception
     */
    public static EiInfo yjyl01DoPost7dayTime() throws Exception {
        logger.info("推送一次信息");
        EiInfo inInfo = new EiInfo();
        Map<String, Object> map =new HashMap<>();
        LocalDate currentDate = LocalDate.now();
        LocalDate previousDay = currentDate.minusDays(7);
        inInfo.set("last_update_time_s", previousDay+" 00:00:00");
        inInfo.set("last_update_time_e", currentDate+" 23:59:59");
        //http://localhost:8180/nnaq_war_exploded/service/S_AQ_YJ_003
        //http://*************:82/nnaq/service/S_AQ_YJ_003
        EiInfo outInfo = EiInfoUtils.callParam("S_YJ_YL01", inInfo).build();
        return outInfo;
    }

    public String getStr(Object obj){
        return obj == null?"":obj.toString();
    }
    /**
     * 判断及处理安全系统同步的演练数据
     * @throws Exception
     */
    public EiInfo yjylDrillUpdate(EiInfo inInfo) throws Exception {
        EiBlock aqInfo =  inInfo.getBlock("result");
        List<Map<String,Object>> result = new ArrayList<>();
        if(aqInfo != null){
            result = aqInfo.getRows();
        }
        Map<String, Object> map =new HashMap<>();
        String UUID,updateTime,drillstatus,pgURL,assessment ="";
        for(int i=0;i<result.size();i++){
            UUID = (String) result.get(i).get("drill_id");
            updateTime = (String) result.get(i).get("last_update_time");
            pgURL = getStr(result.get(i).get("assessment_feport"));
            drillstatus = (String) result.get(i).get("drill_status");
            map.put("drillUUID",UUID);
            List<Map<String,Object>> query = dao.query("YJYL01.query", map);
            if(query.size() > 0) assessment =getStr(query.get(0).get("filePath"));//nocc演练的评估报告字段判断是否有值

            map.put("fdUuid",(String) result.get(i).get("drill_id"));
            //如果演练表已经有该数据则判断是否需要修改。
            if (query.size()>0 && !updateTime.equals(query.get(0).get("lastUpdateTime"))){
//                map.put("fdUuid",(String) result.get(i).get("drill_id"));
                map.put("name",(String) result.get(i).get("drill_name"));
                map.put("date",(String) result.get(i).get("drill_date"));
                map.put("enddate",(String) result.get(i).get("drill_endDate"));
                map.put("place",(String) result.get(i).get("drill_place"));
                map.put("level",Integer.parseInt((String) result.get(i).get("drill_level")));
                map.put("desc","演练时间："+(String) result.get(i).get("drill_date")+",演练地点："+(String) result.get(i).get("drill_place"));   //接口返回数据没有演练描述，先用演练名称数据代替
                map.put("plan",(String) result.get(i).get("plan_type"));
                map.put("status",(int) Integer.parseInt(drillstatus));
                map.put("update",UserSession.getLoginName());
                map.put("updateTime",CYUtils.getCurrentNow());
                map.put("organization",(String) result.get(i).get("organization_unit_name"));
                map.put("cooperation",(String) result.get(i).get("cooperation_unit_name"));
                map.put("lastUpdateTime",(String) result.get(i).get("last_update_time"));
                map.put("fd_assessment_report",(String) result.get(i).get("assessment_feport"));//安全系统演练评估报告
                dao.update("YJYL01.updateDrill",map);//根据安全管理系统反馈数据更新演练表数据。
            }
            if(pgURL.length()>5 && assessment.length()<5){
                //pgURL：判断从安全系统获取的url是否有值   assessment判断应急演练小于5个字段等于没有有效url地址
                map.put("fd_assessment_report",pgURL);//安全系统演练评估报告
                dao.update("YJYL01.updateDrill",map);//根据安全管理系统反馈数据更新演练表数据。
            }
            //如果演练表没有该数据则新增插入数据。
            if(query.size()==0){
                map.put("flag","0");
                map.put("name",(String) result.get(i).get("drill_name"));
                map.put("date",(String) result.get(i).get("drill_date"));
                map.put("enddate",(String) result.get(i).get("drill_endDate"));
                map.put("place",(String) result.get(i).get("drill_place"));
                map.put("level",Integer.parseInt((String) result.get(i).get("drill_level")));
                map.put("desc","演练时间："+(String) result.get(i).get("drill_date")+",演练地点："+(String) result.get(i).get("drill_place"));   //接口返回数据没有演练描述，先用演练名称数据代替
                map.put("plan",(String) result.get(i).get("plan_type"));
                map.put("status",(int) Integer.parseInt((String) result.get(i).get("drill_status")));
                map.put("created",UserSession.getLoginName());
                map.put("createdTime",CYUtils.getCurrentNow());
                map.put("organization",(String) result.get(i).get("organization_unit_name"));
                map.put("cooperation",(String) result.get(i).get("cooperation_unit_name"));
                map.put("lastUpdateTime",(String) result.get(i).get("last_update_time"));
                map.put("fd_assessment_report",(String) result.get(i).get("assessment_feport"));//安全系统演练评估报告
                map.put("extend1","3");
                dao.insert("YJYL01.insertYL",map);//如果演练表没有安全系统反馈的数据则新增。根据安全系统的UUID判断
            }
            if(!drillstatus.equals("3")){
                //查询演练信息表的演练状态，根据信息表状态是否为待确认和待执行，是则修改演练信息状态
                int status2 = Integer.parseInt((String) query.get(0).get("status2"));
                if(status2 <= 190002){
                    switch (drillstatus){
                        case "5" :
                            map.put("drillStatus",190007);
                            break;
                        case "7" :
                            map.put("drillStatus",190006);
                            break;
                        default:
                            map.put("drillStatus",Integer.toString(status2));
                    }
                    map.put("UpdateBy",UserSession.getLoginName());
                    map.put("UpdateTime",CYUtils.getCurrentNow());
                    dao.update("YJYL01.updateDrillInfoState",map);
                    dao.update("YJYL01.updateDrillStatus",map);//演练表-修改编辑后的演练状态数据。
                }
            }
            map.clear();
        }

        return inInfo;
    }

    public static EiInfo queryEventDates(EiInfo inInfo){
        //查询参数
        String eventId = Optional.ofNullable(inInfo.get("eventId")).map(Object::toString).orElse("");
        Map<String, Object> param = new HashMap<>();
        if (StringUtils.isNotBlank(eventId)){
            param.put("drillUUID",eventId);
        }
        List<Map<String,Object>> result = dao.queryAll("YJYL01.queryDrillInfo", param);

        if (CollectionUtil.isEmpty(result)) {
            inInfo.addBlock("result").setRows(new ArrayList());
            return inInfo;
        }

        dealEventResultData(result);
        inInfo.addBlock("result").setRows(result);
        inInfo.getBlock("result").set(EiConstant.offsetStr, 9999);
        inInfo.getBlock("result").set(EiConstant.limitStr, 9999);
        return inInfo;
    }

    /**
     * 处理事件数据
     * @param result
     */
    public static void dealEventResultData(List<Map<String, Object>> result) {
        //获取全部线路信息
        EiInfo lineInfo = queryLine(new EiInfo());
        List<Map<String,String>> lineList = lineInfo.getBlock("result").getRows();

        //查询预案数据源
        EiInfo planInfo = queryPlanInfo(new EiInfo());
        List<Map<String,String>> planList = (List<Map<String,String>>)Optional.ofNullable(Convert.toList(planInfo.get("planList"))).orElse(new ArrayList<>());

        //获取全部车站信息
        EiInfo stationInfo = queryStation(new EiInfo());
        List<Map<String,String>> stationAllList = stationInfo.getBlock("result").getRows();

        for (int i = 0; i < result.size(); i++) {
            //生成序号
            result.get(i).put("fdNum",i+1);

            // 查询线路中文名
            String lineId = Optional.ofNullable(result.get(i).get("line")).map(Object::toString).orElse("");
            String lineName =lineList.stream().filter(data -> lineId.equals(data.get("line_id"))).map(l -> l.get("line_cname")).collect(Collectors.joining(","));

            result.get(i).put("lineName",lineName);
            //设置地点EVENTPLACE
            String eventPlace = "";
            String fdArea = Optional.ofNullable(result.get(i).get("AreaT")).map(Object::toString).orElse("");
            if (StringUtils.isNotBlank(fdArea)){
                if (Integer.parseInt(fdArea)==10001){//车站
                    //名称：线路号+车站\场段名
                    //车站\场段名多选，进行处理
                    String stations = Optional.ofNullable(result.get(i).get("stationNumber")).map(Object::toString).orElse("");
                    String[] stationList = stations.split(",");
                    StringBuffer stationBuilt = new StringBuffer();
                    for (int j = 0; j < stationList.length; j++) {
                        String stationId = stationList[j];
                        String stationName = stationAllList.stream().filter(data -> stationId.equals(data.get("sta_id"))).map(s -> s.get("sta_cname")).collect(Collectors.joining(","));
                        stationBuilt.append(stationName).append(",");
                    }
                    String stationsName = stationBuilt.toString().substring(0,stationBuilt.toString().length()-1);
                    eventPlace = lineName + " " + stationsName;
                }else if (Integer.parseInt(fdArea)==10002){//区间
                    //名称：线路号+上行或下行或上下行+车站名-车站名
                    //查询上下行中文名
                    String direction = "";
                    if (StringUtils.isNotBlank(Optional.ofNullable(result.get(i).get("direction")).map(Object::toString).orElse(""))){
                        switch ((String)result.get(i).get("direction")){
                            case "up" :
                                direction =  "上行";
                                break;
                            case "down":
                                direction = "下行";
                                break;
                            case "all":
                                direction = "上下行";
                                break;
                            default:
                                direction = "未知";
                        }
                    }
                    String startStationId = Optional.ofNullable(result.get(i).get("Sstation")).map(Object::toString).orElse("");
                    String startStationName = stationAllList.stream().filter(data -> startStationId.equals(data.get("sta_id"))).map(s -> s.get("sta_cname")).collect(Collectors.joining(","));

                    String endStationId = Optional.ofNullable(result.get(i).get("Estation")).map(Object::toString).orElse("");;
                    String endStationName = stationAllList.stream().filter(data -> endStationId.equals(data.get("sta_id"))).map(s -> s.get("sta_cname")).collect(Collectors.joining(","));

                    eventPlace = lineName + " " + direction + " " + startStationName + " - " + endStationName;
                }
            }
            result.get(i).put("fdPlace",eventPlace);
            //查询预案名称--旧
//            String eventPlan = Optional.ofNullable(result.get(i).get("plan")).map(Object::toString).orElse("");
//            String eventPlanName = planList.stream().filter(plan -> eventPlan.equals(plan.get("planUuid"))).map(plan -> plan.get("planName"))
//                    .collect(Collectors.joining());

            //查询预案名称
            String eventPlanJson = Optional.ofNullable(result.get(i).get("plan")).map(Object::toString).orElse("");
            String eventPlanName = "";
            if (StringUtils.isNotBlank(eventPlanJson)){
                Map<String,List> planMap = JSONObject.parseObject(eventPlanJson, Map.class);
                List<String> planIdsList =  (List<String>)planMap.get("planNamesList");
                eventPlanName = planIdsList.stream().collect(Collectors.joining(","));
            }

            result.get(i).put("fdPlanName",eventPlanName);
        }
    }

    /**
     * 查询预案名称（只能单条线路查询）
     * @param inInfo 参数：PlanLine 线路编号，（nocc.yj.yg01）里的小代码
     * @return
     */
    public static EiInfo queryPlanInfo(EiInfo inInfo){
        EiInfo outInfo = new EiInfo();
        try {
            inInfo.set("planLine","140001");//默认查NOCC类下的应急预案
            outInfo = EiInfoUtils.callParam("S_YJ_YGCX_01",inInfo).build();

        }catch (Exception e){
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    /**
     * * 定时任务  每天凌晨5点
     * * 平台微服务ID:S_YJ_YL02
     * * 平台定时任务编号:J_YL_01
     * * 平台触发器编号:J_YL_01
     * * 定时任务：每天凌晨5点 ： 0 0 5 * * ?
     * 定时同步安全系统演练数据
     * @param inInfo
     * @return
     */
    public EiInfo timerUpdateDrill(EiInfo inInfo) {
        try {
            inInfo = yjyl01DoPost7dayTime();//获取安全系统进7天的演练数据
            yjylDrillUpdate(inInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return inInfo;
    }



    /**
     * 处理接收的应急演练状态信息
     * 微服务：S_YJ_YL_04
     * @return
     */
    public static EiInfo getYLStatusInfo(Map<String, Object> mapInfo){
        logger.info("处理接收的应急演练状态信息。微服务：S_YJ_YL_04。");
        EiInfo inInfo = new EiInfo();
        try {
            String drillUUID = Optional.ofNullable(mapInfo.get("event_uuid")).map(Object::toString).orElse("");//演练ID
            int drillStatus = Optional.ofNullable(Convert.toInt(mapInfo.get("event_status"))).orElse(0); //演练状态event_status
            int from = Convert.toInt(Optional.ofNullable(mapInfo.get("from")).orElse(0)); //调用方法来源
            String UpdateBy = Optional.ofNullable(mapInfo.get("UpdateBy")).map(Object::toString).orElse("");//修改人
            String UpdateTime = Optional.ofNullable(mapInfo.get("UpdateTime")).map(Object::toString).orElse("");//修改时间
            Map map = new HashMap();
            map.put("drillUUID",drillUUID);
            List query = dao.query("YJYL01.query", map);
            if (CollectionUtil.isEmpty(query)){//新增事件
                throw new PlatException("该应急演练未找到，数据缺少，请刷新应急演练页面同步演练信息！");
            }else{//更新演练状态
                map.put("fdUuid",drillUUID);
                map.put("drillStatus",drillStatus);
                map.put("UpdateBy",UpdateBy);
                map.put("UpdateTime",UpdateTime);
                if(drillStatus == 190001){
                    map.put("status",3);//待确认
                }else if(drillStatus == 190003){
                    map.put("status",5);//演练中
                }
                dao.update("YJYL01.updateDrillStatus",map);//修改演练表的状态
                dao.update("YJYL01.updateDrillInfoState",map);//修改演练信息表的状态
                if(from == 1){
                    //判断from为1时，需要把状态返回给智能调度
                    map.put("event_uuid",drillUUID);//设置智能应急调度需要的字段演练id
                    map.put("event_status",drillStatus);//设置智能应急调度需要的字段演练状态
                    EiInfo outInfo1 = FromZNDDYLStatus(map);
                }
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }
        }catch (Exception e){
            inInfo.setMsg(e.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return inInfo;
    }

    /**
     * 演练状态同步上传智能调度
     * 微服务：S_YJ_YL03
     * @return
     */
    public static EiInfo FromZNDDYLStatus(Map<String, Object> map){
        logger.info("演练状态同步上传智能调度。微服务：S_YJ_YL03。");
        EiInfo inInfo = new EiInfo();
        inInfo.setAttr(map);
        EiInfo outInfo = EiInfoUtils.callParam("S_YJ_YL03", inInfo).build();
        return outInfo;
    }


}
