package com.baosight.cmp.yj.zy.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.cmp.common.util.eiinfo.EiInfoUtils;
import com.baosight.cmp.yj.zy.common.constants.YJZYConstants;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ei.parser.EiInfoParserFactory;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import redis.clients.jedis.Jedis;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.baosight.cmp.common.CYUtils.querySectInfo;
import static com.baosight.cmp.common.CYUtils.queryStation;
import static com.baosight.cmp.yj.zy.service.ServiceYJZY0101.formatDataForSceneDPFX;
import static com.baosight.cmp.yj.zy.common.ZYUtil.*;


/**
 * @description 应急资源(GIS)后台服务逻辑汇总
 * @author: yanghuanbo
 * @date: 2023/09/12 13:19:21
 * 含：
 * ①、获取天气实况、天气预报数据（走redis）
 * ②、与智能应急调度系统-人员定位数据
 * ③、获取应急物资数据
 * ④、获取应急值守点数据
 * ⑤、获取处置中的应急事件信息
 * ⑥、获取未解除气象、地震预警信息（走websocket将数据推送到前端）
 * ⑦、关键词搜索功能实现
 * ⑧、获取车站信息基础信息
 */
@Slf4j
public class ServiceYJZY01 extends ServiceBase {

    private static Logger logger = LoggerFactory.getLogger(ServiceYJZY01.class);

    private static Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");
    /////////////redis相关/////////////////////
    //引用 RedisTemplate
    private static RedisTemplate<String,Object> redisTemplate = (RedisTemplate) PlatApplicationContext.getApplicationContext().getBean("redisTemplate");
    public static String redisHost = PlatApplicationContext.getProperty("spring.redis.host");
    private static int redisPort = Integer.parseInt(PlatApplicationContext.getProperty("spring.redis.port"));
//    public static String redisPassword = PlatApplicationContext.getProperty("spring.redis.password");

    /////////////redis相关/////////////////////


    private static final Map<String, String> INTERVAL_IMG_MAPPING = new HashMap<>();
    //初始化map，url后缀
    static {
        //1号线-区间图
        INTERVAL_IMG_MAPPING.put("0100000000", "./station/line1.png");
        //2号线-区间图
        INTERVAL_IMG_MAPPING.put("0200000000", "./station/line2.png");
        //3号线-区间图
        INTERVAL_IMG_MAPPING.put("0300000000", "./station/line3.png");
        //4号线-区间图
        INTERVAL_IMG_MAPPING.put("0400000000", "./station/line4.png");
        //5号线-区间图
        INTERVAL_IMG_MAPPING.put("0500000000", "./station/line5.png");
//        INTERVAL_IMG_MAPPING.put("0500000000", "https://static.699pic.com/index_multi/2023/03/0nz4hhpn8q8ii3pl.png";);
    }

    //初始化拼接sql条件
    private static final Map<Integer, String> JOIN_SQL_CONDITION_MAPPING = new HashMap<>();
    static {
        //查询应急物资数据
        JOIN_SQL_CONDITION_MAPPING.put(1, "martialData_keywordSearch");
        //查询应急值守点数据
        JOIN_SQL_CONDITION_MAPPING.put(2, "dutyPointData_keywordSearch");
        //查询应急资源基础数据
        JOIN_SQL_CONDITION_MAPPING.put(3, "baseData_keywordSearch");
        //查询业务数据(应急物资、应急值守点)
        JOIN_SQL_CONDITION_MAPPING.put(4, "buinessData_keywordSearch");
        //查询应急物资数据与基础数据(应急物资、基础资源)
        JOIN_SQL_CONDITION_MAPPING.put(5, "maritalsAndBaseData_keywordSearch");
        //查询应急值守点数据与基础数据(应急值守点、基础资源)
        JOIN_SQL_CONDITION_MAPPING.put(6, "dutyPointAndBaseData_keywordSearch");
    }

    //天气实况-设定区域
    private List districtNameList = new ArrayList(Arrays.asList("南宁市", "兴宁区", "西乡塘区", "江南区", "良庆区", "青秀区"));




    /**
     * 返回GIS中的天气内容
     * 微服务ID：S_YJ_ZY_04
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getWeatherInfo(EiInfo inInfo) {
        // 配置Redis连接信息
        Jedis jedis = new Jedis(redisHost, redisPort);
        try {
            if(!isRedisAvailable(redisHost,redisPort)){
                inInfo.set("list", Collections.emptyList());
                logger.error("获取天气接口失败===》{}", "redis service is not running！");
                return EiInfoUtils.setError("redis service is not running！");
            }
            String currentWeatherJsonStr = jedis.get(YJZYConstants.REDISKEY_WEATHER_CURRENT_GIS);//天气实况
            String weatherForecastJosnStr = jedis.get(YJZYConstants.REDISKEY_WEATHER_FORECAST_GIS);//天气预报
            if (currentWeatherJsonStr != null && weatherForecastJosnStr != null) {
                List callbackList = new ArrayList();
                List<Map<String, Object>> windSpLevelList = dao.query("YJZY01.queryWidSpLevel", new HashMap<>());
                List<Map<String, Object>> weatherDescList = dao.query("YJZY01.queryWeatherDesc", new HashMap<>());
                EiInfo currentWeatherEiInfo = EiInfoParserFactory.getParser("json").parse(currentWeatherJsonStr);
                EiInfo weatherForecastEiInfo = EiInfoParserFactory.getParser("json").parse(weatherForecastJosnStr);
                //天气实况信息
                List<Map<String, Object>> result = currentWeatherEiInfo.getBlock("result").getRows();
                //天气预报信息
                List<Map<String, Object>> weatherResult = weatherForecastEiInfo.getBlock("result").getRows();
                //获取指定行政区的天气信息
                result.stream().filter(fil->districtNameList.contains(fil.get("one_name")))
                        .forEach(currentWeather -> {
                    Map saveMap = new HashMap();
                    saveMap.put("districtName", currentWeather.get("one_name"));//区县名称
                    saveMap.put("airTemperature", currentWeather.get("air_temperature"));//当前温度
                    saveMap.put("precipitation", currentWeather.get("precipitation"));//过去一小时降水量
                    saveMap.put("windDirection", currentWeather.get("wind_direction"));//风向
                    //风级
                    saveMap.put("windLevel", getWindLevelStr(currentWeather.get("wind_velocity").toString(), windSpLevelList));
                    //天气现象描述
                    saveMap.put("weatherDesc", getWeatherDescStr(currentWeather.get("weather").toString(), weatherDescList));
                    //天气图标
                    String weatherIconStr = getWeatherIconStr(weatherResult,currentWeather);
                    saveMap.put("weatherIcon", weatherIconStr);
                    callbackList.add(saveMap);
                });
                inInfo.set("list", callbackList);
            }
        } catch (PlatException platException) {
            logger.error("获取天气接口失败===》{}", platException.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("获取天气接口失败===》" + platException.getMessage());
        } finally {
            //释放资源
            jedis.close();
        }
//        //本地mock数据 start
//        inInfo.set("list", ZYMOCK.getInstance().getWeatherinfoOfMockData());
//        //本地mock数据 end
        return inInfo;
    }

    /**
     * 返回人员定位接口数据
     * 微服务ID：S_YJ_ZY_05
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getPersonPositionInfo(EiInfo inInfo) {
        try {
            List<Map<String, String>> stationsInfoList = stationsFormat();
            StopWatch stopWatch = new StopWatch();
            //开启计时
            stopWatch.start();
            //调用智能应急调度--人员定位接口
            inInfo.set(EiConstant.serviceId,"S_YJ_ZY_08");
            EiInfo outInfo = XServiceManager.call(inInfo);
            List<Map> personPositionInfoList = (List<Map>) outInfo.get("data");
            personPositionInfoList.stream().forEach(data->{
                String longitude = (String) Optional.ofNullable(data.get("longitude")).orElseGet(()->"");
                String latitude = (String) Optional.ofNullable(data.get("latitude")).orElseGet(()->"");
                if(StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude)){
                    List<Map> mapList = fillDataByStationCode(stationsInfoList, data);
                    data.put("longitude",mapList.get(0).get("longitude"));
                    data.put("latitude",mapList.get(0).get("latitude"));
                }
            });
            inInfo.set("data", personPositionInfoList);
            //关闭计时
            stopWatch.stop();
            //统计接口调用总耗时(单位：秒)
            double getTotalTimeSeconds = stopWatch.getTotalTimeSeconds();
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("获取人员定位接口成功！总耗时：" + getTotalTimeSeconds + " 秒！");
            logger.info("获取人员定位接口成功！总耗时：{} ", getTotalTimeSeconds + " 秒！");

            ////////TEST START //////////////////////
//          inInfo.set("data", ZYMOCK.getInstance().getPersonPositionOfMockData());
            ////////TEST END //////////////////////

        } catch (Exception exception) {
            exception.printStackTrace();
            logger.error("获取人员定位接口失败！{} ", exception.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("获取人员定位接口失败==>" + exception.getMessage());
        }
        return inInfo;
    }





    /**
     * 获取应急物资信息（给GIS用）
     * serviceId：S_YJ_ZY_06
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getGoodsMaterlsInfo(EiInfo inInfo) {
        try {
            boolean redisAvailable = isRedisAvailable(redisHost, redisPort);
            //获取基础数据中的车站信息
            List<Map<String, String>> stationList = stationsFormat();
            //应急物资uuid
            String goodsId = (String) Optional.ofNullable(inInfo.get("uuid")).orElseGet(() -> "");
            //车站编码
            String positionId = (String) Optional.ofNullable(inInfo.get("positionId")).orElseGet(() -> "");
            //车站名称
            String position = (String) Optional.ofNullable(inInfo.get("position")).orElseGet(() -> "");
            List positionItem = (List) Optional.ofNullable(inInfo.get("positionItem")).orElseGet(() -> new ArrayList());
            //线路编码
            String lineCode = (String) Optional.ofNullable(inInfo.get("lineCode")).orElseGet(() -> "");
            //线路名称
            String lineName = (String) Optional.ofNullable(inInfo.get("lineName")).orElseGet(() -> "");
            int offset = Optional.ofNullable(inInfo.get("offset"))
                    .map(data -> { if (!(data instanceof Integer)) throw new PlatException("The offset parameter must be of type int");
                        return (int) data;})
                    .orElse(0);
            int limit = Optional.ofNullable(inInfo.get("limit"))
                    .map(data -> { if (!(data instanceof Integer)) throw new PlatException("The limit parameter must be of type int");
                        return (int) data;})
                    .orElse(-999999);

            Map param = new HashMap();
//            param.put("uuid", goodsId);//注释：搜索时的弹窗列表需要展示此车站内所有物资且按照搜素的关键词匹配度由高到低排序
            param.put("positionId", positionId);
            param.put("position", position);
            param.put("positionItem", positionItem);
            param.put("lineCode", lineCode);
            param.put("lineName", lineName);

            //////校验输入的关键词是否是车站-如果是车站，则关键词不作为物资名称搜索条件，不是车站-则关键词作为物资名称搜素条件
            String materialsName = (String) Optional.ofNullable(inInfo.get("materialsName")).orElseGet(() -> "");
            param.put("materialsName", materialsName);
//            param.put("materialsName", keywordIsStation(materialsName) ? "" : materialsName);

            List<Map<String, String>> goodsDataList = new ArrayList<>();
            if(redisAvailable && redisTemplate.hasKey(YJZYConstants.REDISKEY_MATERIAL_INFO_GIS)){
                goodsDataList = getMaterialsFromRedis(YJZYConstants.REDISKEY_MATERIAL_INFO_GIS,param);
            }else{
                goodsDataList = dao.query("YJZY01.queryGoodsMarterials", param, offset * limit, limit);
            }
            List<Map<String, String>> goodsList = goodsDataList;
//            List<Map<String, String>> goodsList = dao.query("YJZY01.queryGoodsMarterials", param, offset * limit, limit);
            List<Map<String, String>> goodsByStaNameList = dao.query("YJZY01.queryGoodsMarterialsByStaName", param);
            if (CollectionUtil.isEmpty(goodsList) || CollectionUtil.isEmpty(goodsByStaNameList)) {
                inInfo.set("data", new ArrayList<>());
                return EiInfoUtils.setError(inInfo, "no emergency materials records were found ! ");
            }
            List<Map> callbackGoodsInfoList_temp = new ArrayList();
            //根据车站分组后的结果集
            goodsByStaNameList.stream().forEach(groupInfo -> {
                Map sameStationMap = new HashMap<>();
                List<Map<String, Object>> lineGoodsInfoList = new ArrayList();
                List comparelineList = new ArrayList();
                //下面的方法如果能循环且循环大于1次则代表是[换乘站]
                stationList.stream().filter(staFil -> groupInfo.get("position").equals(staFil.get("sta_cname")) )
                        .forEach(staInfo -> {
                            List sameStationList = new ArrayList();//存放同一车站的多个物资信息
                            //同线路信息
                            Map lineMap = new HashMap();
                            goodsList.stream().filter(goods -> goods.get("position")
                                            .equals(staInfo.get("sta_cname").toString())
                                            && goods.get("lineCode").equals(staInfo.get("line_id").toString()))
                                    .forEach(goodsInfo -> {
                                        if (sameStationMap.isEmpty()) {//车站信息（如车站名称、经、纬度等）
                                            //车站信息
                                            sameStationMap.put("sta_id", staInfo.get("sta_id"));
                                            sameStationMap.put("sta_cname", staInfo.get("sta_cname"));
                                            sameStationMap.put("sta_longitude", staInfo.get("sta_longitude"));
                                            sameStationMap.put("sta_dimension", staInfo.get("sta_dimension"));
                                        }
                                        //根据线路编码，判断是否已经添加过同车站但不同线路的车站（换乘站）
                                        boolean matchState = comparelineList.stream()
                                                .anyMatch(fil -> fil.equals(staInfo.get("line_id").toString()));
                                        //线路分组基本信息
                                        if (CollectionUtil.isEmpty(comparelineList) || !matchState) {
                                            //车站信息
                                            lineMap.put("sta_id", staInfo.get("sta_id"));
                                            lineMap.put("sta_cname", staInfo.get("sta_cname"));
                                            lineMap.put("line_id", staInfo.get("line_id"));
                                            lineMap.put("line_cname", staInfo.get("line_cname"));
                                            //物资信息
                                            lineMap.put("departmentName", goodsInfo.get("departmentName"));
                                            lineMap.put("subCenterName", goodsInfo.get("subCenterName"));
                                            lineMap.put("teamName", goodsInfo.get("teamName"));
                                            comparelineList.add(staInfo.get("line_id").toString());
                                            //lineGoodsInfoList.add(lineMap);
                                        }
                                        sameStationList.add(goodsInfo);
                                    });
                            if (CollectionUtil.isNotEmpty(sameStationList)) {
                                lineMap.put("goodsInfo", sameStationList);
                                lineGoodsInfoList.add(lineMap);
                                sameStationMap.put("lineMap", lineGoodsInfoList);
                            }
                        });
                if (!sameStationMap.isEmpty()) callbackGoodsInfoList_temp.add(sameStationMap);
            });
            List<Map> callbackGoodsInfoList = new ArrayList();
            callbackGoodsInfoList_temp.stream().forEach(data -> {
                boolean state = ((List<Map<String, List>>) data.get("lineMap"))
                        .stream().anyMatch(fil -> ((List) fil.get("goodsInfo")).size() > 0 ? true : false);
                if (state) {
                    callbackGoodsInfoList.add(data);
                }
            });
            //2024-07-17 新村停车场 特殊处理 start
            List<Map> orignList_temp = callbackGoodsInfoList.stream()
                    .filter(fi -> "新村停车场".equals(fi.get("sta_cname")))
                    .collect(Collectors.toList());
            List<Map> newCallbackList = sepcialPark(orignList_temp,callbackGoodsInfoList, stationList);
            //2024-07-17 新村停车场 特殊处理 end
            inInfo.set("data", CollectionUtil.isNotEmpty(orignList_temp) ? newCallbackList : callbackGoodsInfoList);
            inInfo.set("offset", offset);
            inInfo.set("limit", limit);
            inInfo.set("totalCounts", CollectionUtil.isNotEmpty(orignList_temp) ? newCallbackList.size() : callbackGoodsInfoList.size());//记录总条数
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("GIS获取应急物资信息成功！");

        } catch (PlatException exception) {
            logger.error("GIS获取应急物资出错====>{}", exception.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("GIS获取应急物资出错====>" + exception.getMessage());
        }
        return inInfo;
    }


    /**
     * 从redis中获取格式化前的应急物资数据
     * @param rediskey redis的key
     * @param params   匹配查询参数
     * @return List<Map<String,String>>
     */
    public static List<Map<String,String>> getMaterialsFromRedis(String rediskey,Map params) {
        List<Map<String, String>> dataFromRedis = getDataByRedis(rediskey);
        return dataFromRedis.stream().filter(materialMap -> {
            boolean positionIdState = true,positionItemState = true, lineCodeState = true, lineNameState = true;
            if (StringUtils.isNotBlank((String)params.get("positionId")))
                positionIdState = materialMap.get("positionId").equals((String)params.get("positionId"));
            if (StringUtils.isNotBlank((String)params.get("lineCode")))
                lineCodeState = materialMap.get("lineCode").equals((String)params.get("lineCode"));
            if (StringUtils.isNotBlank((String)params.get("lineName")))
                lineNameState = materialMap.get("lineName").equals((String)params.get("lineName"));
            if(CollectionUtil.isNotEmpty((List)params.get("positionItem"))){
                List staItem = (List)params.get("positionItem");
                positionItemState = staItem.contains(materialMap.get("position"));
            }
            return positionItemState && positionIdState && lineCodeState && lineNameState;
        }).collect(Collectors.toList());
    }

    /**
     *  新村停车场特殊处理
     *  原因：新村停车场在实际情况中就是两个不同地点的建筑物（2号线新村停车场与3号线新村停车场），
     *  线路与数据均不相同，不需要合并所谓的换乘站，需要对其做拆分处理（普通站点处理）
     * @param orignList_temp        过滤出存在新村停车场的数据
     * @param preparCallbackResult  已组装好准备返回接口数据内容
     * @param stationList   车站信息
     * @return List<Map>
     * @throws RuntimeException
     */
    private List<Map> sepcialPark(List<Map> orignList_temp,List<Map> preparCallbackResult,List<Map<String, String>> stationList) throws RuntimeException {
        List<Map> newList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(orignList_temp)){
            //1.先将不属于新村停车场的数据保留
            newList = preparCallbackResult.stream().filter(fi -> !"新村停车场".equals(fi.get("sta_cname")))
                    .collect(Collectors.toList());
            //2.记录当前本线自己的车站编码，留着做对比邻线用
            String staId = orignList_temp.get(0).get("sta_id").toString();
            List<Map> list_lineMap = (List)orignList_temp.get(0).get("lineMap");
            Map OtherLineMap_temp = new HashMap();//临时存储邻线的新村停车场数据
            String otherStaId = "";//临时存储邻线的车站编码
            //3.删除邻线的新车场停车场数据
            for (int i = 0; i <list_lineMap.size() ; i++) {
                if(!list_lineMap.get(i).get("sta_id").equals(staId)){//判断当前车站编码与邻线车站编码是否一致
                    OtherLineMap_temp = list_lineMap.get(i);
                    otherStaId = list_lineMap.get(i).get("sta_id").toString();
                    list_lineMap.remove(i);//删除另条线路的新村停车场数据，此时list_lineMap里面只剩下与自己本线路相关的数据
                }
            }
            //4.重新组装新村停车场数据
            //4.1重新组装新村停车场数据--与自己最原始的数据一致
            Map orignMap = orignList_temp.get(0);
            Map thisLineMap = formatMaterialBase(orignMap.get("sta_id").toString(),orignMap.get("sta_cname").toString(),orignMap.get("sta_longitude").toString(),
                    orignMap.get("sta_dimension").toString(),list_lineMap.get(0));

            //4.2重新组装新村停车场数据--邻线的数据
            String finalOtherStaId = otherStaId;
            Map finalMapTemp = OtherLineMap_temp;
            List<Map> oteherListMap_format = stationList.stream()
                    .filter(fi -> finalOtherStaId.equals(fi.get("sta_id")))
                    .map(info -> {
                        return formatMaterialBase(info.get("sta_id"), info.get("sta_cname"), info.get("sta_longitude"),
                                info.get("sta_dimension"), finalMapTemp);
                    }).collect(Collectors.toList());
            //5.将重新组装好的新村停车场相关数据再此加入到已原剔除“新村停车场”的数据中
            newList.add(thisLineMap);
            if(CollectionUtil.isNotEmpty(oteherListMap_format)){
                newList.add(oteherListMap_format.get(0));
            }
        }
        return newList;
    }


    /**
     * 格式化应急物资在GIS上撒点展示的数据
     * @param sta_id 车站编码
     * @param sta_cname 车站名称
     * @param sta_longitude 经度
     * @param sta_dimension 纬度
     * @param lineMap 线路信息
     * @return Map
     */
    private Map formatMaterialBase(String sta_id,String sta_cname,String sta_longitude,String sta_dimension,Map lineMap){
        Map map = new HashMap();
        map.put("sta_id", sta_id);
        map.put("sta_cname", sta_cname);
        map.put("sta_longitude", sta_longitude);
        map.put("sta_dimension", sta_dimension);
        List lineMapList = new ArrayList();
        lineMapList.add(lineMap);
        map.put("lineMap", lineMapList);
        return map;
    }


    /**
     * 获取应急值守点信息（给GIS用）
     * serviceId：S_YJ_ZY_07
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getDutyInfoInfo(EiInfo inInfo) {
        try {
            //获取基础数据中的车站信息
            List<Map<String, String>> stationList = stationsFormat();
            String dutyPointId = (String) Optional.ofNullable(inInfo.get("uuid")).orElseGet(() -> "");//应急值守点UUID
            String dutyBuildingId = (String) Optional.ofNullable(inInfo.get("dutyBuildingId")).orElseGet(() -> ""); //车站编码
            String dutyBuilding = (String) Optional.ofNullable(inInfo.get("dutyBuilding")).orElseGet(() -> "");//车站名称
            List positionItem = (List) Optional.ofNullable(inInfo.get("positionItem")).orElseGet(() -> new ArrayList());
            String lineCode = (String) Optional.ofNullable(inInfo.get("lineCode")).orElseGet(() -> "");//线路编码
            String lineName = (String) Optional.ofNullable(inInfo.get("lineName")).orElseGet(() -> "");//线路名称
            int offset = Optional.ofNullable(inInfo.get("offset"))
                    .map(data -> { if (!(data instanceof Integer)) throw new PlatException("The offset parameter must be of type int");
                        return (int) data;})
                    .orElse(0);
            int limit = Optional.ofNullable(inInfo.get("limit"))
                    .map(data -> { if (!(data instanceof Integer)) throw new PlatException("The limit parameter must be of type int");
                        return (int) data;})
                    .orElse(-999999);
            Map param = new HashMap();
            param.put("uuid", dutyPointId);
            param.put("dutyBuildingId", dutyBuildingId);
            param.put("dutyBuilding", dutyBuilding);
            param.put("positionItem", positionItem);
            param.put("lineCode", lineCode);
            param.put("lineName", lineName);

            List<Map<String, String>> dutyPointDataList = new ArrayList<>();
            if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(YJZYConstants.REDISKEY_DUTYPOINT_INFO_GIS)){
                dutyPointDataList = getDutyPointFromRedis(YJZYConstants.REDISKEY_DUTYPOINT_INFO_GIS,param);
            }else{
                dutyPointDataList = dao.query("YJZY01.queryDutyPointInfo", param, offset * limit, limit);
            }
            List<Map<String, String>> dutyPointList = dutyPointDataList;

//            List<Map<String, String>> dutyPointList = dao.query("YJZY01.queryDutyPointInfo", param, offset * limit, limit);
            List<Map<String, String>> dutyPointListByStaCode = dao.query("YJZY01.queryDutyPointInfoByStaCode", param);
            if (CollectionUtil.isEmpty(dutyPointList) || CollectionUtil.isEmpty(dutyPointListByStaCode)) {
                inInfo.set("data", new ArrayList<>());
                return EiInfoUtils.setError(inInfo, "no dutyPoint records were found ! ");
            }
            //应急值守点格式化组装
            List<Map>callbackDutyPointInfoList_temp = formatDutyPointData(dutyPointList,dutyPointListByStaCode,stationList);
            List<Map> callbackDutyPointInfoList = new ArrayList();
            callbackDutyPointInfoList_temp.stream().forEach(data -> {
                boolean state = ((List<Map<String, List>>) data.get("lineMap"))
                        .stream().anyMatch(fil -> ((List) fil.get("dutyPointInfo")).size() > 0 ? true : false);
                if (state) {
                    callbackDutyPointInfoList.add(data);
                }
            });

            //2024-07-20 新村停车场 特殊处理 start
            List<Map> orignList_temp = callbackDutyPointInfoList.stream()
                    .filter(fi -> "新村停车场".equals(fi.get("dutyBuilding")))
                    .collect(Collectors.toList());
            List<Map> newCallbackList = sepcialParkForDutyPoint(orignList_temp,callbackDutyPointInfoList, stationList);
            //2024-07-20 新村停车场 特殊处理 end
            inInfo.set("data", CollectionUtil.isNotEmpty(newCallbackList) ? newCallbackList : callbackDutyPointInfoList);
            inInfo.set("totalCounts", CollectionUtil.isNotEmpty(newCallbackList) ? newCallbackList.size() : callbackDutyPointInfoList.size());//记录总条数
            inInfo.set("offset", offset);
            inInfo.set("limit", limit);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("GIS获取应急值守点信息成功！");
        } catch (PlatException exception) {
            logger.error("GIS获取应急值守点出错====>{}", exception.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("GIS获取应急值守点出错====>" + exception.getMessage());
        }
        return inInfo;
    }


    /**
     * 从redis中获取格式化前的应急值守点数据
     * @param rediskey redis的key
     * @param params   匹配查询参数
     * @return List<Map<String,String>>
     */
    private List<Map<String,String>> getDutyPointFromRedis(String rediskey,Map params) {
        List<Map<String, String>> dataFromRedis = getDataByRedis(rediskey);
        return dataFromRedis.stream().filter(dataMap -> {
            boolean dutyBuildingIdState = true,positionItemState = true, lineCodeState = true, lineNameState = true;
            if (StringUtils.isNotBlank(params.get("dutyBuildingId").toString()))
                dutyBuildingIdState = dataMap.get("dutyBuildingId").equals(params.get("dutyBuildingId").toString());
            if (StringUtils.isNotBlank(params.get("lineCode").toString()))
                lineCodeState = dataMap.get("lineCode").equals(params.get("lineCode").toString());
            if (StringUtils.isNotBlank(params.get("lineName").toString()))
                lineNameState = dataMap.get("lineName").equals(params.get("lineName").toString());
            if(CollectionUtil.isNotEmpty((List)params.get("positionItem"))){
                List staItem = (List)params.get("positionItem");
                positionItemState = staItem.contains(dataMap.get("dutyBuilding"));
            }
            return positionItemState && dutyBuildingIdState && lineCodeState && lineNameState;
        }).collect(Collectors.toList());
    }


    /**
     * 应急值守点格式化组装
     * @param dutyPointList 数据库中的所有应急值守点数据
     * @param dutyPointListByStaCode 数据库中根据车站编码分组后的应急值守点站点信息
     * @param stationList 车站基础数据
     * @return List<Map>
     * @throws RuntimeException
     */
    private List<Map>formatDutyPointData(List<Map<String, String>> dutyPointList,List<Map<String, String>> dutyPointListByStaCode,
                                     List<Map<String, String>>stationList) throws RuntimeException{
        List<Map> callbackDutyPointInfoList_temp = new ArrayList();
        //根据车站分组后的结果集
        dutyPointListByStaCode.stream().forEach(groupInfo -> {
            Map sameStationMap = new HashMap<>();
            List<Map<String, Object>> lineGoodsInfoList = new ArrayList();
            List comparelineList = new ArrayList();
            //下面的方法如果能循环且循环大于1次则代表是[换乘站]
            stationList.stream().filter(staFil -> groupInfo.get("dutyBuilding").equals(staFil.get("sta_cname")) )
                    .forEach(staInfo -> {
                        List sameStationList = new ArrayList();//存放同一车站的多个值守点信息
                        //同线路信息
                        Map lineMap = new HashMap();
                        dutyPointList.stream().filter(staFil_dp -> staFil_dp.get("dutyBuilding").equals(staInfo.get("sta_cname").toString())
                                        && staInfo.get("sta_id").toString().equals(staFil_dp.get("dutyBuildingId")))
                                .forEach(dutyPointInfo -> {
                                    if (sameStationMap.isEmpty()) {//车站信息（如车站名称、经、纬度等）
                                        //车站信息
                                        sameStationMap.put("major", dutyPointInfo.get("major"));
                                        sameStationMap.put("lineCode", dutyPointInfo.get("lineCode"));
                                        sameStationMap.put("lineName", dutyPointInfo.get("lineName"));
                                        sameStationMap.put("dutyBuildingId", staInfo.get("sta_id"));
                                        sameStationMap.put("dutyBuilding", staInfo.get("sta_cname"));
                                        sameStationMap.put("staLongitude", staInfo.get("sta_longitude"));//车站经度
                                        sameStationMap.put("staDimension", staInfo.get("sta_dimension"));//车站纬度
                                    }
                                    //根据线路编码，判断是否已经添加过同车站但不同线路的车站（换乘站）
                                    boolean matchState = comparelineList.stream()
                                            .anyMatch(fil -> fil.equals(staInfo.get("line_id").toString()));
                                    //线路分组基本信息
                                    if (CollectionUtil.isEmpty(comparelineList) || !matchState) {
                                        //车站信息
                                        lineMap.put("dutyBuildingId", staInfo.get("sta_id"));
                                        lineMap.put("dutyBuilding", staInfo.get("sta_cname"));
                                        lineMap.put("lineCode", staInfo.get("line_id"));
                                        lineMap.put("lineName", staInfo.get("line_cname"));
                                        //值守点信息
                                        lineMap.put("teamName", dutyPointInfo.get("teamName"));
                                        lineMap.put("major", dutyPointInfo.get("major"));
                                        comparelineList.add(staInfo.get("line_id").toString());
                                    }
                                    sameStationList.add(dutyPointInfo);
                                });
                        if (CollectionUtil.isNotEmpty(sameStationList)) {
                            lineMap.put("dutyPointInfo", sameStationList);
                            lineGoodsInfoList.add(lineMap);
                            sameStationMap.put("lineMap", lineGoodsInfoList);
                        }
                    });
            if (!sameStationMap.isEmpty()) callbackDutyPointInfoList_temp.add(sameStationMap);
        });
        return callbackDutyPointInfoList_temp;
    }

    /**
     *  新村停车场特殊处理
     *  原因：新村停车场在实际情况中就是两个不同地点的建筑物（2号线新村停车场与3号线新村停车场），
     *  线路与数据均不相同，不需要合并所谓的换乘站，需要对其做拆分处理（普通站点处理）
     * @param orignList_temp        过滤出存在新村停车场的数据
     * @param preparCallbackResult  已组装好准备返回接口数据内容
     * @param stationList   车站信息
     * @return List<Map>
     * @throws RuntimeException
     */
    private List<Map> sepcialParkForDutyPoint(List<Map> orignList_temp,List<Map> preparCallbackResult,List<Map<String, String>> stationList) throws RuntimeException {
        List<Map> newList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(orignList_temp)){
            //1.先将不属于新村停车场的数据保留
            newList = preparCallbackResult.stream().filter(fi -> !"新村停车场".equals(fi.get("dutyBuilding")))
                    .collect(Collectors.toList());
            //2.记录当前本线自己的车站编码，留着做对比邻线用
            String staId = orignList_temp.get(0).get("dutyBuildingId").toString();
            List<Map> list_lineMap = (List)orignList_temp.get(0).get("lineMap");
            Map OtherLineMap_temp = new HashMap();//临时存储邻线的新村停车场数据
            String otherStaId = "";//临时存储邻线的车站编码
            String major = "";//临时存储邻线的专业内容
            //3.删除邻线的新车场停车场数据
            for (int i = 0; i <list_lineMap.size() ; i++) {
                if(!list_lineMap.get(i).get("dutyBuildingId").equals(staId)){//判断当前车站编码与邻线车站编码是否一致
                    OtherLineMap_temp = list_lineMap.get(i);
                    otherStaId = list_lineMap.get(i).get("dutyBuildingId").toString();
                    major = list_lineMap.get(i).get("major").toString();
                    list_lineMap.remove(i);//删除另条线路的新村停车场数据，此时list_lineMap里面只剩下与自己本线路相关的数据
                }
            }
            //4.重新组装新村停车场数据
            //4.1重新组装新村停车场数据--与自己最原始的数据一致
            Map orignMap = orignList_temp.get(0);
            Map thisLineMap = formatDutyPointBase(orignMap.get("lineCode").toString(),orignMap.get("lineName").toString(),orignMap.get("dutyBuildingId").toString(),orignMap.get("dutyBuilding").toString()
                    ,orignMap.get("major").toString(),orignMap.get("staDimension").toString(),orignMap.get("staLongitude").toString(),list_lineMap.get(0));

            //4.2重新组装新村停车场数据--邻线的数据
            String finalOtherStaId = otherStaId;
            String infalMajor = major;
            Map finalMapTemp = OtherLineMap_temp;
            List<Map> oteherListMap_format = stationList.stream()
                    .filter(fi -> finalOtherStaId.equals(fi.get("sta_id")))
                    .map(info -> {
                        return formatDutyPointBase(info.get("line_id"),info.get("line_cname"),info.get("sta_id"),
                                info.get("sta_cname"),infalMajor, info.get("sta_dimension"),info.get("sta_longitude"), finalMapTemp);
                    }).collect(Collectors.toList());
            //5.将重新组装好的新村停车场相关数据再此加入到已原剔除“新村停车场”的数据中
            newList.add(thisLineMap);
            if(CollectionUtil.isNotEmpty(oteherListMap_format)){
                newList.add(oteherListMap_format.get(0));
            }
        }
        return newList;
    }


    /**
     * 格式化应急值守点在GIS上撒点（图层）展示的数据
     * @param lineCode 线路编码
     * @param lineName 线路名称
     * @param staId    车站编码
     * @param staCname 车站名称
     * @param major    专业
     * @param staDimension 纬度
     * @param staLongitude 经度
     * @param lineMap  每条线路的应急值守点信息
     * @return Map
     */
    private Map formatDutyPointBase(String lineCode,String lineName,String staId,String staCname,String major,String staDimension,String staLongitude,Map lineMap){
        Map map = new HashMap();
        map.put("lineCode", lineCode);
        map.put("lineName", lineName);
        map.put("dutyBuildingId", staId);
        map.put("dutyBuilding", staCname);
        map.put("major", major);
        map.put("staLongitude", staLongitude);
        map.put("staDimension", staDimension);
        List lineMapList = new ArrayList();
        lineMapList.add(lineMap);
        map.put("lineMap", lineMapList);
        return map;
    }


    /**
     * 获取应急事件信息（给GIS用）
     * serviceId：S_YJ_ZY_09
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getEmergencyEventInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
//            inInfo.set("inqu_status-0-State","30002");//事件状态:处置中
            inInfo.set("eventId",Optional.ofNullable(inInfo.get("eventId")).map(Object::toString).orElse(""));//事件ID
            EiInfo eiInfo = EiInfoUtils.callParam("YJCZ01", "queryEventDates", inInfo).build();
            if(eiInfo.getStatus() == EiConstant.STATUS_FAILURE){
                outInfo.set("data", new ArrayList<>());
                return EiInfoUtils.setError(outInfo, eiInfo.getMsg());
            }
            List<Map<String, Object>> result = eiInfo.getBlock("result").getRows();
            if (CollectionUtil.isEmpty(result)) {
                outInfo.set("data", new ArrayList<>());
                return EiInfoUtils.setError(outInfo, "query successfuly but no data return!");
            }
            //获取基础数据中的车站信息
            List<Map<String, String>> stationList = stationsFormat();
            List callbackList = new ArrayList();
            result.stream().forEach(data -> {
                List stations = new ArrayList();//存车站经、纬度信息
                Map map = new HashMap();//存应急事件基本信息
                //初始化应急时间信息
                map.put("fdUuid", data.get("fdUuid"));//应急事件ID
                map.put("fdName", data.get("fdName"));//事件名称
                map.put("fdAreaT", data.get("fdAreaT"));//发生区域，10001-车站，10002-区间
                //上报方式大类(对应GIS上的应急事件图标) 101-火灾，102-供电，103-信号，104-客流，105-设备，106-人工
                map.put("fdReportTClass", data.get("fdReportTClass"));
                map.put("fdTime", data.get("fdTime"));//事件发生时间
                if (data.get("fdAreaT") != null && Integer.parseInt(data.get("fdAreaT").toString()) == 10001
                        && data.get("fdStationNumber") != null && StringUtils.isNotBlank(data.get("fdStationNumber")
                        .toString())) {
                    //data.get("fdAreaT");//发生区域 10001-车站，10002-区间
                    //事件发生地点为：车站
                    //如果车站为多个，则在地图上显示所有车站的应急事件图标，任意车站显示应急事件名称
                    //如果车站为一个，则在地图上显示对应车站的应急事件图标，对应车站上显示应急事件名称
                    String[] fdStationNumbers = data.get("fdStationNumber").toString().split(",");
                    Arrays.stream(fdStationNumbers).forEach(eventStation -> {
                        stationList.stream()
                                .filter(filterStation -> eventStation.equals(filterStation.get("sta_id").toString()))
                                .forEach(event -> {
                                    stations.add(saveEventInfo(event));
                                });
                        map.put("stations", stations);
                    });
                    callbackList.add(map);
                } else if (data.get("fdAreaT") != null && Integer.parseInt(data.get("fdAreaT").toString()) == 10002
                        && (data.get("fdStationNumber") == null || StringUtils.isBlank(data.get("fdStationNumber").toString()))) {
                    //事件发生地点为：区间
                    //则在所有车站上显示对应应急事件图标，任意车站上显示应急事件名称
                    //开始车站编码
                    int fdStartStationCode = Integer.parseInt(data.get("fdStartStation").toString()
                            .substring(data.get("fdStartStation").toString().length() - 2));
                    //结束车站编码
                    int fdEndStationCode = Integer.parseInt(data.get("fdEndStation").toString()
                            .substring(data.get("fdEndStation").toString().length() - 2));
                    stationList.stream().filter(filterStation -> {
                        String station = filterStation.get("sta_id");//车站编码
                        //车站编码匹配状态 ，false-不匹配 ，true-匹配
                        boolean stationState = fdStartStationCode >= Integer.parseInt
                                (station.substring(station.length() - 2)) &&
                                fdEndStationCode <= Integer.parseInt(station.substring(station.length() - 2));
                        //线路编码匹配状态，false-不匹配 ，true-匹配
                        boolean lineState = filterStation.get("line_id").toString().equals(data.get("fdLine").toString());
                        return (stationState && lineState) ? true : false;
                    }).forEach(event -> {
                        stations.add(saveEventInfo(event));
                    });
                    map.put("stations", stations);
                    callbackList.add(map);
                }
            });

            outInfo.set("data", callbackList);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("GIS获取应急事件成功！");
            /////推送数据至前端 start///////
            if(inInfo.get("isPushState") == null){
                outInfo.set("wsgisType", "event");//websocket推送类型为事件
                sendWSMessageData(outInfo);
            }
            /////推送数据至前端 end ///////


            //获取到数据后，获取到第一个车站经纬度，计算出距离 start///////
            outInfo.set("distanceRangeData", Collections.EMPTY_LIST);//初始化设空
            if(CollectionUtil.isNotEmpty(callbackList)){
                EiInfo paramEiInfo = new EiInfo();
                paramEiInfo.set("eventInfolist",callbackList);
                EiInfo materialInfo = EiInfoUtils.callParam("S_YJ_ZY_17", paramEiInfo).build();
                if(materialInfo.getStatus() == EiConstant.STATUS_SUCCESS){
                    outInfo.set("distanceRangeData",materialInfo.get("distanceRangeData"));
                }
            }
            //获取到数据后，获取到第一个车站经纬度，计算出距离 end///////


        } catch (Exception exception) {
            exception.printStackTrace();
            logger.error("GIS获取应急事件出错====>{}，", exception.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("GIS获取应急事件出错====>" + exception.getMessage());
        }
        return outInfo;
    }


    /**
     * 获取应急预警（气象、地震预警）信息（给GIS用）
     * serviceId：S_YJ_ZY_10
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getEarlyWaringInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
//            EiInfo outInfo = EiInfoUtils.callParam("S_YJ_YF_01", inInfo).build();
            outInfo = EiInfoUtils.callParam("YJYF01", "queryUnreleasedData", inInfo).build();
            outInfo.set("wsgisType","warn");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("GIS获取应急事件成功！");
            /////推送数据至前端 start///////
            sendWSMessageData(outInfo);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            if (outInfo.getStatus() == EiConstant.STATUS_FAILURE){
                outInfo.setMsg("推送websocket失败，"+outInfo.getMsg());
            }
            ///推送数据至前端 end ///////
        } catch (Exception exception) {
            exception.printStackTrace();
            logger.error("GIS获取预警信息出错====>{}", exception.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("GIS获取预警信息出错====>" + exception.getMessage());
        }
        return outInfo;
    }




    /**
     * GIS搜索框关键词搜索信息方法
     * serviceId：S_YJ_ZY_11
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo searchDataByKeyword(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //关键词
            String keyword = (String) Optional.ofNullable(inInfo.get("key")).orElseGet(() -> "");
            //type分类：查询所有-（val-0）物资-material（val-1）、值守点-duty（val-2）、医院-hospital（val-3）、消防队-fire（val-4）、
            // 加油站-gas（val-5）、公交枢纽-bus（val-6）、社会机构-society（val-7）
            List<Integer> type = Optional.ofNullable(inInfo.get("type"))
                    .map(data -> {if (!(data instanceof List)) throw new PlatException("The type parameter must be an array");
                        return (List) data;
                    }).orElseGet(()->Arrays.asList(0));
            int offset = Optional.ofNullable(inInfo.get("offset"))
                    .map(data -> {if (!(data instanceof Integer)) throw new PlatException("The offset parameter must be an int");
                        return (int) data;})
                    .orElseGet(()->0);
            int limit = Optional.ofNullable(inInfo.get("limit"))
                    .map(data -> {if (!(data instanceof Integer)) throw new PlatException("The limit parameter must be an int");
                        return (int) data;})
                    .orElseGet(()->-999999);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();//开启计时

            Map paramMap = new HashMap();
            paramMap.put("keyword", keyword);
            paramMap.put("typeArray", type);
            //根据应急资源类型获取需要拼接sql的条件标识
            String conditionStr = getJoinSqlStrCondition(type);
            paramMap.put("conditionStr",conditionStr);

            List count = dao.query("YJZY01.countByKeyword", paramMap);
            if (((int) count.get(0)) == 0) {
                Map returnMap = new HashMap();
                returnMap.put("records", Collections.EMPTY_LIST);
                returnMap.put("count", 0);
                outInfo.set("data",returnMap);
                outInfo.setStatus(EiConstant.STATUS_DEFAULT);
                inInfo.setMsg("query success but no data return!");
                return outInfo;
            }

            ///////////////获取应急事件信息////////////////////
            //搜索的关键词根据事件发生地点距离排序(由近到远)
            String eventId = (String)Optional.ofNullable(inInfo.get("eventId")).orElseGet(()->"");
            Map<String,Object> emergencyDistanceMap = getEmergencyStationDistanceByEventId(eventId);
            if (MapUtils.getObject(emergencyDistanceMap, "longitude") != null
                    && MapUtils.getObject(emergencyDistanceMap, "dimension") != null) {
                paramMap.put("longitude",emergencyDistanceMap.get("longitude").toString());
                paramMap.put("dimension",emergencyDistanceMap.get("dimension").toString());
            }
            ///////////////获取应急事件信息////////////////////

            //计算offset偏移量，默认从第0页的第一条记录开始，offset设为limit的倍数
            // limit 10,10 第一个10--跳过第10页；第二个10--查询的条数限制
            //offset offset * limit
            List<Map<String, Object>> list = dao.query("YJZY01.queryDataByKeyword", paramMap, offset * limit, limit);
            Map callbackMap = new HashMap();
//            List recordsList = keywordkSerchDataFormat(list);
            stopWatch.stop();//关闭计时
            double getTotalTimeSeconds = stopWatch.getTotalTimeSeconds();//统计接口调用总耗时(单位：秒)
            callbackMap.put("records", list);
            callbackMap.put("count", count.get(0));
            callbackMap.put("offset", offset);
            callbackMap.put("limit", limit);
            outInfo.set("data", callbackMap);
            outInfo.set("searchTime", "The total time spent on this query is：【" + getTotalTimeSeconds + "】seconds！");
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("关键词搜索结果成功！");
        } catch (Exception exception) {
            exception.printStackTrace();
            logger.error("GIS搜索框信息出错====>{}", exception.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("GIS搜索框信息出错====>" + exception.getMessage());
        }
        return outInfo;
    }





    /**
     * 根据应急资源类型获取需要拼接sql的条件标识
     * @param list 应急资源类型集合
     * @return String
     */
    private String getJoinSqlStrCondition(List<Integer>list){
        String conditionStr = "allData_keywordSearch";//初始化查询所有应急资源的数据标识
        List<Integer> initBaseFlag = new ArrayList<>(Arrays.asList(3,4,5,6,7));//初始化基础资源标识
        List<Integer> initBuessinessFlag = new ArrayList<>(Arrays.asList(1,2));//初始化业务数据标识
        if(list.size()==1){
            conditionStr = JOIN_SQL_CONDITION_MAPPING.getOrDefault(initBaseFlag.contains(list.get(0)) ? 3 : list.get(0)
                    ,"allData_keywordSearch");
        }
        if(list.size()>=2){
            if(compareListsIgnoreOrder(list,initBuessinessFlag)){//参数值含应急物资、应急值守点
                conditionStr = "buinessData_keywordSearch";
            }else if(list.contains(1) && !list.contains(2)){
                //参数值含应急物资、应急资源基础数据且不含应急值守点
                conditionStr = "maritalsAndBaseData_keywordSearch";
            }else if(list.contains(2) && !list.contains(1)){
                //参数值含应急值守点、应急资源基础数据且不含应急物资
                conditionStr = "dutyPointAndBaseData_keywordSearch";
            }else if(!list.contains(1) && !list.contains(2)){
                //参数值不含应急物资、且不含应急值守点
                conditionStr = "baseData_keywordSearch";
            }
        }
        return conditionStr;
    }


    /**
     * 获取车站信息（给GIS用）
     * serviceId：S_YJ_ZY_12
     *
     * @param inInfo
     * @return EiInfo
     */
    public static EiInfo getStationInfo(EiInfo inInfo) {
        try {
            if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(YJZYConstants.REDISKEY_STAINFO_GIS)){
                List callbackList = getDataByRedis(YJZYConstants.REDISKEY_STAINFO_GIS);
                inInfo.set("data", callbackList);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                inInfo.setMsg("query successfuly！the data get by redis cache!");
                return inInfo;
            }
            EiInfo outInfo = queryStation(new EiInfo());//车站信息（含车站名称、换乘车站、车站站点面积、车站埋深、车站全景图片）
            EiInfo outInfo_sectInfo = querySectInfo(new EiInfo());//车站对应的区间信息（含车站区间图片、上、下行区间视频）
            List<Map> sectResult = outInfo_sectInfo.getBlock("result").getRows();
            List<Map> stationFormatList = new ArrayList();
            List<Map> stationResult = outInfo.getBlock("result").getRows();
            stationResult.stream().filter(fi -> "true".equals(fi.get("enable_status")))
                    .forEach(data -> {
                        List<Map> transferInfoList = Optional.ofNullable(data)
                                .map(info -> info.get("transfer_info"))
                                .map(info -> {
                                    List<Map> transferInfoMap = new ArrayList<>();
                                    if (StringUtils.isNotBlank(info.toString())) {
                                        transferInfoMap = stationResult.stream()
                                            .filter(fil -> info.toString().equals(fil.get("line_id").toString())
                                                    && data.get("sta_cname").toString().equals(fil.get("sta_cname").toString()))
                                            .map(infos -> {
                                                Map map = new HashMap();
                                                map.put("transferInfoStr", infos.get("sta_cname") + "(" + infos.get("line_cname") + ")");
                                                map.put("transferInfoMsg", data.get("line_cname") + "/"
                                                        + infos.get("line_id").toString().charAt(1)+"号线");
                                                return map;
                                            }).collect(Collectors.toList());
                                    }
                                    return transferInfoMap;
                                }).orElseGet(() -> Collections.EMPTY_LIST);
                        //格式化换乘站、格式化换乘信息
                        data.put("transferInfoStr", CollectionUtil.isNotEmpty(transferInfoList) ? transferInfoList.get(0).get("transferInfoStr") : "");
                        data.put("transferInfoMsg", CollectionUtil.isNotEmpty(transferInfoList) ? transferInfoList.get(0).get("transferInfoMsg") : "");

                //获取区间数据中的区间图片、上行、下行区间视频信息
                List<Map> sectList = null;
                try {
                    sectList = formatSectList(sectResult, data);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
                data.put("intervalImg", sectList.get(0).get("intervalImg"));//区间图片
                data.put("upIntervalVideo",  sectList.get(0).get("upIntervalVideo"));//上行区间视频
                data.put("downIntervalVideo",  sectList.get(0).get("downIntervalVideo"));//下行区间视频
                stationFormatList.add(data);
            });
            List<Map> callbackList = distStationListByStaCname(stationFormatList);
            inInfo.set("data", callbackList);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("query successfuly ！");
            //将数据存入redis
            if(isRedisAvailable(redisHost,redisPort) && CollectionUtil.isNotEmpty(callbackList)){
                redisTemplate.opsForValue().set(YJZYConstants.REDISKEY_STAINFO_GIS, JSON.toJSONString(callbackList));
                redisTemplate.expire(YJZYConstants.REDISKEY_STAINFO_GIS, 15, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("GIS获取车站基础数据出错{}", e.getMessage());
            inInfo.set("data", Collections.EMPTY_LIST);
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("GIS获取车站基础数据出错，" + e.getMessage());
        }
        return inInfo;
    }




    /**
     * 获取不同场景信息（给GIS用）
     * serviceId：S_YJ_ZY_13
     *
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getSceneInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //mock data start
//            ZYMOCK.getInstance().formatDataOfSceneDP_MockData();
            //mock data end
            String sceneType = (String)Optional.ofNullable(inInfo.get("sceneType"))
                    .orElseThrow(() -> new PlatException("[sceneType]字段不能为空!请传入场景类型!"));
            if("DP".equals(sceneType)){//大屏应急模式
                Map getDataByRedisForDP = getDataForSceneDPYJ(); //获取大屏模式下GIS需展示的数据要素
                outInfo.set("data", getDataByRedisForDP);
            } else if("DP_FX".equals(sceneType)){//大屏防汛模式
                //物资与车站以及车站级天气数据合并
                List<Map> resultList = formatDataForSceneDPFX();
                outInfo.set("data",resultList);
                outInfo.set("layerArr",Arrays.asList("material"));//需在GIS上展示的图层数组
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            outInfo.setMsg("query successfuly！");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("GIS获取场景信息数据出错，{}", e.getMessage());
            outInfo.set("data", new HashMap());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("GIS获取场景信息础数据出错，" + e.getMessage());
        }
        return outInfo;
    }





    /**
     * 获取大屏模式下GIS需展示的数据要素
     * @return Map
     * @throws PlatException
     */
    private static Map getDataForSceneDPYJ() throws PlatException {
        EiInfo info = new EiInfo();
        info.set(EiConstant.serviceId,"S_YJ_CZ_31");
        EiInfo outInfo = XServiceManager.call(info);
        return outInfo.getAttr();
    }


    /**
     * 根据天气预报信息获取天气图标字符串
     * @param weatherResult 天气预报结果集
     * @param currentWeather 当前天气信息
     * @return String
     * 天气图标获取逻辑汇总：
     * 1、当前时间> 08:00 < 20:00
     *     当日20:00
     * 2、当前时间 > 08:00 > 20：00
     *     后一天的08:00
     * 3、当前时间 < 08:00 < 20:00
     *     前一天 20：00
     */
    private String getWeatherIconStr(List<Map<String, Object>> weatherResult,Map<String, Object>currentWeather) throws PlatException{
        LocalDate currentDate = LocalDate.now();//当天
        return weatherResult.stream()
                //第一层过滤：同一行政区的
                .filter(firstFilter -> currentWeather.get("one_name").toString()
                        .equals(firstFilter.get("one_name")))
                .filter(secondFilter -> {
                    //第二次过滤：判断当前时间是否在符合规定的时间区间内
                    boolean filterState = false;
                    LocalTime currentTime = LocalTime.now();//实例化当前时间
                    LocalTime twentyHours = LocalTime.of(20, 0);//实例化20点的时间
                    LocalTime eightHours = LocalTime.of(8, 0);//实例化08点时间
                    if (currentTime.isAfter(twentyHours)) {//如果当前时间大于20点，则取出后一天的08：00的数据
                        LocalDate nextDate = currentDate.plusDays(1);//当前日期后一天
                        filterState = formatDateTime(nextDate, eightHours).equals(secondFilter.get("time")) ? true : false;
                    } else {//如果当前时间小于20点
                        if (currentTime.isBefore(eightHours)) {
                            //1、跨天：当前时间是凌晨（小于20点且小于08点）,数据取：前一天20：00（上一天的20：:00）
                            LocalDate preDate = currentDate.minusDays(1);//当前日期前一天
                            filterState = formatDateTime(preDate, twentyHours).equals(secondFilter.get("time")) ? true : false;
                        } else {
                            //2、同天：当前时间小于20点且大于08：00，数据取：当天20：00
                            filterState = formatDateTime(currentDate, twentyHours).equals(secondFilter.get("time")) ? true : false;
                        }
                    }
                    return filterState;
                })
                .map(weatherInfo -> weatherInfo.get("weather").toString())
                .collect(Collectors.joining());
    }



    /**
     * 格式化区间图片、上行区间视频、下行区间视频信息
     *
     * @param sectResult 区间基础数据集合
     * @param data       车站基础数据Map
     * @return List<Map>
     * @throws Exception
     */
    private static List<Map> formatSectList(List<Map> sectResult, Map data) throws RuntimeException {
        Map backMap = new HashMap();
        String upIntervalVideo = "", downIntervalVideo = "";//上行视频、下行视频
        List<Map> callbakList = new ArrayList<>();
        List<Map> sectInfoList = sectResult.stream()
                .filter(fi -> data.get("sta_id").toString().equals(fi.get("start_sta_id").toString())
                        && data.get("line_id").toString().equals(fi.get("line_id").toString()))
                .map(sectInfo -> {
                    Map sectInfoMap = new HashMap();
                    sectInfoMap.put("section_picture", Optional.ofNullable(sectInfo.get("section_picture")).orElseGet(() -> ""));
                    sectInfoMap.put("section_video", Optional.ofNullable(sectInfo.get("section_video")).orElseGet(() -> ""));
                    /////TEST/////
//                    sectInfoMap.put("section_picture",testIntervalImg(sectInfo.get("line_id").toString()));
                    String thisLine = INTERVAL_IMG_MAPPING.getOrDefault(sectInfo.get("line_id").toString(), "");
                    String [] interImgArray = new String[0];
                    if(StringUtils.isNotBlank(data.get("transfer_info").toString())){
                        String transfer = INTERVAL_IMG_MAPPING.getOrDefault(data.get("transfer_info").toString(), "");
                        interImgArray = new String[]{thisLine, transfer};
                    }else{
                        interImgArray = new String[]{thisLine};
                    }
                    sectInfoMap.put("section_picture",interImgArray);
                    /////TEST/////
                    return sectInfoMap;
                }).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(sectInfoList)){
                backMap.put("intervalImg", new String[0]);
                backMap.put("upIntervalVideo","" );
                backMap.put("downIntervalVideo", "");
                callbakList.add(backMap);
                return callbakList;
            }

        backMap.put("intervalImg", sectInfoList.get(0).get("section_picture"));//区间图片

        //todo 区间视频暂未开发
//        backMap.put("upIntervalVideo", upIntervalVideo);//上行区间视频
//        backMap.put("downIntervalVideo", downIntervalVideo);//下行区间视频
        backMap.put("upIntervalVideo", "./station/st1.mp4");//上行区间视频
        backMap.put("downIntervalVideo", "./station/st2.mp4");//下行区间视频
        callbakList.add(backMap);
        return callbakList;
    }





    /**
     * 通过websocket推送数据
     *
     * @param inInfo
     * @throws RuntimeException
     */
    private boolean sendWSMessageData(EiInfo inInfo) throws RuntimeException {
        //event-应急事件；warn-预警
        String wsgisType = (String) Optional.ofNullable(inInfo.get("wsgisType")).orElseGet(() -> "");
        List<Map> list = (List) inInfo.get("data");
        if (StringUtils.isBlank(wsgisType) || CollectionUtil.isEmpty(list)) return true;
        if (!"event".equals(wsgisType) && !"warn".equals(wsgisType)) return true;
        Map map = new HashMap();
        map.put("wsgisType", wsgisType);
        list.add(map);
        inInfo.set("topic", "appwsgis");
        inInfo.set("data", list);
//        EiInfo eiInfo = EiInfoUtils.callParam("S_APP_RT_MQ_92", inInfo).build();
        //开启新的事务
        inInfo.set(EiConstant.serviceName, "RTMQ02");
        inInfo.set(EiConstant.methodName, "sendWSMsg");
        EiInfo eiInfo = XLocalManager.callNewTx(inInfo);
        eiInfo.get("data");
        return true;
    }


    /**
     * 保存应急事件基本信息
     *
     * @param event
     * @return
     */
    private Map saveEventInfo(Map event) {
        Map stationMap = new HashMap();
        stationMap.put("sta_id", event.get("sta_id"));//车站编码
        stationMap.put("sta_cname", event.get("sta_cname"));//车站名称
        stationMap.put("sta_cname", event.get("sta_cname"));//车站名称
        stationMap.put("line_id", event.get("line_id"));//线路编码
        stationMap.put("sta_longitude", event.get("sta_longitude"));//经度
        stationMap.put("sta_dimension", event.get("sta_dimension"));//纬度
        return stationMap;
    }


    /**
     * 获取风级中文名
     *
     * @param windVelocity    风速编号
     * @param windSpLevelList 风级对照关系集合
     * @return String
     */
    private String getWindLevelStr(String windVelocity, List<Map<String, Object>> windSpLevelList) {
        return windSpLevelList.stream().filter(windLevel -> {
            double windLevelCode = Double.parseDouble(windVelocity);
            double windMin = Double.parseDouble(windLevel.get("windMin").toString());//风速区间最小值
            double windMax = Double.parseDouble(windLevel.get("windMax").toString());//风速区间最大值
            return (windLevelCode >= windMin && windLevelCode <= windMax) ? true : false;
        }).map(wind -> wind.get("windLevel").toString()).collect(Collectors.joining(","));
    }


    /**
     * 获取天气现象描述中文名
     *
     * @param weather         天气现象编号
     * @param weatherDescList 天气现象编号对照关系集合
     * @return String
     */
    private String getWeatherDescStr(String weather, List<Map<String, Object>> weatherDescList) {
        return weatherDescList.stream().filter(weatherInfo -> weather.equals(weatherInfo.get("weatherDesc").toString()))
                .map(weatherData -> weatherData.get("windLevel").toString()).collect(Collectors.joining(","));
    }


    /**
     * 根据应急事件ID查询事件发生地点的经、纬度
     * @param eventId  事件ID
     * @return
     */
    private Map<String,Object> getEmergencyStationDistanceByEventId(String eventId) throws RuntimeException{
        Map map = new HashMap();
        Optional.ofNullable(eventId)
                .filter(StringUtils::isNotBlank)
                .map(k -> {
                    EiInfo eiInfo = new EiInfo();
                    eiInfo.set("isPushState", false);
                    eiInfo.set("eventId",eventId);//根据事件ID过滤
                    EiInfo emergencyEventEiInfo = getEmergencyEventInfo(eiInfo);
                    return (List<Map<String, Object>>) emergencyEventEiInfo.get("data");
                })
                .filter(CollectionUtil::isNotEmpty)
                .map(emergencyEventList -> (List<Map>) emergencyEventList.get(0).get("stations"))
                .filter(CollectionUtil::isNotEmpty)
                .map(emergencyStationList -> emergencyStationList.get(0))
                .ifPresent(emergencyStation -> {
                    map.put("longitude",emergencyStation.get("sta_longitude").toString());//经度
                    map.put("dimension",emergencyStation.get("sta_dimension").toString());//纬度
                });
        return map;
    }


    /**
     * 根据查询信息组装数据
     * @param list 应急资源信息
     * @return List
     */
    private List<Map<String, Object>> keywordkSerchDataFormat(List<Map<String, Object>> list){
        list = list.stream()
                .sorted(Comparator.comparing(map -> (Integer) map.get("type")))
                .collect(Collectors.toList());
        List callList = new ArrayList<>();
        //获取基础数据中的车站信息
        List<Map<String, String>> stationList = stationsFormat();
        list.stream().forEach(data -> {
            Map tempMap = new HashMap();//车站去重-临时
            int poiType = Integer.parseInt(data.get("type").toString());
            if (poiType == 1 || poiType == 2) {//类型为物资或者值守点时，则需要手动增加车站的经、纬度信息
                stationList.stream().filter(staMatch -> data.get("name").equals(staMatch.get("sta_cname")))
                        .forEach(staData -> {
                            if (tempMap.isEmpty()) {
                                tempMap.put("temp", staData);
                                data.put("lon", staData.get("sta_longitude"));//经度
                                data.put("lat", staData.get("sta_dimension"));//纬度
                                callList.add(data);
                            }
                        });
            } else {
                callList.add(data);
            }
        });
        return callList;
    }


    /**
     * 校验输入的关键词是否是车站
     * @param keyword 关键词
     * @return true，则关键词不作为物资名称搜索条件，false-则关键词作为物资名称搜素条件
     *
     */
    private boolean keywordIsStation(String keyword){
        List<Map<String, String>> baseStationsInfo = stationsFormat();
        if(StringUtils.isBlank(keyword))return false;
        return baseStationsInfo.stream().anyMatch(fi->{
               String subStr = fi.get("sta_cname").indexOf("站") > 0 ? fi.get("sta_cname")
                    .substring(0, fi.get("sta_cname").length() - 1) : fi.get("sta_cname");
               return keyword.equals(subStr);
        });
    }




}


