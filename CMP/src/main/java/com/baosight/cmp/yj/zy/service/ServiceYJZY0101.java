package com.baosight.cmp.yj.zy.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.cmp.common.util.eiinfo.EiInfoUtils;
import com.baosight.cmp.yj.zy.common.constants.YJZYConstants;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import redis.clients.jedis.Jedis;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.baosight.cmp.yj.zy.service.ServiceYJZY01.getMaterialsFromRedis;
import static com.baosight.cmp.yj.zy.service.ServiceYJZY01.getStationInfo;
import static com.baosight.cmp.yj.zy.common.ZYUtil.getDataByRedis;
import static com.baosight.cmp.yj.zy.common.ZYUtil.isRedisAvailable;

/**
 * @author: yanghuanbo
 * @date: 2024/08/20 15:13:11
 * @description 应急资源(GIS)后台服务逻辑
 *
 * 主要方法：
 * 1. 从【气象灾害监测服务保障系统】[地铁线路天气实况]接口中获取”地铁线路天气实况“数据
 * 2.根据应急事件获取应急事件发生地点3KM与5KM范围内的应急物资信息
 */
public class ServiceYJZY0101 {

    private static Logger logger = LoggerFactory.getLogger(ServiceYJZY0101.class);
    private static Dao dao = (Dao) PlatApplicationContext.getApplicationContext().getBean("dao");
    //引用 RedisTemplate
    private static RedisTemplate<String, Object> redisTemplate = (RedisTemplate) PlatApplicationContext.getApplicationContext().getBean("redisTemplate");
    //读取redis的IP配置
    private static String redisHost = PlatApplicationContext.getProperty("spring.redis.host");
    private static int redisPort = Integer.parseInt(PlatApplicationContext.getProperty("spring.redis.port"));
//    public static String redisPassword = PlatApplicationContext.getProperty("spring.redis.password");


    /**
     * 从【气象灾害监测服务保障系统】[地铁线路天气实况]接口中获取”地铁线路天气实况“数据
     * serviceId：S_YJ_ZY_14
     * dataRefreshRate:数据刷新频率  5分钟
     * @param inInfo
     * @return EiInfo
     */
    public EiInfo getStationWeatherOfMeteorDisaster(EiInfo inInfo) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();//开启计时
            //发送GET请求至【气象灾害监测服务保障系统】[地铁线路天气实况]接口
//            EiInfo eiInfo = sendGetForMeteorDisaster();
            //从redis中获取【气象灾害监测服务保障系统】[地铁线路天气实况]数据
            EiInfo eiInfo = getMeteorDisasterFromRedis();
            if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE) return EiInfoUtils.setError(inInfo, eiInfo.getMsg());
            JSONObject responseObject = (JSONObject) eiInfo.get("responseObject");
            JSONObject dataObject = responseObject.getJSONObject("data");
            //格式化[地铁线路天气实况]接口返回数据(在原接口数据基础上增加经、纬度)
            List dataList = formatDataForMeteorDisaster(dataObject.getJSONArray("list"));
            Map callbackMap = new HashMap();
            callbackMap.put("dataTime",  dataObject.getStr("dataTime"));//数据更新时间
            callbackMap.put("list", dataList);
            stopWatch.stop();
            inInfo.set("data", callbackMap);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("query successfuly！The total time spent on this query is：[" + stopWatch.getTotalTimeSeconds() + "] seconds！");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("GIS获取【气象灾害监测服务保障系统】[地铁线路天气实况]接口数据出错,{}", e.getMessage());
            inInfo.set("data", new HashMap());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("GIS获取【气象灾害监测服务保障系统】[地铁线路天气实况]接口数据出错，" + e.getMessage());
        }
        return inInfo;
    }


    /**
     * 发送GET请求至【气象灾害监测服务保障系统】[地铁线路天气实况]接口
     *
     * @return EiInfo
     * @throws PlatException
     */
    private static EiInfo sendGetForMeteorDisaster() throws PlatException {
        EiInfo inInfo = new EiInfo();
        String jsonStringResponse = HttpUtil.get(YJZYConstants.METEOR_DISASTER_URL);

//        if(isRedisAvailable(redisHost,redisPort) && StringUtils.isNotBlank(jsonStringResponse)){
//            redisTemplate.opsForValue().set(STA_WEATHER_DATA_REDIS_KEY, jsonStringResponse);
//            redisTemplate.expire(STA_WEATHER_DATA_REDIS_KEY, 15, TimeUnit.DAYS);
//        }

        if (StringUtils.isBlank(jsonStringResponse)) {
            return EiInfoUtils.setError(inInfo, "获取【气象灾害监测服务保障系统】[地铁线路天气实况]接口数据，接口返回数据为空！");
        }
        JSONObject jsonObject = new JSONObject(jsonStringResponse);
        if (jsonObject.getInt("status") != 0 || !jsonObject.getBool("success")) {
            inInfo.set("responseObject", new HashMap());
            return EiInfoUtils.setError(inInfo, jsonObject.getStr("message"));
        }
        inInfo.set("responseObject", jsonObject);
        return inInfo;
    }


    /**
     * 从redis中获取【气象灾害监测服务保障系统】[地铁线路天气实况]数据
     * @return EiInfo
     * @throws PlatException
     */
    private static EiInfo getMeteorDisasterFromRedis() throws PlatException {
        EiInfo inInfo = new EiInfo();
        Jedis jedis = new Jedis(redisHost,redisPort);
        if(!isRedisAvailable(redisHost,redisPort) || !jedis.exists(YJZYConstants.STA_WEATHER_DATA_REDISKEY)){
            inInfo.set("data", new HashMap());
            return EiInfoUtils.setError("get subway station weather data failed;redis connection error Or redis no [STA_QX_WEATHER_DATA] key !");
        }
        Object objData = jedis.get(YJZYConstants.STA_WEATHER_DATA_REDISKEY);
        JSONObject jsonObject = new JSONObject(objData);
        if (jsonObject.getInt("status") != 0 || !jsonObject.getBool("success")) {
            inInfo.set("responseObject", new HashMap());
            return EiInfoUtils.setError(inInfo, jsonObject.getStr("message"));
        }
        inInfo.set("responseObject", jsonObject);
        return inInfo;
    }


    /**
     * 格式化【气象灾害监测服务保障系统】[地铁线路天气实况]接口返回数据
     * @param dataListJsonArray json数组
     * @return List
     * @throws PlatException
     */
    private List formatDataForMeteorDisaster(JSONArray dataListJsonArray) throws PlatException {
        //获取NOCC车站基础数据
        List<Map<String, String>> stationsInfoList = getStaInfoForMeteorDisaster();
        if (CollectionUtil.isEmpty(stationsInfoList)) return Collections.emptyList();
        List callbackList = new ArrayList();
        //根据线路名称与站点名称获取对应车站的经纬度
        dataListJsonArray.stream().forEach(data -> {
            Map<String, Object> dataMap = (Map<String, Object>) data;
            //1.接口数据中如是换乘站会加上【线路名称】前缀，需要特殊处理
            //2.接口数据中【5号线】的[5号线那洪车辆基地] 站点名称与NOCC基础数据不一致【5号线】的[那洪车辆段]，需特殊处理
            String orginStaName = dataMap.get("stationName").toString();//保存接口原车站名称
            //临时更新接口中的车站名称内容--为了匹配NOCC基础数据即上面提到的第二点
            dataMap.put("stationName", "5号线那洪车辆基地".equals(orginStaName) ? "那洪车辆段" : orginStaName);
            stationsInfoList.stream().filter(fil -> dataMap.get("lineName").toString().equals(fil.get("line_cname").toString())
                            && (dataMap.get("stationName").toString().equals(fil.get("sta_cname").toString())
                            || fil.get("sta_cname").toString().contains(dataMap.get("stationName").toString())
                            || dataMap.get("stationName").toString().contains(fil.get("sta_cname").toString())))
                    .forEach(staInfo -> {
                        Map staMap = (Map) staInfo;
                        staMap.put("datetime", dataMap.get("datetime"));//日期时间
                        staMap.put("pre1h", dataMap.get("pre1h"));//1小时降水量
                        staMap.put("tem1h", dataMap.get("tem1h"));//1小时平均气温
                        staMap.put("windSpeed1h", Optional.ofNullable(dataMap.get("windSpeed1h")).map(Object::toString).orElse(""));//1小时风速
                        staMap.put("windDirect1h", Optional.ofNullable(dataMap.get("windDirect1h")).map(Object::toString).orElse(""));//1小时风向
                        staMap.put("stationName", orginStaName);//返回接口原字段名称
                        callbackList.add(staMap);
                    });
        });
        return callbackList;
    }

    /**
     * 获取NOCC车站基础数据【气象灾害监测服务保障系统】[地铁线路天气实况]
     * @return List<Map<String, String>>
     * @throws PlatException
     */
    private List<Map<String, String>> getStaInfoForMeteorDisaster() throws PlatException {
        if (isRedisAvailable(redisHost, redisPort) && redisTemplate.hasKey(YJZYConstants.REDISKEY_STAINFO_GIS)) {
            return getDataByRedis(YJZYConstants.REDISKEY_STAINFO_GIS);
        }
        EiInfo eiInfo = getStationInfo(new EiInfo());
        if (eiInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            logger.error("GIS获取【气象灾害监测服务保障系统】[地铁线路天气实况]接口转换NOCC车站基础数据出错,{}", eiInfo.getMsg());
            return Collections.emptyList();
        }
        return (List<Map<String, String>>) eiInfo.get("data");
    }


    /**
     *  格式化大屏防汛模式数据组装
     *  数据含：①物资数据、②车站天气数据、③车站基础数据
     * @return List<Map>
     * @throws PlatException
     */
    public static List<Map> formatDataForSceneDPFX() throws PlatException {
        EiInfo staWeatherRes = EiInfoUtils.call("S_YJ_ZY_14").build();//获取车站天气数据
        List<Map> materialList = getMaterialDataForSceneDPFX();//获取大屏防汛模式物资数据
        List<Map> staWeatherList = Optional.ofNullable(staWeatherRes.get("data")).map(data -> {
            Map<String,Object> resMap = (Map) staWeatherRes.get("data");
            return (List)resMap.get("list");
        }).orElse(Collections.emptyList());
        materialList.stream().forEach(material -> {
            staWeatherList.stream()
                    .filter(staWeather -> material.get("sta_id").toString().equals(staWeather.get("sta_id").toString()))
                    .forEach(staWeatherMap -> {
                        material.put("pre1h", staWeatherMap.get("pre1h"));//1小时降水量
                        material.put("tem1h", staWeatherMap.get("tem1h"));//1小时平均气温
                        material.put("windSpeed1h", staWeatherMap.get("windSpeed1h"));//1小时风速
                        material.put("windDirect1h", staWeatherMap.get("windDirect1h"));//1小时风向
                        material.put("sta_dimension", staWeatherMap.get("sta_dimension"));//纬度
                        material.put("sta_longitude", staWeatherMap.get("sta_longitude"));//经度
                    });
        });
        return materialList;
    }


    /**
     * 获取大屏防汛模式物资数据
     * @return List<Map>
     * @throws PlatException
     */
    private static List<Map> getMaterialDataForSceneDPFX() throws PlatException {
        boolean isRedisAvailable = isRedisAvailable(redisHost, redisPort);//判断redis是否可用,true-可用，false-不可用
        if(isRedisAvailable && redisTemplate.hasKey(YJZYConstants.REDISKEY_FORMAT_MATERIAL_GIS)){
            Object objData = redisTemplate.opsForValue().get(YJZYConstants.REDISKEY_FORMAT_MATERIAL_GIS);
            return Optional.ofNullable(objData).map(data -> {
                List<Map> dataList = JSONUtil.toList(JSONUtil.parseArray(objData.toString()), Map.class);
                return dataList;
            }).orElse(Collections.emptyList());
        }else{
            EiInfo materialRes = EiInfoUtils.call("S_YJ_ZY_06").build();
            if( isRedisAvailable && materialRes.get("data") !=null){
                redisTemplate.opsForValue().set(YJZYConstants.REDISKEY_FORMAT_MATERIAL_GIS, JSON.toJSONString((List)materialRes.get("data")));
                redisTemplate.expire(YJZYConstants.REDISKEY_FORMAT_MATERIAL_GIS, 6, TimeUnit.HOURS);
            }
            return (List)materialRes.get("data");
        }
    }



    /**
     * 根据应急事件获取应急事件发生地点3KM与5KM范围内的应急物资信息
     * serviceId：S_YJ_ZY_17
     * @param inInfo 输入的 EiInfo 对象，包含应急事件信息
     * @return 包含3KM与5KM范围内应急物资信息的 EiInfo 对象
     */
    public EiInfo getMaterialInfoOfRange3To5KM(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            List eventInfolist = (List) inInfo.get("eventInfolist");
            boolean redisAvailable = isRedisAvailable(redisHost, redisPort);
            Optional.ofNullable(eventInfolist)
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0))// 获取列表中的第一个元素
                    .map(obj -> (Map<String, Object>) obj) // 显式类型转换为 Map<String, Object>
                    .map(staInfoMap -> (List<Map<String, Object>>) staInfoMap.get("stations"))
                    .filter(stations -> stations != null && !stations.isEmpty())
                    // 获取车站信息列表中的第一个元素
                    .map(stations -> stations.get(0))
                    .ifPresent(station -> {
                        Double longitude = Convert.toDouble(station.get("sta_longitude"));//经度
                        Double latitude = Convert.toDouble(station.get("sta_dimension"));//纬度
                        Map<Object, Object> params = MapUtil.builder().put("longitude", longitude).put("lat", latitude).build();
                        // 根据经纬度查询距事件发生地点3KM与5KM范围内的车站信息
                        List<Map<String, Object>> distanceRangeData = dao.query("YJZY01.queryCalDistanceByLatAndLong", params);
                        if (CollectionUtil.isNotEmpty(distanceRangeData)){
                            // 筛选出距离小于等于3KM的车站ID列表
                            List<String> lessEqual3KMStaIds = distanceRangeData.stream()
                                    .filter(fil -> "Y".equals(fil.get("lessEqual3KM")))
                                    .map(data -> data.get("staId").toString())
                                    .collect(Collectors.toList());
                            // 筛选出距离大于3KM的车站ID列表
                            List<String> moreThan3KMStaIds = distanceRangeData.stream()
                                    .filter(fil -> "N".equals(fil.get("lessEqual3KM")))
                                    .map(data -> data.get("staId").toString())
                                    .collect(Collectors.toList());

                            ///////////优先从redis中获取物资的基本信息，若redis中没有则在数据库中获取///////////////
                            //1. 根据小于等于3KM的车站ID列表查询物资基础信息
                            List<Map> lessOfMaterialList = new ArrayList<>();
                            if(CollectionUtil.isNotEmpty(lessEqual3KMStaIds)){
                                Map lessParam = MapUtil.builder().put("positionIdsItem", lessEqual3KMStaIds).build();
                                if(redisAvailable && redisTemplate.hasKey(YJZYConstants.REDISKEY_MATERIAL_INFO_GIS)){
                                    List<Map<String, String>> materialsFromRedis = getMaterialsFromRedis(YJZYConstants.REDISKEY_MATERIAL_INFO_GIS, lessParam);
                                    lessOfMaterialList = ZYUtil.convertListMap(materialsFromRedis);
                                }else{
                                    lessOfMaterialList = dao.query("YJZY01.queryGoodsMarterials", lessParam, 0, -999999);
                                }
                            }

                            // 为小于3KM的物资列表追加 distanceKM 字段
                            for (Map<String, Object> material : lessOfMaterialList) {
                                String position = (String) material.get("position");
                                for (Map<String, Object> distanceData : distanceRangeData) {
                                    String staCname = (String) distanceData.get("staCname");
                                    if (position.equals(staCname)) {
                                        material.put("distanceKM", distanceData.get("distanceKM"));
                                        break;
                                    }
                                }
                            }

                            //2. 根据小于等于5KM的车站ID列表查询物资基础信息
                            List<Map> moreOfMaterialList = new ArrayList<>();
                            if(CollectionUtil.isNotEmpty(moreThan3KMStaIds)){
                                Map<Object, Object> moreParam = MapUtil.builder().put("positionIdsItem", moreThan3KMStaIds).build();
                                if(redisAvailable && redisTemplate.hasKey(YJZYConstants.REDISKEY_MATERIAL_INFO_GIS)){
                                    List<Map<String, String>> materialsFromRedis = getMaterialsFromRedis(YJZYConstants.REDISKEY_MATERIAL_INFO_GIS, moreParam);
                                    moreOfMaterialList = ZYUtil.convertListMap(materialsFromRedis);
                                }else{
                                    moreOfMaterialList = dao.query("YJZY01.queryGoodsMarterials", moreParam, 0, -999999);
                                }
                            }

                            // 为小于5KM的物资列表追加 distanceKM 字段
                            for (Map<String, Object> material : moreOfMaterialList) {
                                String position = (String) material.get("position");
                                for (Map<String, Object> distanceData : distanceRangeData) {
                                    String staCname = (String) distanceData.get("staCname");
                                    if (position.equals(staCname)) {
                                        material.put("distanceKM", distanceData.get("distanceKM"));
                                        break;
                                    }
                                }
                            }

                            /////////////////////数据组装-距离从近到远排序(与distanceRangeData返回数据一致)///////////////////////////
                            //组装 3KM 范围内的物资信息
                            List<Map> lessEqual3KMMaterialList = assembleMaterialList(distanceRangeData, lessOfMaterialList, true);
                            //组装 5KM 范围内的物资信息
                            List<Map> moreEqual3KMMaterialList = assembleMaterialList(distanceRangeData, moreOfMaterialList, false);

                            // 构建包含3KM和5KM范围内物资信息的 Map
                            Map<Object, Object> rangeMaterialInfo = MapUtil.builder().put("lessEqual3KM", lessEqual3KMMaterialList)
                                    .put("moreEqual3KM", moreEqual3KMMaterialList).build();
                            // 构建包含物资信息和事件发生地点的 Map,返回结果
                            Map<Object, Object> rangeMaterialMap = MapUtil.builder().put("rangeMaterialMap", rangeMaterialInfo)
                                    .put("eventPosition", distanceRangeData.get(0).get("staCname"))
                                    .put("longitude", longitude)
                                    .put("latitude", latitude)
                                    .build();
                            List rangeMaterialList = new ArrayList<>();
                            rangeMaterialList.add(rangeMaterialMap);
                            outInfo.set("distanceRangeData", rangeMaterialList);
                            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                        }
                    });
            // 如果输出的 EiInfo 对象的状态为成功，则保持成功状态，否则设置为默认状态
            outInfo.setStatus(outInfo.getStatus() == EiConstant.STATUS_SUCCESS ? EiConstant.STATUS_SUCCESS : EiConstant.STATUS_DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            logger.error("获取应急事件获取应急事件发生地点3KM与5KM范围内的应急物资信息错误，{}", e.getMessage());
        }
        return outInfo;
    }



    /**
     * 用于组装指定范围内的物资信息
     * 该方法会根据传入的距离范围数据筛选出符合条件的车站信息，
     * 然后将物资列表中与这些车站信息匹配的物资信息组装到一个新的列表中返回。
     * @param distanceRangeData 距离范围数据，包含车站的距离信息，如是否在3KM范围内
     * @param materialList 物资列表，包含各种物资的基础信息
     * @param is3KMRange 是否是3KM范围，true 表示筛选3KM以内的车站对应的物资信息，false 表示筛选3KM以外（5KM范围）的车站对应的物资信息
     * @return 组装后的物资列表，按照筛选出的车站信息顺序排列
     */
    private List<Map> assembleMaterialList(List<Map<String, Object>> distanceRangeData, List<Map> materialList, boolean is3KMRange) {
        // 用于存储最终组装好的物资信息列表
        List<Map> resultList = new ArrayList<>();
        // 用于存储筛选出的符合距离条件的车站信息列表
        List<Map<String, Object>> filteredDistanceData = new ArrayList<>();
        // 筛选出符合条件的数据
        // 遍历距离范围数据，根据 is3KMRange 参数判断是否筛选3KM以内的车站信息
        for (Map<String, Object> distanceData : distanceRangeData) {
            // 判断当前车站是否符合距离条件，Y - 3KM ；N - 5KM
            boolean matchCondition = is3KMRange ? "Y".equals(distanceData.get("lessEqual3KM")) : "N".equals(distanceData.get("lessEqual3KM"));
            if (matchCondition) {
                filteredDistanceData.add(distanceData);
            }
        }
        for (Map<String, Object> distanceData : filteredDistanceData) {
            String staId = distanceData.get("staId").toString();//车站的ID
            String transferInfo = (String) distanceData.get("transferInfo");//换乘信息-换乘线路编码
            String staCname = (String) distanceData.get("staCname");//获取当前车站的名称
            // 遍历物资列表，查找与当前车站匹配的物资信息
            for (Map material : materialList) {
                String positionId = material.get("positionId").toString();//车站ID
                String lineCode = (String) material.get("lineCode");//线路编码
                String position = (String) material.get("position");//车站名称
                // 判断物资是否与当前车站匹配
                if (staId.equals(positionId)) {// 如果位置ID相等，则认为匹配，将该物资信息添加到结果列表中
                    resultList.add(material);
                } else if (transferInfo != null && !transferInfo.isEmpty()
                        && transferInfo.equals(lineCode) && staCname.equals(position)) {
                    // 如果换乘信息不为空，且线路代码和位置名称都匹配，则认为匹配，将该物资信息添加到结果列表中
                    resultList.add(material);
                }
            }
        }
        return resultList;
    }








}
