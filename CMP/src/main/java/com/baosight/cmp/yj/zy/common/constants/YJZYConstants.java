package com.baosight.cmp.yj.zy.common.constants;

/**
 * @description 应急资源模块常量类
 * <AUTHOR>
 * @date 2024/09/24
 */
public class YJZYConstants {

    /**
     * redis中的车站基础信息
     */
    public static final String REDISKEY_STAINFO_GIS = "gis:stationInfo";


    /**
     * redis中天气实况
     */
    public static final String REDISKEY_WEATHER_CURRENT_GIS = "currentWeather";


    /**
     * redis中天气预报
     */
    public static final String REDISKEY_WEATHER_FORECAST_GIS = "weatherForecast";



    /**
     * redis中的应急物资信息
     * 此redis中的key存储的value为格式化前的应急物资数据
     */
    public static final String REDISKEY_MATERIAL_INFO_GIS = "gis:materialInfo";


    /**
     * redis中的应急值守点信息
     * 此redis中的key存储的value为格式化前的应急值守点数据
     */
    public static final String REDISKEY_DUTYPOINT_INFO_GIS = "gis:dutyPointInfo";


    /**
     * redis中存储的【气象灾害监测服务保障系统】[地铁线路天气实况]接口数据
     */
    public static String STA_WEATHER_DATA_REDISKEY = "STA_QX_WEATHER_DATA";


    /**
     * 【气象灾害监测服务保障系统】[地铁线路天气实况]接口URL
     */
    public static String METEOR_DISASTER_URL = "https://zyqx.nnweather.com/api/railTransitDataInterface/getLineWeatherRealData?apiKey=ae18cc425fa4f9b807575caf002526b3";


    /**
     * redis中的大屏防汛模式应急物资信息
     * 此redis中的key存储的value为格式化后的应急物资数据
     */
    public static final String REDISKEY_FORMAT_MATERIAL_GIS = "gis:format:material";




}
