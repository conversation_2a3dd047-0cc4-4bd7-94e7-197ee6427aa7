<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="YJZY01">

	<sql id="sql_query_GoodsMarterials">
		<isNotEmpty prepend="and" property="goodsId">
			goods_id = #goodsId#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="uuid">
			goods_id = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="lineCode">
			line_code = #lineCode#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="lineName">
			line_name like '%$lineName$%'
		</isNotEmpty>
		<isNotEmpty prepend="and" property="positionId">
			position_id = #positionId#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="position">
			position like '%$position$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="positionItem">
			position  in
			<iterate open="(" close=")" conjunction="," property="positionItem">
				#positionItem[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="positionIdsItem">
			position_id  in
			<iterate open="(" close=")" conjunction="," property="positionIdsItem">
				#positionIdsItem[]#
			</iterate>
		</isNotEmpty>
		<isNotEmpty prepend="and" property="materialsName">
			(position like '%$materialsName$%' or material_name like '%$materialsName$%' or place like '%$materialsName$%')
		</isNotEmpty>
	</sql>

	<select id="queryGoodsMarterials" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		select
			goods_id as "goodsId",
			department_name as "departmentName",
			sub_center_name as "subCenterName",
			team_name as "teamName",
			line_code as "lineCode",
			line_name as "lineName",
			position_id as "positionId",
			position as "position",
			place as "place",
			first_level_class_name as "firstLevelClassName",
			second_level_class_name as "secondLevelClassName",
			material_name as "materialName",
			specifications as "specifications",
			quality as "quality",
			unit as "unit",
			custidianer as "custidianer",
			validiaty_date as "validiatyDate",
			scrap_date as "scrapDate",
			material_status as "materialStatus",
			remark as "remark",
			delete_flag as "deleteFlag",
			rec_creator as "recCreator",
			rec_create_time as "recCreateTime",
			rec_revisor as "recRevisor",
			rec_revise_time as "recReviseTime",
			ext1 as "ext1",
			ext2 as "ext2",
			ext3 as "ext3"
		<isNotEmpty prepend="," property="materialsName">
			CASE WHEN material_name = #materialsName# THEN 1 ELSE 0 END AS is_exact_match,
			(LENGTH(material_name) - LENGTH(REPLACE(material_name, #materialsName#, ''))) / LENGTH(#materialsName#) AS match_count
		</isNotEmpty>
		from ${cmpProjectSchema}.t_cmp_goods_marterials
		where 1=1 AND delete_flag='0'
		<include refid="sql_query_GoodsMarterials"></include>
		<isNotEmpty prepend="ORDER BY" property="materialsName">
			 is_exact_match desc, match_count desc,material_name asc
		</isNotEmpty>
	</select>

	<select id="queryGoodsMarterialsByStaName" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		select
			position as "position",
			max(position_id) as "positionId"
		from ${cmpProjectSchema}.t_cmp_goods_marterials
		where 1=1 AND delete_flag='0'
		<include refid="sql_query_GoodsMarterials"></include>
		group by position
	</select>

	<select id="countGoodsMarterials" parameterClass="java.util.Map" resultClass="int">
		select
			count(*)
		from ${cmpProjectSchema}.t_cmp_goods_marterials
		where 1=1 AND delete_flag='0'
		<include refid="sql_query_GoodsMarterials"></include>
	</select>

	<!-- 物理删除物资信息 TRUNCATE删除表数据不会影响表结构-->
	<delete id="deleteGoodsMarterials">
		DELETE FROM ${cmpProjectSchema}.t_cmp_goods_marterials
		where goods_id is not null
	</delete>

	<delete id="deleteBatchGoodsMarterials">
		DELETE FROM ${cmpProjectSchema}.t_cmp_goods_marterials
		where goods_id = #goodsId#
	</delete>

	<insert id="insertGoodsMarterials">
		INSERT INTO ${cmpProjectSchema}.t_cmp_goods_marterials (goods_id,  <!-- 唯一标识,UUID -->
				department_name,  <!-- 部门名称 -->
				sub_center_name,  <!-- 分中心名称 -->
				team_name,  <!-- 班组名称 -->
				line_code,  <!-- 线路编码 -->
				line_name,  <!-- 线路名称 -->
				position_id,  <!-- 位置ID（建筑物ID） -->
				position,  <!-- 位置名称（建筑物名称） -->
				place,  <!-- 地点 -->
				first_level_class_name,  <!-- 一级分类 -->
				second_level_class_name,  <!-- 二级分类 -->
				material_name,  <!-- 物资名称 -->
				specifications,  <!-- 规格 -->
				quality,  <!--现有数量 -->
				unit,  <!-- 单位 -->
				custidianer,  <!-- 保管人 -->
				validiaty_date,  <!-- 有效期 -->
				scrap_date,  <!-- 报废日期 -->
				material_status,  <!-- 物资状态 -->
				remark,  <!-- 备注 -->
				delete_flag,  <!-- 删除标识（0-未删除，1-已删除） -->
				rec_creator,  <!--创建人 -->
				rec_create_time,  <!-- 创建时间 -->
				rec_revisor,  <!-- 修改人 -->
				rec_revise_time,  <!-- 修改时间 -->
				ext1,  <!-- 扩展字段1 -->
				ext2,  <!-- 扩展字段2 -->
				ext3  <!-- 扩展字段3 -->
		)
		VALUES (#goodsId#, #departmentName#, #subCenterName#, #teamName#, #lineCode#, #lineName#, #positionId#, #position#,
		#place#, #firstLevelClassName#, #secondLevelClassName#, #materialName#, #specifications#, #quality#, #unit#,
		#custidianer#, #validiatyDate#, #scrapDate#, #materialStatus#, #remark#,
		#deleteFlag#,#recCreator#, #recCreateTime#, #recRevisor#, #recReviseTime#,#ext1#, #ext2#, #ext3#
		)
	</insert>

	<!--应急值守点信息 start-->
	<sql id="sql_query_dutyPoint">
		<isNotEmpty prepend="and" property="uuid">
			duty_point_id = #uuid#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="dutyPointId">
			duty_point_id = #dutyPointId#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="lineCode">
			line_code = #lineCode#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="lineName">
			line_name like '%$lineName$%'
		</isNotEmpty>
		<isNotEmpty prepend="and" property="dutyBuildingId">
			duty_building_id = #dutyBuildingId#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="dutyBuilding">
			duty_building like '%$dutyBuilding$%'
		</isNotEmpty>
		<isNotEmpty prepend="AND" property="positionItem">
			duty_building  in
			<iterate open="(" close=")" conjunction="," property="positionItem">
				#positionItem[]#
			</iterate>
		</isNotEmpty>
	</sql>

	<select id="queryDutyPointInfo" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		select
			duty_point_id as "dutyPointId",
			rec_id as "recId",
			line_code as "lineCode",
			line_name as "lineName",
			major as "major",
			team_name as "teamName",
			duty_building_id as "dutyBuildingId",
			duty_building as "dutyBuilding",
			duty_telphonenumber as "dutyTelphonenumber",
			duty_time as "dutyTime",
			jurisdiction as "jurisdiction",
			content as "content",
			remark as "remark",
			delete_flag as "deleteFlag",
			rec_creator as "recCreator",
			rec_create_time as "recCreateTime",
			rec_revisor as "recRevisor",
			rec_revise_time as "recReviseTime",
			ext1 as "ext1",
			ext2 as "ext2",
			ext3 as "ext3"
		from ${cmpProjectSchema}.t_cmp_duty_point
		where 1=1 AND delete_flag='0'
		<include refid="sql_query_dutyPoint"></include>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				rec_create_time desc
			</isEmpty>
		</dynamic>
	</select>

	<select id="queryDutyPointInfoByStaCode" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		select
			max(duty_building_id) as "dutyBuildingId",
			duty_building as "dutyBuilding"
		from ${cmpProjectSchema}.t_cmp_duty_point
		where 1=1 AND delete_flag='0'
		<include refid="sql_query_dutyPoint"></include>
			group by duty_building
	</select>

	<select id="countDutyPointInfo" parameterClass="java.util.Map" resultClass="int">
		select
		count(*)
		from ${cmpProjectSchema}.t_cmp_duty_point
		where 1=1 AND delete_flag='0'
		<include refid="sql_query_dutyPoint"></include>
		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderBy">
				$orderBy$
			</isNotEmpty>
			<isEmpty property="orderBy">
				rec_create_time desc
			</isEmpty>
		</dynamic>
	</select>

	<!-- 物理删除应急值守点信息 TRUNCATE删除表数据不会影响表结构-->
	<delete id="deleteDutyPointInfo">
		DELETE FROM ${cmpProjectSchema}.t_cmp_duty_point
		where duty_point_id is not null
	</delete>

	<delete id="deleteBatchDutyPointInfo">
		DELETE FROM ${cmpProjectSchema}.t_cmp_duty_point
		where
		rec_id = #recId#
	</delete>

	<insert id="insertDutyPoint">
		INSERT INTO ${cmpProjectSchema}.t_cmp_duty_point (duty_point_id,  <!-- 唯一标识,UUID -->
			rec_id,<!--关联安全系统业务ID-->
			line_code,  <!-- 线路编码 -->
			line_name,  <!-- 线路名称 -->
			major,  <!-- 专业 -->
			team_name,  <!-- 班组名称 -->
			duty_building_id,  <!-- 值守点ID（建筑物ID） -->
			duty_building,  <!--值守点中文名（建筑物中文名） -->
			duty_telphonenumber,  <!-- 值班电话 -->
			duty_time,  <!-- 值守时间 -->
			jurisdiction,  <!-- 管辖范围 -->
			content,  <!-- 工作内容 -->
			remark,  <!-- 备注 -->
			delete_flag,  <!-- 删除标识（0-未删除，1-已删除） -->
			rec_creator,  <!--创建人 -->
			rec_create_time,  <!-- 创建时间 -->
			rec_revisor,  <!-- 修改人 -->
			rec_revise_time,  <!-- 修改时间 -->
			ext1,  <!-- 扩展字段1 -->
			ext2,  <!-- 扩展字段2 -->
			ext3  <!-- 扩展字段3 -->
		)
		VALUES (#dutyPointId#,
		#recId#,
		#lineCode#, #lineName#, #major#, #teamName#, #dutyBuildingId#, #dutyBuilding#, #dutyTelphonenumber#,
		#dutyTime#, #jurisdiction#, #content#, #remark#,#deleteFlag#,#recCreator#,
		#recCreateTime#, #recRevisor#, #recReviseTime#,#ext1#, #ext2#, #ext3#
		)
	</insert>
	<!--应急值守点信息 end-->

	<!--风级对照关系 start-->
	<sql id="sql_query_widSpLevel">
		<isNotEmpty prepend="and" property="windMin">
			wind_min <![CDATA[  >=  ]]>  #windMin#
		</isNotEmpty>
		<isNotEmpty prepend="and" property="windMax">
			wind_max <![CDATA[  <=  ]]>  #windMax#
		</isNotEmpty>
	</sql>

	<select id="queryWidSpLevel" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		select
			rec_id as "recId",
			wind_level as "windLevel",
			wind_min as "windMin",
			wind_max as "windMax"
		from ${cmpProjectSchema}.t_cmp_wind_sp_level
		where 1=1
		<include refid="sql_query_widSpLevel"></include>
	</select>
	<!--风级对照关系 end-->

	<!--天气现象描述对照关系 start-->
	<sql id="sql_query_weatherDesc">
		<isNotEmpty prepend="and" property="weatherCode">
			weather_code = #weatherCode#
		</isNotEmpty>
	</sql>

	<select id="queryWeatherDesc" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		select
			rec_id as "recId",
			weather_code as "weatherCode",
			weather_desc as "weatherDesc"
		from ${cmpProjectSchema}.t_cmp_weather_desc
		where 1=1
		<include refid="sql_query_weatherDesc"></include>
	</select>
	<!--天气现象描述对照关系 end-->

	<!--GIS关键词搜索-->
		<!--查询应急物资数据（GIS关键词搜索）-->
		<sql id="martialData_keywordSearch">
			SELECT
				max(goods_id) as "uuid",
				position as "name",
				position as "address",
				1 as "type",
				ext1 as "lon",
				ext2 as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			FROM ${cmpProjectSchema}.t_cmp_goods_marterials
			where delete_flag ='0' and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(position like '%$keyword$%' or material_name like '%$keyword$%' or place like '%$keyword$%')
			</isNotEmpty>
			group by position,lon, lat<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
		</sql>

		<!--查询应急值守点数据（GIS关键词搜索）-->
		<sql id="dutyPointData_keywordSearch">
			select
				max(duty_point_id) as "uuid",
				duty_building as "name",
				duty_building as "address",
				2 as "type",
				max(ext1) as "lon",
				max(ext2) as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_duty_point
			where delete_flag ='0' and duty_building is not null
			and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(major like '%$keyword$%' or team_name like '%$keyword$%' or duty_building like '%$keyword$%' or jurisdiction like '%$keyword$%'
				or content like '%$keyword$%')
			</isNotEmpty>
			group by duty_building<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
		</sql>

		<!--查询应急资源基础数据（GIS关键词搜索）-->
		<sql id="baseData_keywordSearch">
			select
				' ' as "uuid",
				name as "name",
				address as "address",
				type as "type",
				sta_longitude as "lon",
				sta_dimension as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(sta_dimension)) * COS(RADIANS(sta_longitude) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(sta_dimension))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_gis_base_info
			where 1=1
			<isNotEmpty prepend="and" property="keyword">
				(name like '%$keyword$%' or address like '%$keyword$%')
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="typeArray">
				type  in
				<iterate open="(" close=")" conjunction="," property="typeArray">
					#typeArray[]#
				</iterate>
			</isNotEmpty>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
		</sql>

		<!--查询业务数据(应急物资、应急值守点)（GIS关键词搜索）-->
		<sql id="buinessData_keywordSearch">
			SELECT
				max(goods_id) as "uuid",
				position as "name",
				position as "address",
				1 as "type",
				ext1 as "lon",
				ext2 as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			FROM ${cmpProjectSchema}.t_cmp_goods_marterials
			where delete_flag ='0' and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(position like '%$keyword$%' or material_name like '%$keyword$%' or place like '%$keyword$%')
			</isNotEmpty>
			group by position,lon, lat<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
			union all
			select
<!--				max(rec_id) as "uuid",-->
			max(duty_point_id) as "uuid",
				duty_building as "name",
				duty_building as "address",
				2 as "type",
				max(ext1) as "lon",
				max(ext2) as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_duty_point
			where delete_flag ='0' and duty_building is not null
			and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(major like '%$keyword$%' or team_name like '%$keyword$%' or duty_building like '%$keyword$%' or jurisdiction like '%$keyword$%'
				or content like '%$keyword$%')
			</isNotEmpty>
			group by duty_building<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
		</sql>

		<!--查询应急物资数据与基础数据(应急物资、基础资源)（GIS关键词搜索）-->
		<sql id="maritalsAndBaseData_keywordSearch">
			SELECT
				max(goods_id) as "uuid",
				position as "name",
				position as "address",
				1 as "type",
				ext1 as "lon",
				ext2 as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			FROM ${cmpProjectSchema}.t_cmp_goods_marterials
			where delete_flag ='0' and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(position like '%$keyword$%' or material_name like '%$keyword$%' or place like '%$keyword$%')
			</isNotEmpty>
			group by position,lon, lat<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
			union all
			select
				' ' as "uuid",
				name as "name",
				address as "address",
				type as "type",
				sta_longitude as "lon",
				sta_dimension as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(sta_dimension)) * COS(RADIANS(sta_longitude) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(sta_dimension))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_gis_base_info
			where 1=1
			<isNotEmpty prepend="and" property="keyword">
				(name like '%$keyword$%' or address like '%$keyword$%')
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="typeArray">
				type  in
				<iterate open="(" close=")" conjunction="," property="typeArray">
					#typeArray[]#
				</iterate>
			</isNotEmpty>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
		</sql>

		<!--查询应急值守点数据与基础数据(应急值守点、基础资源)（GIS关键词搜索）-->
		<sql id="dutyPointAndBaseData_keywordSearch">
			select
<!--				max(rec_id) as "uuid",-->
			max(duty_point_id) as "uuid",
				duty_building as "name",
				duty_building as "address",
				2 as "type",
				max(ext1) as "lon",
				max(ext2) as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_duty_point
			where delete_flag ='0' and duty_building is not null
			and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(major like '%$keyword$%' or team_name like '%$keyword$%' or duty_building like '%$keyword$%' or jurisdiction like '%$keyword$%'
				or content like '%$keyword$%')
			</isNotEmpty>
			group by duty_building<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
			union all
			select
				' ' as "uuid",
				name as "name",
				address as "address",
				type as "type",
				sta_longitude as "lon",
				sta_dimension as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(sta_dimension)) * COS(RADIANS(sta_longitude) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(sta_dimension))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_gis_base_info
			where 1=1
			<isNotEmpty prepend="and" property="keyword">
				(name like '%$keyword$%' or address like '%$keyword$%')
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="typeArray">
				type  in
				<iterate open="(" close=")" conjunction="," property="typeArray">
					#typeArray[]#
				</iterate>
			</isNotEmpty>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
		</sql>

		<!--查询所有应急资源数据（GIS关键词搜索）-->
		<sql id="allData_keywordSearch">
			SELECT
				max(goods_id) as "uuid",
				position as "name",
				position as "address",
				1 as "type",
				ext1 as "lon",
				ext2 as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			FROM ${cmpProjectSchema}.t_cmp_goods_marterials
			where delete_flag ='0' and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(position like '%$keyword$%' or material_name like '%$keyword$%' or place like '%$keyword$%')
			</isNotEmpty>
			group by position,lon, lat<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
			union all
			select
<!--				max(rec_id) as "uuid",-->
			max(duty_point_id) as "uuid",
				duty_building as "name",
				duty_building as "address",
				2 as "type",
				max(ext1) as "lon",
				max(ext2) as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(ext2)) * COS(RADIANS(ext1) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(ext2))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_duty_point
			where delete_flag ='0' and duty_building is not null
			and (ext1 is not null or  ext2 is not null)
			<isNotEmpty prepend="and" property="keyword">
				(major like '%$keyword$%' or team_name like '%$keyword$%' or duty_building like '%$keyword$%' or jurisdiction like '%$keyword$%'
				or content like '%$keyword$%')
			</isNotEmpty>
			group by duty_building<isNotNull property="longitude">, distance</isNotNull>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
			union all
			select
				' ' as "uuid",
				name as "name",
				address as "address",
				type as "type",
				sta_longitude as "lon",
				sta_dimension as "lat"
			<isNotNull property="longitude">
				, 6371 * ACOS(
				COS(RADIANS(#dimension#)) * COS(RADIANS(sta_dimension)) * COS(RADIANS(sta_longitude) - RADIANS(#longitude#)) +
				SIN(RADIANS(#dimension#)) * SIN(RADIANS(sta_dimension))
				) AS distance
			</isNotNull>
			from ${cmpProjectSchema}.t_cmp_gis_base_info
			where 1=1
			<isNotEmpty prepend="and" property="keyword">
				(name like '%$keyword$%' or address like '%$keyword$%')
			</isNotEmpty>
			<isNotEmpty prepend="AND" property="typeArray">
				type  in
				<iterate open="(" close=")" conjunction="," property="typeArray">
					#typeArray[]#
				</iterate>
			</isNotEmpty>
			<isNotNull property="longitude">
				ORDER BY distance ASC
			</isNotNull>
		</sql>




		<select id="queryDataByKeyword" parameterClass="java.util.Map"
				resultClass="java.util.HashMap">
			select
				"uuid" as "uuid",
				"name" as "name",
				"address" as "address",
				"type" as "type",
				"lon" as "lon",
				"lat" as "lat"
			<isNotNull property="longitude">
				, distance as distance
			</isNotNull>
			from (
			<isEqual property="conditionStr" compareValue="martialData_keywordSearch">
				<!--查询应急物资数据-->
				<include refid="martialData_keywordSearch"/>
			</isEqual>
			<isEqual property="conditionStr" compareValue="dutyPointData_keywordSearch">
				<!--查询应急值守点数据-->
				<include refid="dutyPointData_keywordSearch"/>
			</isEqual>
			<isEqual property="conditionStr" compareValue="baseData_keywordSearch">
				<!--查询应急资源基础数据-->
				<include refid="baseData_keywordSearch"/>
			</isEqual>
			<isEqual property="conditionStr" compareValue="buinessData_keywordSearch">
				<!--查询业务数据(应急物资、应急值守点)-->
				<include refid="buinessData_keywordSearch"/>
			</isEqual>
			<isEqual property="conditionStr" compareValue="maritalsAndBaseData_keywordSearch">
				<!--查询应急物资数据与基础数据(应急物资、基础资源)-->
				<include refid="maritalsAndBaseData_keywordSearch"/>
			</isEqual>
			<isEqual property="conditionStr" compareValue="dutyPointAndBaseData_keywordSearch">
				<!--查询应急值守点数据与基础数据(应急值守点、基础资源)-->
				<include refid="dutyPointAndBaseData_keywordSearch"/>
			</isEqual>
			<isEqual property="conditionStr" compareValue="allData_keywordSearch">
				<!--查询所有应急资源数据-->
				<include refid="allData_keywordSearch"/>
			</isEqual>
			)
			order by type <isNotNull property="longitude">, distance </isNotNull> asc
		</select>

		<select id="countByKeyword" parameterClass="java.util.Map"
				resultClass="int">
		select count(1) from (
			select
				"uuid" as "uuid",
				"name" as "name",
				"address" as "address",
				"type" as "type",
				"lon" as "lon",
				"lat" as "lat"
				<isNotNull property="longitude">
					, distance as distance
				</isNotNull>
				from (
				<isEqual property="conditionStr" compareValue="martialData_keywordSearch">
					<!--查询应急物资数据-->
					<include refid="martialData_keywordSearch"/>
				</isEqual>
				<isEqual property="conditionStr" compareValue="dutyPointData_keywordSearch">
					<!--查询应急值守点数据-->
					<include refid="dutyPointData_keywordSearch"/>
				</isEqual>
				<isEqual property="conditionStr" compareValue="baseData_keywordSearch">
					<!--查询应急资源基础数据-->
					<include refid="baseData_keywordSearch"/>
				</isEqual>
				<isEqual property="conditionStr" compareValue="buinessData_keywordSearch">
					<!--查询业务数据(应急物资、应急值守点)-->
					<include refid="buinessData_keywordSearch"/>
				</isEqual>
				<isEqual property="conditionStr" compareValue="maritalsAndBaseData_keywordSearch">
					<!--查询应急物资数据与基础数据(应急物资、基础资源)-->
					<include refid="maritalsAndBaseData_keywordSearch"/>
				</isEqual>
				<isEqual property="conditionStr" compareValue="dutyPointAndBaseData_keywordSearch">
					<!--查询应急值守点数据与基础数据(应急值守点、基础资源)-->
					<include refid="dutyPointAndBaseData_keywordSearch"/>
				</isEqual>
				<isEqual property="conditionStr" compareValue="allData_keywordSearch">
					<!--查询所有应急资源数据-->
					<include refid="allData_keywordSearch"/>
				</isEqual>
					)
			)
		</select>

	<!--GIS关键词搜索-->


	<!--计算距离：根据经纬度在基础车站数据表中计算在经纬度5KM范围内的车站-->
	<select id="queryCalDistanceByLatAndLong" parameterClass="java.util.Map"
			resultClass="java.util.HashMap">
		SELECT
			line_id AS "lineId",
			sta_id AS "staId",
			sta_cname AS "staCname",
			sta_longitude AS "staLongitude",
			sta_dimension AS "staDimension",
			transfer_info AS "transferInfo",
			(
			6371 * 2 * ASIN(
			SQRT(
			POWER(SIN((#lat# - sta_dimension) * 3.141592653589793 / 180 / 2), 2) +
			COS(#lat# * 3.141592653589793 / 180) * COS(sta_dimension * 3.141592653589793 / 180) *
			POWER(SIN((#longitude# - sta_longitude) * 3.141592653589793 / 180 / 2), 2)
			)
			)
			) AS "distanceKM",
			CASE
			WHEN (
			6371 * 2 * ASIN(
			SQRT(
			POWER(SIN((#lat# - sta_dimension) * 3.141592653589793 / 180 / 2), 2) +
			COS(#lat# * 3.141592653589793 / 180) * COS(sta_dimension * 3.141592653589793 / 180) *
			POWER(SIN((#longitude# - sta_longitude) * 3.141592653589793 / 180 / 2), 2)
			)
			)
			) <![CDATA[ <= ]]> 3 THEN 'Y' ELSE 'N'
			END AS "lessEqual3KM"
			FROM ${baseDataSchema}.noccbase_sta_info
			WHERE (
			6371 * 2 * ASIN(
			SQRT(
			POWER(SIN((#lat# - sta_dimension) * 3.141592653589793 / 180 / 2), 2) +
			COS(#lat# * 3.141592653589793 / 180) * COS(sta_dimension * 3.141592653589793 / 180) *
			POWER(SIN((#longitude# - sta_longitude) * 3.141592653589793 / 180 / 2), 2)
			)
			)
			) <![CDATA[ <= ]]> 5
		ORDER BY "distanceKM" ASC
	</select>






</sqlMap>
