<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="YJCZ01">

    <sql id="sql_query">
        <isNotEmpty prepend="and" property="fdName">
            e.fd_name like '%$fdName$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="fdRespT">
            e.fd_resp_t = #fdRespT#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="fdUuid">
            fd_uuid = #fdUuid#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="fdTimeStart">
            TO_DATE(e.fd_time) <![CDATA[ >= ]]> TO_DATE(#fdTimeStart#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="fdTimeEnd">
            TO_DATE(e.fd_time) <![CDATA[ <= ]]> TO_DATE(#fdTimeEnd#)
        </isNotEmpty>
        <isNotEmpty prepend="and" property="State">
            e.fd_disposal_t = #State#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="isRead">
            e.fd_extend1 = #isRead#<!-- 是否已读参数，0-未读，1-已读 -->
        </isNotEmpty>
    </sql>

    <select id="query" parameterClass="java.util.Map"
            resultClass="java.util.HashMap">
        Select
        e.fd_uuid as "fdUuid",
        e.fd_name as "fdName",
        e.fd_time as "fdTime",
        e.fd_plan as "fdPlan",
        e.fd_area_t as "fdAreaT",
        e.fd_line as "fdLine",
        e.fd_ispush as "ispush",
        e.fd_msg_name as "fdMsgName",
        e.fd_msg_stage as "fdMsgStage",
        e.fd_msg_type as "fdMsgType",
        e.fd_speciality_type as "fdSpecialityType",
        e.fd_resp_t as "fdRespT",
        e.fd_msg_desc as "fdMsgDesc",
        e.fd_disposal_t as "fdDisposalT",
        e.fd_phone_notify as "eventPhoneNotify",
        e.fd_emergency_notify as "eventEmergencyNotify",
        e.fd_operator as "fdOperator",
        e.fd_report_t as "fdReportT",
        e.fd_operator_id as "fdOperatorId",
        e.fd_records as "eventRecords",
        st.fd_statioin_number as "fdStationNumber",
        se.fd_direction as "fdDirection",
        se.fd_start_station as "fdStartStation",
        se.fd_end_station as "fdEndStation",
        ted.item_cname as "fdDisposalTName",
        ted1.item_cname as "fdRespTName",
        ted2.item_ename as "fdReportTClass"
        from ${cmpProjectSchema}.t_event e
        left join ${cmpProjectSchema}.t_event_station st on st.fd_event_uuid = e.fd_uuid
        left join ${cmpProjectSchema}.t_event_section se on se.fd_event_uuid = e.fd_uuid
        left join ${platSchema}.tedcm01 ted on ted.codeset_code = 'nocc.yj.cz03' and ted.item_code = e.fd_disposal_t
        left join ${platSchema}.tedcm01 ted1 on ted1.codeset_code = 'nocc.yj.cz02' and ted1.item_code = e.fd_resp_t
        left join ${platSchema}.tedcm01 ted2 on ted2.codeset_code = 'nocc.yj.cz01' and ted2.item_code = e.fd_report_t
        where 1=1
        <isEmpty prepend="and" property="isEventHistory">
            (e.fd_disposal_t in (30001,30002)
            <isNotEmpty prepend="or" property="allState">
                e.fd_disposal_t in
                (30001,30002,30003,30004,30005)<!-- 事件状态,int, 30001-未处置，30002-处置中，30003-自动解除，30004-完成解除，30005-手动解除 -->
            </isNotEmpty>
            )
            <isEmpty prepend="and" property="isYJBJ">
                fd_isshow=1 <!--判断是否显示在事件列表-->
            </isEmpty>
        </isEmpty>
        <isNotEmpty prepend="and" property="isEventHistory">
            (e.fd_disposal_t = 30004 or ( (e.fd_disposal_t = 30003 or e.fd_disposal_t = 30005) and (fd_report_t = 40801
            or fd_report_t = 40901) ) )
        </isNotEmpty>
        <include refid="sql_query"></include>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                e.fd_time desc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" parameterClass="java.util.Map" resultClass="int">
        Select
        count(*)
        from ${cmpProjectSchema}.t_event e
        left join ${cmpProjectSchema}.t_event_station st on st.fd_event_uuid = e.fd_uuid
        left join ${cmpProjectSchema}.t_event_section se on se.fd_event_uuid = e.fd_uuid
        where 1=1
        <isEmpty prepend="and" property="isEventHistory">
            e.fd_disposal_t in (30001,30002)
        </isEmpty>
        <isNotEmpty prepend="and" property="isEventHistory">
            (e.fd_disposal_t = 30004 or ( (e.fd_disposal_t = 30003 or e.fd_disposal_t = 30005) and (fd_report_t = 40801
            or fd_report_t = 40901) ) )
        </isNotEmpty>
        <include refid="sql_query"></include>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                e.fd_created_time desc
            </isEmpty>
        </dynamic>
    </select>

    <select id="queryEventLevelCode" parameterClass="java.util.Map"
            resultClass="java.util.HashMap">
        Select
        ted.item_cname as "event_level_name",
        ted.item_code as "event_level"
        from ${platSchema}.tedcm01 ted
        where 1=1 and ted.codeset_code = 'nocc.yj.cz02'
    </select>

    <select id="queryStation" parameterClass="java.util.Map"
            resultClass="java.util.HashMap">
        Select
        fd_event_uuid as "fdEventUuid",
        fd_statioin_number as "fdStatioinNumber",
        fd_created_by as "fdCreatedBy",
        fd_created_time as "fdCreatedTime",
        fd_update_by as "fdUpdateBy",
        fd_update_time as "fdUpdateTime"
        from ${cmpProjectSchema}.t_event_station
        where 1=1
        <isNotEmpty prepend="and" property="fdEventUuid">
            fd_event_uuid = #fdEventUuid#
        </isNotEmpty>
    </select>

    <select id="querySection" parameterClass="java.util.Map"
            resultClass="java.util.HashMap">
        Select
        fd_event_uuid as "fdEventUuid",
        fd_direction as "fdDirection",
        fd_start_station as "fdStartStation",
        fd_end_station as "fdEndStation",
        fd_created_by as "fdCreatedBy",
        fd_created_time as "fdCreatedTime",
        fd_update_by as "fdUpdateBy",
        fd_update_time as "fdUpdateTime"
        from ${cmpProjectSchema}.t_event_section
        where 1=1
        <isNotEmpty prepend="and" property="fdEventUuid">
            fd_event_uuid = #fdEventUuid#
        </isNotEmpty>
    </select>

    <!-- 车站物理删除 -->
    <delete id="deleteStation">
        DELETE FROM ${cmpProjectSchema}.t_event_station
        WHERE
        fd_event_uuid = #fdEventUuid#
    </delete>

    <!-- 区间物理删除 -->
    <delete id="deleteSection">
        DELETE FROM ${cmpProjectSchema}.t_event_section
        WHERE
        fd_event_uuid = #fdEventUuid#
    </delete>

    <insert id="insert">
        INSERT INTO ${cmpProjectSchema}.t_event (
        fd_uuid  <!-- UUID -->
        <isNotEmpty prepend="," property="fdName">
            fd_name  <!-- 事件名称 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdTime">
            fd_time  <!-- 发生时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdPlan">
            fd_plan  <!-- 选用预案 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdAreaT">
            fd_area_t  <!-- 发生区域,int, 10001-车站，10002-区间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdLine">
            fd_line  <!-- 发生线路 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgName">
            fd_msg_name  <!-- 信息模板名称 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgStage">
            fd_msg_stage  <!-- 信息模板发布阶段 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgType">
            fd_msg_type  <!-- 信息模板分类 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdSpecialityType">
            fd_speciality_type  <!-- 事件专业群组,int,20001-供电，20002-机电，20003-信号，20004-车辆，20005-线路 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="ispush">
            fd_ispush  <!-- 是否推送防汛群组字段 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdRespT">
            fd_resp_t  <!-- 响应等级（响应等级）,int, 360001-一级，360002-二级，360003-三级，360004-四级，360005-无 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgDesc">
            fd_msg_desc <!-- 事件描述 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdDisposalT">
            fd_disposal_t  <!-- 事件状态,int, 30001-未处置，30002-处置中，30003-自动解除，30004-完成解除，30005-手动解除 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdPhoneNotify">
            fd_phone_notify  <!-- 电话通知人员 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdEmergencyNotify">
            fd_emergency_notify  <!-- 应急通知人员 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdOperator">
            fd_operator  <!-- 填报人员 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdReportT">
            fd_report_t  <!-- 上报方式 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            fd_created_by  <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            fd_created_time  <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            fd_update_by    <!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            fd_update_time    <!-- 更新时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend1">
            fd_extend1    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend2">
            fd_extend2    <!-- 拓展字段2 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend3">
            fd_extend3    <!-- 拓展字段3 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdOperatorId">
            fd_operator_id    <!-- 上报人钉钉id -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdRecords">
            fd_records    <!-- 发布历史信息 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdIsshow">
            fd_isshow    <!-- 是否展示 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdWarnLevel">
            fd_warn_level    <!-- 预警等级 -->
        </isNotEmpty>
        )
        VALUES (#fdUuid#
        <isNotEmpty prepend="," property="fdName">
            #fdName#  <!-- 事件名称 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdTime">
            #fdTime#  <!-- 发生时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdPlan">
            #fdPlan#  <!-- 选用预案 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdAreaT">
            #fdAreaT#  <!-- 发生区域,int, 10001-车站，10002-区间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdLine">
            #fdLine#  <!-- 发生线路 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgName">
            #fdMsgName#  <!-- 信息模板名称 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgStage">
            #fdMsgStage#   <!-- 信息模板发布阶段 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgType">
            #fdMsgType#  <!-- 信息模板分类 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdSpecialityType">
            #fdSpecialityType#   <!-- 事件专业群组,int,20001-供电，20002-机电，20003-信号，20004-车辆，20005-线路 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="ispush">
            #ispush#  <!-- 是否推送防汛群组字段 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdRespT">
            #fdRespT# <!-- 响应等级（响应等级）,int, 360001-一级，360002-二级，360003-三级，360004-四级，360005-无 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgDesc">
            #fdMsgDesc# <!-- 事件描述 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdDisposalT">
            #fdDisposalT#  <!-- 事件状态,int, 30001-未处置，30002-处置中，30003-自动解除，30004-完成解除，30005-手动解除 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdPhoneNotify">
            #fdPhoneNotify#  <!-- 电话通知人员 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdEmergencyNotify">
            #fdEmergencyNotify#  <!-- 应急通知人员 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdOperator">
            #fdOperator#  <!-- 填报人员 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdReportT">
            #fdReportT# <!-- 上报方式 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            #fdCreatedBy#    <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            #fdCreatedTime#    <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdDescTemplate">
            #fdDescTemplate#    <!-- 描述模板（预留） -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            #fdUpdateBy#    <!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            #fdUpdateTime#    <!-- 更新时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend1">
            #fdExtend1#    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend2">
            #fdExtend2#    <!-- 拓展字段3 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend3">
            #fdExtend3#    <!-- 拓展字段3 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdOperatorId">
            #fdOperatorId#    <!-- 上报人钉钉id -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdRecords">
            #fdRecords#    <!-- 发布历史信息 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdIsshow">
            #fdIsshow#    <!-- 是否展示 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdWarnLevel">
            fd_warn_level    <!-- 预警等级 -->
        </isNotEmpty>
        )
    </insert>

    <!-- 新增事件车站表 -->
    <insert id="insertStation">
        INSERT INTO ${cmpProjectSchema}.t_event_station(
        fd_event_uuid  <!-- 应急事件uuid -->
        <isNotEmpty prepend="," property="fdStatioinNumber">
            fd_statioin_number    <!-- 车站编号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            fd_created_by  <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            fd_created_time  <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            fd_update_by    <!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            fd_update_time    <!-- 更新时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend1">
            fd_extend1    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend2">
            fd_extend2    <!-- 拓展字段2 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend3">
            fd_extend3    <!-- 拓展字段3 -->
        </isNotEmpty>
        )
        VALUES (#fdEventUuid#
        <isNotEmpty prepend="," property="fdStatioinNumber">
            #fdStatioinNumber#    <!-- 车站编号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            #fdCreatedBy#  <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            #fdCreatedTime#  <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            #fdUpdateBy#    <!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            #fdUpdateTime#    <!-- 更新时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend1">
            #fdExtend1#    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend2">
            #fdExtend2#    <!-- 拓展字段3 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend3">
            #fdExtend3#    <!-- 拓展字段3 -->
        </isNotEmpty>
        )
    </insert>

    <!-- 新增事件区间表 -->
    <insert id="insertSection">
        INSERT INTO ${cmpProjectSchema}.t_event_section(
        fd_event_uuid  <!-- 应急事件uuid -->
        <isNotEmpty prepend="," property="fdDirection">
            fd_direction  <!-- 方向 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdStartStation">
            fd_start_station  <!-- 起点车站编号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdEndStation">
            fd_end_station  <!-- 终点车站编号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            fd_created_by  <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            fd_created_time  <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            fd_update_by    <!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            fd_update_time    <!-- 更新时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend1">
            fd_extend1    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend2">
            fd_extend2    <!-- 拓展字段2 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend3">
            fd_extend3    <!-- 拓展字段3 -->
        </isNotEmpty>
        )
        VALUES (#fdEventUuid#
        <isNotEmpty prepend="," property="fdDirection">
            #fdDirection#  <!-- 方向 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdStartStation">
            #fdStartStation#  <!-- 起点车站编号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdEndStation">
            #fdEndStation#  <!-- 终点车站编号 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedBy">
            #fdCreatedBy#  <!-- 创建人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdCreatedTime">
            #fdCreatedTime#  <!-- 创建时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateBy">
            #fdUpdateBy#    <!-- 更新人 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdUpdateTime">
            #fdUpdateTime#    <!-- 更新时间 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend1">
            #fdExtend1#    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend2">
            #fdExtend2#    <!-- 拓展字段3 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdExtend3">
            #fdExtend3#    <!-- 拓展字段3 -->
        </isNotEmpty>
        )
    </insert>

    <update id="update">
        UPDATE ${cmpProjectSchema}.t_event
        set
        fd_uuid = #fdUuid#
        <isNotEmpty prepend=" , " property="fdName">
            fd_name = #fdName#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdTime">
            fd_time = #fdTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdPlan">
            fd_plan = #fdPlan#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdAreaT">
            fd_area_t = #fdAreaT#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdLine">
            fd_line = #fdLine#
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgName">
            fd_msg_name = #fdMsgName#  <!-- 信息模板名称 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgStage">
            fd_msg_stage = #fdMsgStage#   <!-- 信息模板发布阶段 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdMsgType">
            fd_msg_type = #fdMsgType#  <!-- 信息模板分类 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdSpecialityType">
            fd_speciality_type = #fdSpecialityType#
        </isNotEmpty>
        <isNotEmpty prepend="," property="ispush">
            fd_ispush = #ispush# <!-- 是否推送防汛群组字段 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdRespT">
            fd_resp_t = #fdRespT#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdWarnLevel">
            fd_warn_level = #fdWarnLevel#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdMsgDesc">
            fd_msg_desc = #fdMsgDesc#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdDisposalT">
            fd_disposal_t = #fdDisposalT#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdPhoneNotify">
            fd_phone_notify = #fdPhoneNotify#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdEmergencyNotify">
            fd_emergency_notify = #fdEmergencyNotify#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdOperator">
            fd_operator = #fdOperator#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdReportT">
            fd_report_t = #fdReportT#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend1">
            fd_extend1 = #fdExtend1#    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend2">
            fd_extend2 = #fdExtend2#    <!-- 拓展字段2 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend3">
            fd_extend3 = #fdExtend3#    <!-- 拓展字段3 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdRecords">
            fd_records = #fdRecords#    <!-- 发布历史信息 -->
        </isNotEmpty>
        <isNotEmpty prepend="," property="fdIsshow">
            fd_isshow = #fdIsshow#     <!-- 是否展示 -->
        </isNotEmpty>
        WHERE fd_uuid = #fdUuid#
    </update>

    <update id="updateStation">
        UPDATE ${cmpProjectSchema}.t_event_station
        set
        fd_event_uuid = #fdEventUuid#
        <isNotEmpty prepend=" , " property="fdStatioinNumber">
            fd_statioin_number = #fdStatioinNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        where fd_event_uuid = #fdEventUuid#
    </update>

    <update id="updateSection">
        UPDATE ${cmpProjectSchema}.t_event_section
        set
        fd_event_uuid = #fdEventUuid#
        <isNotEmpty prepend=" , " property="fdDirection">
            fd_direction = #fdDirection#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdStartStation">
            fd_start_station = #fdStartStation#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdEndStation">
            fd_end_station = #fdEndStation#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend1">
            fd_extend1 = #fdExtend1#    <!-- 拓展字段1 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend2">
            fd_extend2 = #fdExtend2#    <!-- 拓展字段2 -->
        </isNotEmpty>
        <isNotEmpty prepend=" , " property="fdExtend3">
            fd_extend3 = #fdExtend3#    <!-- 拓展字段3 -->
        </isNotEmpty>
        where fd_event_uuid = #fdEventUuid#
    </update>

    <select id="queryStaticInfo" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        select
        NVL(sum(case when fd_report_t in (40101,40102,40504) then 1 else 0 end),0) as
        "hzyj",<!-- 火灾报警：40101-火灾模式，40102-气灭盘气体释放，40504-列车火灾报警 -->
        NVL(sum(case when fd_report_t in (40401,40402) then 1 else 0 end),0) as
        "klbj",<!-- 客流报警：40401-大客流一级预警（进站/出站/换乘），40402-断面满载率一级预警， -->
        NVL(sum(case when fd_report_t in (40201,40202,40203) then 1 else 0 end),0) as
        "sdbj",<!-- 失电故障：40201-接触网供电分区失电，40202-主所失电，40203-400VⅠ段、Ⅱ段同时失电， -->
        NVL(sum(case when fd_report_t in (40601) then 1 else 0 end),0) as "sbgz",<!-- 水泵故障：40601-危险水位报警 -->
        NVL(sum(case when fd_report_t in (40301,40302,40303,40304,40701) then 1 else 0 end),0) as
        "xhgz",<!-- 信号故障：40301-列车区间停车超时，40302-列车严重偏离计划，40303-道岔挤岔，40304-联锁设备故障，40701-闸机75%故障信息 -->
        NVL(sum(case when fd_report_t in (40801,40901) then 1 else 0 end),0) as
        "rgsb",<!-- 人工上报：40801-occ(智能应急调度)；40901-nocc -->
        NVL(sum(case when fd_resp_t = 360001 then 1 else 0 end),0) as "xy1",<!-- I级响应 -->
        NVL(sum(case when fd_resp_t = 360002 then 1 else 0 end),0) as "xy2",<!-- II级响应 -->
        NVL(sum(case when fd_resp_t = 360003 then 1 else 0 end),0) as "xy3",<!-- III级响应 -->
        NVL(sum(case when fd_resp_t = 360004 then 1 else 0 end),0) as "xy4"<!-- IV级响应 -->
        from ${cmpProjectSchema}.t_event where fd_disposal_t = 30004 and year(TO_DATE(fd_update_time,'YYYY-MM-DD')) =
        YEAR(TODAY);
    </select>

</sqlMap>
