package com.baosight.pfa.kf.zf.service;

import cn.hutool.core.convert.Convert;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.BaseDataUtils;
import com.baosight.pfa.kf.common.DataProcessUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.zfUtils;
import lombok.extern.slf4j.Slf4j;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 客流精细化——车站客流综合分析服务
 * @date 2023/12/05 15:36
 */
@Slf4j
public class ServiceKFZF03 extends ServiceBase {
   @Override
   public EiInfo initLoad(EiInfo initInfo) {
      return initInfo;
   }

   //车站换乘信息
   private List<Map<String, Object>> baseTPFInfo = new ArrayList<>();
   //用于计算累计数据
   private List<Map<String, Object>> changeSumData = new ArrayList<>();
   //车站基础信息
   private List<Map<String, Object>> stationInfo = new ArrayList<>();
   //时间轴
   private List<String> times = new ArrayList<>();
   private List<String> fiveMinTimes = new ArrayList<>();
   //传入参数时间，用于换乘折线图数据处理
   private String parStartTime = "";
   private String parEndTime = "";
   private String changePeriodOfTime = "";

   public List<Map<String, Object>> getStationBaseTypes(EiInfo info){
      //车站
      List<Map<String, Object>> stationsBase = BaseDataUtils.queryBaseStaData();
      this.stationInfo = stationsBase;
      Map<String, List<String>> stationsBaseNameMap = stationsBase.stream().collect(Collectors.toMap(
              e -> e.get("sta_cname").toString(),
              e -> {
                 List<String> ids = new ArrayList<>();
                 ids.add(e.get("sta_id").toString());
                 return ids;
              },
              (v1, v2) -> {v1.addAll(v2); return v1;}
      ));

      //将车站基础数据根据line_id进行分组
      //此时stationBaseMap长度则为线路条数，即作为下拉框第1级条数
      Map<String, List<Map<String, Object>>> stationLinesMap = zfUtils.dataGroupingLinkedHashMap(stationsBase, "line_id");
      //按照stationBaseMap key（line_id）值进行排序，否则线路显示顺序混乱
      List<String> lines = new ArrayList<>(stationLinesMap.keySet());
      Collections.sort(lines);

      List<Map<String, Object>> result = new ArrayList<>();

      for (String line: lines){
         List<Map<String, Object>> oneLineStationsBase = stationLinesMap.get(line);
         //1级目录内数据
         Map<String, Object> oneLineStations = new LinkedHashMap<>();
         oneLineStations.put("label", oneLineStationsBase.get(0).get("line_cname"));
         oneLineStations.put("value", oneLineStationsBase.get(0).get("line_id"));
         //2级目录容器，oneLineStations["children"]
         List<Map<String, Object>> second = new ArrayList<>();
         for (Map<String, Object> item : oneLineStationsBase){
            //三级目录内数据
            Map<String, Object> secondItem = new LinkedHashMap<>();
            String staName = Convert.toStr(item.get("sta_cname"));
            secondItem.put("label", staName);
            secondItem.put("value", Optional.ofNullable(stationsBaseNameMap.get(staName)).orElse(new ArrayList<>()));
            second.add(secondItem);
         }
         oneLineStations.put("children", second);
         result.add(oneLineStations);
      }
      return result;
   }


   /**
    * @deprecated 获取车站基础信息
    */
   public List<Map<String, Object>> getStationInfo() {
      EiInfo info = new EiInfo();
      EiInfo outInfo = BaseDataUtils.queryStation(info);
      return (List<Map<String, Object>>) outInfo.getBlock("result").getRows();
   }

   /**
    * @param inInfo
    * @deprecated 传入参数处理
    */
   public Map<String, Object> getQueryParm(EiInfo inInfo) {
      this.stationInfo = getStationInfo();
      Map params = (Map) inInfo.getAttr().get("params");
      String queryDate = Convert.toStr(params.get("queryDate"));
      //起止时间
      String startTime = Convert.toStr(params.get("startTime"),"");
      String endTime = Convert.toStr(params.get("endTime"),"");
      changePeriodOfTime = startTime + "-" + endTime;
      //时间粒度
      int intervalT = Convert.toInt(params.get("intervalT"));
      List<String> times = zfUtils.getTimes(startTime, endTime, intervalT, queryDate, 1);
      //查询参数列表
      Map<String, Object> result = new HashMap<>(DataProcessUtils.setQueryTime(startTime, endTime, queryDate));
      this.parStartTime = Convert.toStr(result.get("startTime"),"");
      this.parEndTime = Convert.toStr(result.get("endTime"),"");
      result.put("date", queryDate);//-> 查询日期
      this.times = times;//-> 时间轴列表
      fiveMinTimes = zfUtils.getTimes(startTime, endTime, 410001, queryDate, 1);
      result.put("intervalT", intervalT);//-> 时间粒度
      result.put("lineNumber", params.get("lineNumber"));//-> 线路编号
      String initStationNumber = Convert.toStr(params.get("stationNumber"));
      result.put("stationNumber", initStationNumber);//-> 车站编号
      result.put("times", times);
      String initStationName = "";
      //匹配到换乘站名称后，通过名称匹配出改换乘站所对应的所有车站id（换乘站有多个id），以此查询所有方向
      for (Map<String, Object> item : stationInfo) {
         if (initStationNumber.equals(Convert.toStr(item.get("sta_id")))) {
            initStationName = Convert.toStr(item.get("sta_cname"));
            break;
         }
      }
      //sql api参数
      List<Map<String, Object>> stationNumbers = new ArrayList<>();
      for (Map<String, Object> item : stationInfo) {
         if (initStationName.equals(Convert.toStr(item.get("sta_cname")))) {
            Map<String, Object> map = new HashMap<>();
            map.put("stationNumber", Convert.toStr(item.get("sta_id")));
            stationNumbers.add(map);
         }
      }
      result.put("stationNumbers", stationNumbers);
      return result;
   }

   /**
    * @deprecated 传入参数处理
    */
   public Map<String, Object> getQueryParam(EiInfo inInfo) {
      this.stationInfo = getStationInfo();
      Map<String, String> stationsBaseMap = this.stationInfo.stream().collect(Collectors.toMap(
              e -> e.get("sta_id").toString(),
              e -> {
                 int transfer_info = "".equals(e.get("transfer_info")) ? 0 : 1;
                 return e.get("sta_cname")
                         +","
                         +transfer_info;
              },
              (v1, v2) -> v1
      ));
      Map params = (Map) inInfo.getAttr().get("params");
      String queryDate = Convert.toStr(params.get("queryDate"));
      //起止时间
      String startTime = Convert.toStr(params.get("startTime"),"");
      String endTime = Convert.toStr(params.get("endTime"),"");
      //是否为四点到四点，即查全日
      boolean isAllDay = "04:00".equals(startTime) && "04:00".equals(endTime);
      Object allPeriodOfTime = startTime + "-" + endTime;
      //时间粒度
      int intervalT = Convert.toInt(params.get("intervalT"));
      List<String> times = zfUtils.getTimes(startTime, endTime, intervalT, queryDate, 1);
      List<String> stationNumbers = (List<String>) params.get("stationNumber");
      String staInfo = Optional.ofNullable(stationsBaseMap.get(stationNumbers.get(0))).orElse(",0");
      String staName = staInfo.split(",")[0];
      String transfer_info = staInfo.split(",")[1];
      //查询参数列表
      Map<String, Object> result = new HashMap<>();
      this.parStartTime = startTime;
      this.parEndTime = endTime;
      result.put("date", queryDate);//-> 查询日期
      result.put("partitionDate", Convert.toInt(queryDate.replaceAll("-", "")));
      this.times = times;//-> 时间轴列表
      fiveMinTimes = zfUtils.getTimes(startTime, endTime, 410001, queryDate, 1);
      result.put("staId", stationNumbers.get(0));
      result.put("staName", staName);
      result.put("transfer_info", transfer_info);
      result.put("endTime", queryDate + " 23:59:00");
      result.put("intervalT", intervalT);//-> 时间粒度
      result.put("stationNumber", stationNumbers);//-> 车站编号
      result.put("lineNumber", params.get("lineNumber"));//-> 车站编号
      result.put("times", times);
      result.put("isAllDay", isAllDay);
      result.put("allPeriodOfTime", allPeriodOfTime);
      return result;
   }

   /**
    * 通过时间段和日期得到开始、结束时间（yyyy-MM-dd HH:mm:ss）
    * @param date yyyy-MM-dd
    * @param time 时段 HH:mm-HH:mm
    * @return Map<String, String>
    */
   public Map<String, String> getTimeStr(String date, String time){
      String start = time.substring(0, 5);
      String end = time.substring(6);
      Map<String, String> map = new HashMap<>();
      map.put("startTime", date + " " + start + ":00");
      map.put("endTime", date + " " + end + ":00");
      return map;
   }
   /*车站客流数据处理(hxj)*/
   private List<Map<String, Object>> handleStationPFInfo(Map<String, Map<String, Object>> stationPFMap, Map<String, Object> parMap) {
      //hxj:改成将时间轴元素作为key获取数据，若无数据则补0，以此修正时间轴下标与趋势图数据个数、下标对应不上的问题
      List<Map<String, Object>> list = new ArrayList<>();
      //匹配车站名称
      for (String time : times){
         Map<String, Object> dateMap = Optional.ofNullable(stationPFMap.get(time)).orElse(new HashMap<>());
         Map<String, Object> resultItem = new HashMap<>();
         resultItem.put("date",parMap.get("date"));
         resultItem.put("stationNumber", parMap.get("staId"));
         resultItem.put("lineNumber", parMap.get("lineNumber"));
         resultItem.put("intervalT", parMap.get("intervalT"));
         resultItem.put("startTime", dateMap.get("start"));
         resultItem.put("endTime", dateMap.get("end"));
         resultItem.put("countIn", dateMap.get("in") == null? 0:dateMap.get("in"));
         resultItem.put("countOut", dateMap.get("out") == null? 0:dateMap.get("out"));
         resultItem.put("countTrans", dateMap.get("trans") == null? 0:dateMap.get("trans"));
         resultItem.put("countDis", dateMap.get("dis") == null? 0:dateMap.get("dis"));
         resultItem.put("countRs", dateMap.get("rs") == null? 0:dateMap.get("rs"));
         if (dateMap.get("start")==null || dateMap.get("end")==null){
            resultItem.putAll(getTimeStr(parMap.get("date").toString(), time));
         }
         resultItem.put("stationName", parMap.get("staName"));
         resultItem.put("stationType", parMap.get("transfer_info"));
         list.add(resultItem);
      }
      return list;
   }

   /**
    * 获取日颗粒度车站分时进站量、出站量、换乘量、客运量数据接口
    */
   private List<Map<String, Object>> queryDayStaData(Map<String, Object> params){
      return zfUtils.toListMap(dao.query("KFZF01.queryStaDayData", params));
   }

   /**
    * @param inInfo
    * @return 车站客流综合数据 表t_acc_target_sta数据
    * 入参示例：{"lineNumber": "0100000000","stationNumber": "010000000001","date": "2023-06-14","intervalT": "410001","startTime": "04:50","endTime": "05:50"}
    * @description 车站客流综合数据
    */
   public EiInfo getStationPFInfo(EiInfo inInfo) {
      EiInfo outInfo = new EiInfo();
      Map<String, Object> parMap = getQueryParam(inInfo);
      //查询车站客流数据
      List<Map<String, Object>> stationPFInfo = EplatService.queryStsDatabase("D_NOCC_PFA_KF01", parMap, "999999");

      //查询用于计算累计的数据 =》 4点到4点则查日粒度，否则查5分钟累加
      int[] sumData;
      //hxj:根据时间轴筛选出规整的数据，过滤掉如05:10-05:40等不规整的数据
      stationPFInfo = filterDataByTimes(stationPFInfo, times);
      if (Convert.toBool(parMap.get("isAllDay"))){
         parMap.put("intervalT", 410005);
         parMap.put("startTime", parMap.get("date"));
         //查询数据并将同一日期的数据累加起来（因为为单车站查询，且sql已去重，所以同日期都为该车站数据，只是不同车站号）
         sumData = zfUtils.getListTypeSums(queryDayStaData(parMap));
      }else {
         List<Map<String, Object>> station5minData = new ArrayList<>(stationPFInfo);
         sumData = zfUtils.getListTypeSums(station5minData);
      }
      Map<String, Map<String, Object>> stationPFMap = zfUtils.sumStaData(stationPFInfo);
      int stationType = Convert.toInt(parMap.get("transfer_info"));
      //返回车站客流峰值客流数据
      List<List<Map<String, Object>>> containTransPeakData = new ArrayList<>();
      containTransPeakData.add(0, tranPeakTimeData(handleStationPFInfo(stationPFMap, parMap)));
      containTransPeakData.add(todayTotal(sumData, stationType));
      outInfo.set("containTransPeakData", containTransPeakData);

      outInfo.set("times", this.times);

      // 返回车站客流对应时间轴对应数据
      Map<String, List<Map<String, Object>>> timeAxisData = handleStationSortInfo(stationPFMap, parMap);
      outInfo.set("timeAxisData", timeAxisData);

      //趋势图数据
      Map<String, List<Map<String, Object>>> trendData = handleTrendInfo(stationPFMap, parMap);
      outInfo.set("trendData", trendData);

      //返回车站客流列表数据
      outInfo.set("listData", listData(stationPFMap, parMap, sumData, parMap.get("allPeriodOfTime")));

      return outInfo;
   }

   /**
    * 处理数据获取不同指标数据集合
    */
   private Map<String, List<Map<String, Object>>> handleStationSortInfo(Map<String, Map<String, Object>> stationPFMap, Map<String, Object> parMap) {
      Map<String, List<Map<String, Object>>> timeAxisData = new HashMap<>();
      List<Map<String, Object>> inData = new ArrayList<>();
      List<Map<String, Object>> outData = new ArrayList<>();
      List<Map<String, Object>> transData = new ArrayList<>();
      List<Map<String, Object>> rsData = new ArrayList<>();
      for (Map.Entry<String, Map<String, Object>> entry : stationPFMap.entrySet()) {
         Map<String, Object> itemDataMap = entry.getValue();
         String time = entry.getKey();

         Map<String, Object> inMap = new HashMap<>();
         inMap.put("time", time);
         inMap.put("value",  Convert.toInt(itemDataMap.get("in"), 0));
         inData.add(inMap);

         Map<String, Object> outMap = new HashMap<>();
         outMap.put("time", time);
         outMap.put("value",  Convert.toInt(itemDataMap.get("out"), 0));
         outData.add(outMap);

         Map<String, Object> transMap = new HashMap<>();
         transMap.put("time", time);
         transMap.put("value",  Convert.toInt(itemDataMap.get("trans"), 0));
         transData.add(transMap);

         Map<String, Object> rsMap = new HashMap<>();
         rsMap.put("time", time);
         rsMap.put("value",  Convert.toInt(itemDataMap.get("rs"), 0));
         rsData.add(rsMap);

      }
      timeAxisData.put("countIn", inData);
      timeAxisData.put("countOut", outData);
      int stationType = Convert.toInt(parMap.get("transfer_info"));
      if (stationType != 0) {
         timeAxisData.put("countTrans", transData);
      }
      timeAxisData.put("countRs", rsData);
      return timeAxisData;
   }

   /**
    * 处理数据获取不同指标数据集合
    */
   private Map<String, List<Map<String, Object>>> handleTrendInfo(Map<String, Map<String, Object>> stationPFMap, Map<String, Object> parMap) {
      Map<String, List<Map<String, Object>>> timeAxisData = new HashMap<>();
      List<Map<String, Object>> inData = new ArrayList<>();
      List<Map<String, Object>> outData = new ArrayList<>();
      List<Map<String, Object>> transData = new ArrayList<>();
      List<Map<String, Object>> rsData = new ArrayList<>();
      for (String time : times) {
         Map<String, Object> itemDataMap = Optional.ofNullable(stationPFMap.get(time)).orElse(new HashMap<>());

         Map<String, Object> inMap = new HashMap<>();
         inMap.put("time", time);
         inMap.put("value",  Convert.toInt(itemDataMap.get("in"), 0));
         inData.add(inMap);

         Map<String, Object> outMap = new HashMap<>();
         outMap.put("time", time);
         outMap.put("value",  Convert.toInt(itemDataMap.get("out"), 0));
         outData.add(outMap);

         Map<String, Object> transMap = new HashMap<>();
         transMap.put("time", time);
         transMap.put("value",  Convert.toInt(itemDataMap.get("trans"), 0));
         transData.add(transMap);

         Map<String, Object> rsMap = new HashMap<>();
         rsMap.put("time", time);
         rsMap.put("value",  Convert.toInt(itemDataMap.get("rs"), 0));
         rsData.add(rsMap);

      }
      timeAxisData.put("countIn", inData);
      timeAxisData.put("countOut", outData);
      int stationType = Convert.toInt(parMap.get("transfer_info"));
      if (stationType != 0) {
         timeAxisData.put("countTrans", transData);
      }
      timeAxisData.put("countRs", rsData);
      return timeAxisData;
   }
   /**
    * 车站客流综合数据列表展示数据格式处理
    */
   public List<Map<String, Object>> listData(Map<String, Map<String, Object>> stationPFMap, Map<String, Object> parMap, int[] sumData, Object allPeriodOfTime) {
      List<Map<String, Object>> listData = new ArrayList<>();
      int transfer_info = Convert.toInt(parMap.get("transfer_info"));
      for (Map.Entry<String, Map<String, Object>> entry : stationPFMap.entrySet()) {
         Map<String, Object> itemDataMap = entry.getValue();
         Map<String, Object> item = new HashMap<>();
         item.put("date", parMap.get("date"));
         item.put("periodOfTime", entry.getKey());
         item.put("station", parMap.get("staName"));
         item.put("inFlow", getValue(itemDataMap, "in"));
         item.put("outFlow", getValue(itemDataMap, "out"));
         item.put("transFlow", getValue(itemDataMap, "trans"));
         item.put("flow", getValue(itemDataMap, "rs"));
         listData.add(item);
      }
      Map<String, Object> sumItem = new HashMap<>();
      sumItem.put("amount", "合计");
      sumItem.put("date", parMap.get("date"));
      sumItem.put("station", parMap.get("staName"));
      sumItem.put("periodOfTime", allPeriodOfTime);
      sumItem.put("inFlow", sumData[0]);
      sumItem.put("outFlow", sumData[1]);
      sumItem.put("transFlow", sumData[2]);
      sumItem.put("flow", sumData[3]);
      listData.add(0, sumItem);
      return listData;
   }
   /**
    * 获取值，若此值不存在则返回默认"-"
    */
   private String getValue(Map<String, Object> map, String key){
     return zfUtils.getItemValue(map, key);
   }

   /**
    * 根据时间轴筛选出规整的数据，过滤掉如05:10-05:40等不规整的数据
    */
   private List<Map<String, Object>> filterDataByTimes(List<Map<String, Object>> list, List<String> times){
      List<Map<String, Object>> result = new ArrayList<>();
      for (Map<String, Object> item : list) {
         if (times.contains(zfUtils.getPeriodOfTime(item))) {
            result.add(item);
         }
      }
      return result;
   }
   /**
    * @param inInfo
    * @description 换乘客流查询
    * 入参示例：{"lineNumber": "0100000000","stationNumber": "010000000001","date": "2023-06-14","intervalT": "410001","startTime": "04:50","endTime": "05:50"}
    */
   public EiInfo getChangePFInfo(EiInfo inInfo) {
      EiInfo outInfo = new EiInfo();
      //查询车站基础信息
      Map<String, Object> parMap = getQueryParm(inInfo);
      List<Map<String, Object>> changePFInfo = EplatService.queryStsDatabase("D_NOCC_PFA_ZF0302", parMap, "999999");
      /*
      if (410001 == intervalT){
         changeSumData = new ArrayList<>(changePFInfo);
      }else {
         parMap.put("intervalT", 410001);
         changeSumData = EplatService.queryStsDatabase("D_NOCC_PFA_ZF0302", parMap, "999999");
         parMap.put("intervalT", intervalT);
      }
      */
      //根据时间轴筛选出规整的数据，过滤掉如05:10-05:40等不规整的数据
      changePFInfo = filterDataByTimes(changePFInfo, times);
      //计算累计数据，从查询数据拷贝
      changeSumData.clear();
      changeSumData = new ArrayList<>();
      for (Map<String, Object> map : changePFInfo) {
         Map<String, Object> copiedMap = new HashMap<>(map);
         changeSumData.add(copiedMap);
      }
      List<Map<String, String>> sectionInfo = BaseDataUtils.querySection(new EiInfo()).getBlock("result").getRows();
      //换乘方向信息map{"车站id-车站id":"线路名+上下行方向”}
      Map<String, String> reMap = new HashMap<>();
      for (Map<String, String> map : sectionInfo) {
         reMap.put(map.get("start_sta_id")
               + "-"
               + map.get("end_sta_id"),
                 map.get("line_cname")
               + ("UP".equals(map.get("direction")) ? "上行" : "下行"));
      }
      this.baseTPFInfo = handleChangeData(changePFInfo, reMap);
      handleChangeData(changeSumData, reMap);

      //返回车站客流时间轴
      outInfo.set("times", this.times);
      //返回处理后的峰值换乘数据
      Map<String, Object> tptData = transferPeakTimeData(this.baseTPFInfo);
      outInfo.set("tptData", tptData);

      //返回处理后的换乘趋势数据
      Map<String, Object> ttaData = transferTimeAxisData(this.baseTPFInfo);
      outInfo.set("ttaData", ttaData);

      //返回处理后的换乘排序表数据
      Map<String, Object> sortData = transferSortData(this.baseTPFInfo);
      outInfo.set("sortData", sortData);

      //返回处理后的换乘数据列表
      List<Map<String, Object>> tflData = transferListData(this.baseTPFInfo, changeSumData, parMap);
      outInfo.set("tflData", tflData);

      return outInfo;
   }

   /**
    * 换乘查询数据填充换乘方向信息
    */
   private List<Map<String, Object>> handleChangeData(List<Map<String, Object>> data, Map<String, String> reMap){
      for (Map<String, Object> map : data) {
         String dirTrans = Convert.toStr(map.get("dt"),"");
         String[] dirTranArr = dirTrans.split("\\*");
         map.put("dirTran", reMap.get(dirTranArr[0]) + "-" + reMap.get(dirTranArr[1]));
         String before = reMap.get(dirTranArr[0]);
         String after = reMap.get(dirTranArr[1]);
         map.put("beforeLineCName", before.substring(0, before.length() - 2));
         map.put("afterLineCName", after.substring(0, before.length() - 2));
         String dateTime = dataTime(Convert.toStr(map.get("start"),""), Convert.toStr(map.get("end"),""),
                 "YYYY-MM-dd HH:mm", "HH:mm");
         map.put("timeItem", dateTime);
      }
      return data;
   }

   /**
    * 换乘客流分析峰值时段格式处理
    * 返回示例
    * {
    * "lineDors":[xx线-xx线,xx线-xx线],
    * "data":[
    * {"peakTime": "(07:15-07:20)","countTrans": 49,"peakName": "xx线下行-xx线上行"},
    * {"peakTime": "(07:20-07:25)","countTrans": 50,"peakName": "xx线上行-xx线上行"}
    * ]
    * }
    */
   public Map<String, Object> transferPeakTimeData(List<Map<String, Object>> baseTPFInfo) {
      Map<String, Object> transferPeakTimeData = new HashMap<>();
      //处理不同方向峰值数据{"peakTime": "(06:50-06:55)","countTrans": 55,"peakName": "2号线下行-1号线上行"}
      List<List<Map<String, Object>>> data = new ArrayList<>();
      ArrayList<String> lineDirs = new ArrayList<>();
      //hxj:
      //先将数据根据进出线路分组：
      Map<String, List<Map<String, Object>>> lineGroupsData = baseTPFInfo.stream().collect(
              Collectors.groupingBy(e -> e.get("beforeLineCName") + "-" + e.get("afterLineCName")));
      for (String lineKey : lineGroupsData.keySet()){
         List<Map<String, Object>> dataItem = new ArrayList<>();
         //再将数据根据方向分组，随后遍历每组选出最大换乘量时段
         Map<String, List<Map<String, Object>>> dirGroupsData = lineGroupsData.get(lineKey)
                 .stream().collect(Collectors.groupingBy(e -> Convert.toStr(e.get("dirTran"))));
         for (List<Map<String, Object>> oneGroupData : dirGroupsData.values()){
            Map<String, Object> maxItem = zfUtils.getPeakMap(oneGroupData, "ct");
//            oneGroupData.stream().max(
//                    Comparator.comparingInt(e -> Convert.toInt(e.get("ct"), 0))).orElse(new HashMap<>());
            Map<String, Object> item = new HashMap<>();
            item.put("peakName", maxItem.get("dirTran"));
            item.put("peakTime", zfUtils.getPeriodOfTime(maxItem));
            item.put("peakValue", Convert.toInt(maxItem.get("ct"), 0));
            dataItem.add(item);
         }
         lineDirs.add(lineKey);
         data.add(dataItem);
      }
      transferPeakTimeData.put("lineDirs", lineDirs);
      transferPeakTimeData.put("data", data);
      return transferPeakTimeData;
   }

   /**
    * 换乘客流分析折线图数据格式处理
    */
   private Map<String, Object> transferTimeAxisData(List<Map<String, Object>> baseTPFInfo) {
      //将数据根据方向分组，随后遍历时间轴，根据时间轴获取数据
      List<Object> trendData = new ArrayList<>();
      List<String> names = new ArrayList<>();
      Map<String, List<Map<String, Object>>> dirGroupsData = baseTPFInfo.stream().collect(Collectors.groupingBy(e -> Convert.toStr(e.get("dirTran"))));
      for (String dir : dirGroupsData.keySet()){
         names.add(dir);
         Map<String, Object> oneDirResult = new HashMap<>();
         oneDirResult.put("name", dir);
         List<Map<String, Object>> oneDirDataResult = new ArrayList<>();
         //查询的数据转成Map
         Map<String, Map<String, Object>> oneDirData = dirGroupsData.get(dir).stream().collect(Collectors.toMap(
                 zfUtils::getPeriodOfTime,
                 e -> e,
                 (v1, v2) -> v1
         ));
         for (String time : times){
            Map<String, Object> item = Optional.ofNullable(oneDirData.get(time)).orElse(new HashMap<>());
            Map<String, Object> map = new HashMap<>();
            map.put("time", time);
            map.put("type", dir);
            map.put("value", Convert.toInt(item.get("ct"), 0));
            oneDirDataResult.add(map);
         }
         oneDirResult.put("data", oneDirDataResult);
         trendData.add(oneDirResult);
      }
      Map<String, Object> transferTimeAxisData = new HashMap<>();
      transferTimeAxisData.put("data", trendData);
      transferTimeAxisData.put("names", names);
      return transferTimeAxisData;
   }

   /**
    * 换乘客流分析折线图数据格式处理
    */
   private Map<String, Object> transferSortData(List<Map<String, Object>> baseTPFInfo) {
      List<Object> sortData = new ArrayList<>();
      sortData.add(transferRankData(baseTPFInfo));
      List<String> names = new ArrayList<>();
      names.add("全部方向");
      Map<String, List<Map<String, Object>>> dirGroupsData = baseTPFInfo.stream().collect(Collectors.groupingBy(e -> Convert.toStr(e.get("dirTran"))));
      for (String dir : dirGroupsData.keySet()){
         names.add(dir);
         Map<String, Object> oneDirResult = new HashMap<>();
         oneDirResult.put("name", dir);
         List<Map<String, Object>> oneDirDataResult = new ArrayList<>();
         //查询的数据转成Map
         Map<String, Map<String, Object>> oneDirData = dirGroupsData.get(dir).stream().collect(Collectors.toMap(
                 zfUtils::getPeriodOfTime,
                 e -> e,
                 (v1, v2) -> v1
         ));
         for (Map.Entry<String, Map<String, Object>> entry : oneDirData.entrySet()){
            Map<String, Object> map = new HashMap<>();
            map.put("time", entry.getKey());
            map.put("type", dir);
            map.put("value", Convert.toInt(entry.getValue().get("ct"), 0));
            oneDirDataResult.add(map);
         }
         oneDirResult.put("data", oneDirDataResult);
         sortData.add(oneDirResult);
      }
      Map<String, Object> transferTimeAxisData = new HashMap<>();
      transferTimeAxisData.put("names", names);

      transferTimeAxisData.put("sortData", sortData);
      return transferTimeAxisData;
   }

   /**
    * 换乘客流分析排名情况数据格式处理(全部方向)
    */
   public Map<String, Object> transferRankData(List<Map<String, Object>> baseTPFInfo) {
      Map<String, Object> request = new HashMap<>();
      List<Map<String, Object>> data = new ArrayList<>();
      for (Map<String, Object> map : baseTPFInfo) {
         Map<String, Object> allDirDatas = new HashMap<>();
         String dateTime = dataTime(
               Convert.toStr(map.get("start"),""),
               Convert.toStr(map.get("end"),""),
               "yyyy-MM-dd HH:mm",
               "HH:mm");
         allDirDatas.put("time", dateTime);
         allDirDatas.put("type", map.get("dirTran"));
         allDirDatas.put("value", map.get("ct"));
         data.add(allDirDatas);
      }
      request.put("name", "全部方向");
      request.put("data", data);
      return request;
   }

   /**
    * 换乘客流分析列表数据格式处理
    */
   public List<Map<String, Object>> transferListData(List<Map<String, Object>> basePFInfo, List<Map<String, Object>> changeSumData, Map<String, Object> parMap) {
      Map<String, String> stationMap = new HashMap<>();
      for (Map<String, Object> item : this.stationInfo) {
         stationMap.put(Convert.toStr(item.get("sta_id"),""), Convert.toStr(item.get("sta_cname"),""));
      }
      List<Map<String, Object>> timeTransferListData = new ArrayList<>();
      Object date = parMap.get("date");
      Object station = stationMap.get(Convert.toStr(parMap.get("stationNumber"),""));
      //数据分组排序 =》 便于输出展示
      //由于Collectors.groupingBy使用HashMap实现，无法保留顺序，所以如下：
      // 使用Collectors.toMap()进行分组并指定LinkedHashMap保留顺序
      Map<String, List<Map<String, Object>>> sortTableData = zfUtils.dataGroupingLinkedHashMap(basePFInfo, "dirTran");
      List<String> names = new ArrayList<>(sortTableData.keySet());
      Collections.sort(names);
      //时段
      for (String name : names) {
         List<Map<String, Object>> oneList = sortTableData.get(name);
         for (Map<String, Object> item : oneList) {
            Map<String, Object> map = new HashMap<>(16);
            String startTime = Convert.toStr(item.get("start"),"");
            String endTime = Convert.toStr(item.get("end"),"");
            String transInDir = name.substring(name.indexOf("-") - 2, name.indexOf("-"));
            String transOutDir = name.substring(name.length() - 2);
            map.put("date", date);
            String pt = DataProcessUtils.isNextDay(startTime, endTime);
            map.put("periodOfTime", pt);
            map.put("transInLine", item.get("beforeLineCName"));
            map.put("transInDir", transInDir);
            map.put("station", station);
            map.put("transOutLine", item.get("afterLineCName"));
            map.put("transOutDir", transOutDir);
            map.put("transFlow", Convert.toStr(item.get("ct"), "0"));
            timeTransferListData.add(map);
         }
      }
      //合计
      List<Map<String, Object>> allTransferListData = new ArrayList<>();
      for (String direction : names) {
         float count = 0;
         Map<String, Object> map = new HashMap<>();
         for (Map<String, Object> item : changeSumData) {
            if (Convert.toStr(item.get("dirTran"),"").equals(direction)) {
               count +=  Convert.toFloat(item.get("ct"), 0f);
            }
         }
         map.put("amount", "合计");
         map.put("date", parMap.get("date"));
         map.put("periodOfTime", changePeriodOfTime);
         map.put("station", station);
         map.put("transInLine", direction.substring(0, 3));
         map.put("transInDir", direction.substring(3, 5));
         map.put("transOutLine", direction.substring(6, 9));
         map.put("transOutDir", direction.substring(9));
         map.put("transFlow", Math.round(count));
         allTransferListData.add( map);
      }
      allTransferListData.addAll(timeTransferListData);
      return allTransferListData;
   }

   /**
    * 日期格式转换
    *
    * @return
    */
   public String dataTime(String startTime, String endTime, String inFormat, String outFormat) {
      SimpleDateFormat insdf = new SimpleDateFormat(inFormat);
      SimpleDateFormat outsdf = new SimpleDateFormat(outFormat);
      String outTime = "";
      try {
         startTime = outsdf.format(insdf.parse(startTime));
         endTime = outsdf.format(insdf.parse(endTime));
         outTime = startTime + "-" + endTime;
      } catch (Exception e) {
         System.err.println("日期格式转换异常");
      }
      return outTime;
   }

   /**
    * 客流综合分析峰值时段数据格式处理
    */
   public List<Map<String, Object>> tranPeakTimeData(List<Map<String, Object>> basePFInfo) {
      List<Map<String, Object>> list = new ArrayList<>();
      Integer max1 = -1, max2 = -1, max3 = -1, max4 = -1;
      String peakTime1 = "", peakTime2 = "", peakTime3 = "", peakTime4 = "";
      int countIn = 0, countOut = 0, countTrans = 0, countRs = 0;
      for (Map<String, Object> map : basePFInfo) {
         countIn = Convert.toInt(map.get("countIn"),0);
         countOut =Convert.toInt(map.get("countOut"),0);
         countRs = Convert.toInt(map.get("countRs"),0);
         if (max1 <= countIn) {
            max1 = countIn;
            try {
               String startTime = Convert.toStr(map.get("startTime"),"").substring(11, 16);
               String endTime = Convert.toStr(map.get("endTime"),"").substring(11, 16);
               peakTime1 = "(" + startTime + "-" + endTime + ")";
            }catch (Exception e){
               peakTime1 = "";
            }
         }
         if (max2 <= countOut) {
            max2 = countOut;
            try {
               String startTime = Convert.toStr(map.get("startTime"),"").substring(11, 16);
               String endTime = Convert.toStr(map.get("endTime"),"").substring(11, 16);
               peakTime2 = "(" + startTime + "-" + endTime + ")";
            }catch (Exception e){
               peakTime2 = "";
            }
         }
         if (Convert.toInt(basePFInfo.get(0).get("stationType")) != 0) {
            countTrans = Convert.toInt(map.get("countTrans"),0);
            if (max3 <= countTrans) {
               max3 = countTrans;
               try{
                  String startTime = Convert.toStr(map.get("startTime"),"").substring(11, 16);
                  String endTime = Convert.toStr(map.get("endTime"),"").substring(11, 16);
                  peakTime3 = "(" + startTime + "-" + endTime + ")";
               }catch(Exception e){
                  peakTime3="";
               }
            }
         }
         if (max4 <= countRs) {
            max4 = countRs;
            try {
               String startTime = Convert.toStr(map.get("startTime"),"").substring(11, 16);
               String endTime = Convert.toStr( map.get("endTime"),"").substring(11, 16);
               peakTime4 = "(" + startTime + "-" + endTime + ")";
            }catch(Exception e){
               peakTime4 = "";
            }
         }
      }
      HashMap<String, Object> map1 = new HashMap<>();
      map1.put("peakName", "进站量");
      map1.put("peakTime", peakTime1);
      map1.put("peakValue", max1);
      list.add(map1);
      HashMap<String, Object> map2 = new HashMap<>();
      map2.put("peakName", "出站量");
      map2.put("peakTime", peakTime2);
      map2.put("peakValue", max2);
      list.add(map2);
      if (Convert.toInt(basePFInfo.get(0).get("stationType")) != 0) {
         HashMap<String, Object> map3 = new HashMap<>();
         map3.put("peakName", "换乘量");
         map3.put("peakTime", peakTime3);
         map3.put("peakValue", max3);
         list.add(map3);
      }
      HashMap<String, Object> map4 = new HashMap<>();
      map4.put("peakName", "客运量");
      map4.put("peakTime", peakTime4);
      map4.put("peakValue", max4);
      list.add(map4);
      return list;
   }

   /**
    * 当日总量数据格式处理
    */
   public List<Map<String, Object>> todayTotal(int[] sumData, int stationType) {
      List<Map<String, Object>> list = new ArrayList<>();
      HashMap<String, Object> map1 = new HashMap<>();
      map1.put("peakName", "进站量");
      map1.put("peakValue", sumData[0]);
      list.add(map1);
      HashMap<String, Object> map2 = new HashMap<>();
      map2.put("peakName", "出站量");
      map2.put("peakValue", sumData[1]);
      list.add(map2);
      if (stationType != 0) {
         HashMap<String, Object> map3 = new HashMap<>();
         map3.put("peakName", "换乘量");
         map3.put("peakValue", sumData[2]);
         list.add(map3);
      }
      HashMap<String, Object> map4 = new HashMap<>();
      map4.put("peakName", "客运量");
      map4.put("peakValue", sumData[3]);
      list.add(map4);
      return list;
   }

   /**
    * 对应时间轴对应数据格式处理
    */
   public List<Map<String, Object>> timeAxisData(List<Map<String, Object>> stationPFInfo, String dataType) {
      List<Map<String, Object>> dataAxisData = new ArrayList<>();
      try {
         SimpleDateFormat insdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
         SimpleDateFormat outsdf = new SimpleDateFormat("HH:mm");
         for (Map<String, Object> map : stationPFInfo) {
            Map<String, Object> item = new HashMap<>();
            String startTime = outsdf.format(insdf.parse(Convert.toStr(map.get("startTime"),"")));
            String endTime = outsdf.format(insdf.parse(Convert.toStr(map.get("endTime"),"")));
            String time = startTime + "-" + endTime;
            int value = Math.round(Convert.toFloat(map.get(dataType), 0f));
            item.put("time", time);
            item.put("value", value);
            dataAxisData.add(item);
         }
      } catch (ParseException e) {
         throw new RuntimeException(e);
      }
      return dataAxisData;
   }
}
