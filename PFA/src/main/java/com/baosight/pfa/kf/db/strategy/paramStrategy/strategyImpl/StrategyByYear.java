package com.baosight.pfa.kf.db.strategy.paramStrategy.strategyImpl;

import cn.hutool.core.convert.Convert;
import com.baosight.pfa.kf.db.strategy.paramStrategy.ParamStrategy;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class StrategyByYear implements ParamStrategy {
   @Override
   public boolean isMatch(Map<String, Object> params) {
      return params.get("timeLatitude").equals("年");
   }

   @Override
   public Map<String, Map<String, Object>> getParams(Map<String, Object> params) {
      Map<String, Map<String, Object>> resultParam = new HashMap<>();
      String referenceStartTime = params.get("referenceDate").toString() + "-01-01";
      String referenceEndTime = params.get("referenceDate").toString() + "-12-31";
      String contrastStartTime = params.get("contrastDate").toString() + "-01-01";
      String contrastEndTime = params.get("contrastDate").toString() + "-12-31";
      //List<String> typeParams = (List<String>) params.get("typeParams");
      //String lineNumber = typeParams.get(0);
      //String stationNumber = typeParams.get(1);
      List<?> typeParams = Convert.toList(params.get("typeParams"));
      String lineNumber = typeParams.get(0).toString();
      List<?> stationNumber = Convert.toList(typeParams.get(1));
      LinkedHashMap<String, Object> rEmptyTimes = emptyTimes(referenceStartTime, referenceEndTime);
      LinkedHashMap<String, Object> cEmptyTimes = emptyTimes(contrastStartTime, contrastEndTime);
      Map<String, Object> referenceParam = new HashMap<>();
      Map<String, Object> contrastParam = new HashMap<>();
      //参考数据参数
      referenceParam.put("lineNumber", lineNumber);
      referenceParam.put("stationNumber", stationNumber);
      referenceParam.put("intervalT", 410007);
      referenceParam.put("timeLatitude", "年");
      referenceParam.put("startTime", referenceStartTime);
      referenceParam.put("endTime", referenceEndTime);
      referenceParam.put("emptyTimes", rEmptyTimes);
      //对比数据参数
      contrastParam.put("lineNumber", lineNumber);
      contrastParam.put("stationNumber", stationNumber);
      contrastParam.put("intervalT", 410007);
      contrastParam.put("timeLatitude", "年");
      contrastParam.put("startTime", contrastStartTime);
      contrastParam.put("endTime", contrastEndTime);
      contrastParam.put("emptyTimes", cEmptyTimes);
      //参数map
      resultParam.put("referenceParam", referenceParam);
      resultParam.put("contrastParam", contrastParam);
      return resultParam;
   }
   //生成用于填充空值的时间轴
   private LinkedHashMap<String,Object> emptyTimes(String startDate, String endDate){
      LinkedHashMap<String, Object> dates = new LinkedHashMap<>();
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
      LocalDate start = LocalDate.parse(startDate, formatter);
      LocalDate end = LocalDate.parse(endDate, formatter);
      while (!start.isAfter(end)) {
         dates.put(start.toString(),null);
         start = start.plusMonths(1);
      }
      return dates;
   }
   @Override
   public String strategyMessage() {
      return "以年为时间单位的参数处理策略";
   }
}
