package com.baosight.pfa.kf.od.service;

import cn.hutool.core.convert.Convert;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.BaseDataUtils;
import com.baosight.pfa.kf.common.EplatService;
import com.baosight.pfa.kf.common.zfUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA
 *
 * <AUTHOR>
 * @date 2023/12/22
 */
public class ServiceKFOD02 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * OD查询车站基础数据获取
     * serviceId: S_KF_0D_0100
     * @param info EiInfo
     * @return  EiInfo.stationBase
     */
    public EiInfo od01StationBase(EiInfo info){
        //初始化类别数据，里面装了线网类别
        List<Map<String, Object>> result = BaseDataUtils.initBaseTypeData();
        //车站基础数据,并根据line_id进行分组
        Map<String, List<Map<String, Object>>> stationBaseMap = BaseDataUtils.queryBaseStaGroupData();
        //按照stationBaseMap key（line_id）值进行排序，否则线路显示顺序混乱
        List<String> stationBaseMapKeys = new ArrayList<>(stationBaseMap.keySet());
        Collections.sort(stationBaseMapKeys);
        //将分组后的每条线路数据装在Map中，内含label、value与children数据
        for (String  stationBaseMapKey: stationBaseMapKeys){
            List<Map<String, Object>> oneLine = stationBaseMap.get(stationBaseMapKey);
            //一级目录内数据
            Map<String, Object> oneLineResult = new LinkedHashMap<>();
            oneLineResult.put("label", oneLine.get(0).get("line_cname"));
            oneLineResult.put("value", stationBaseMapKey);
            List<Map<String, Object>> children = new ArrayList<>();
            for (Map<String, Object> item : oneLine){
                //二级目录内数据:各个车站
                Map<String, Object> stationItem = new LinkedHashMap<>();
                stationItem.put("label", item.get("sta_cname"));
                stationItem.put("value", item.get("sta_id"));
                children.add(stationItem);
            }
            oneLineResult.put("children", children);
            result.add(oneLineResult);
        }

        info.set("data", result);
        return info;
    }

    /**
     * 获取线网客流对比数据
     * serviceId: S_KF_0D_0101
     * @param info EiInfo
     * @return EiInfo.data
     */
    public EiInfo od01Query(EiInfo info){
        //传入的参数集
        Map<?, ?> initParams = zfUtils.getParams(info);
        //sql查询参数集
        Map<String, Object> params = new HashMap<>(16);
        //解析车站类别参数信息，并根据传入的type判断是来源(O)站还是去向（D）站
        List<Map<String, Object>> stations = new ArrayList<>();
        List<?> initStations = Convert.toList(initParams.get("stations"));
        for (Object stationObj : initStations) {
            Map<String, Object> map = new HashMap<>(16);
            List<?> station = Convert.toList(stationObj);
            if ("net".equals(station.get(0))){
                break;
            }
            map.put("lineNumber", station.get(0));
            map.put("stationNumber", station.get(1));
            stations.add(map);
        }
        params.put("intervalT", 410004);
        String date = initParams.get("date").toString();
        params.put("partitionDate", Convert.toInt(date.replaceAll("-", "")));
        params.put("startTime", date + " 00:00:00");
        params.put("endTime", date + " 23:59:59");
        //起止时间
        String startTime = initParams.get("startTime").toString();
        String endTime = initParams.get("endTime").toString();
        List<String> times = zfUtils.getTimes(startTime, endTime, 410004, date, 1);
        //查询处理数据
        List<List<Map<String, Object>>> result = new ArrayList<>();
        params.put("endStations", stations);
        //timeType为时段类型，选择来源时，查询D站（end_sta）与出站时刻（fd_extend=1出站、2进站时段）
        long t1 = System.currentTimeMillis();
        params.put("timeType", "1");
        result.add(0, handleOneTypeData(params, date, times));
        params.remove("endStations");
        //beginStations
        params.put("timeType", "2");
        params.put("beginStations", stations);
        List<Map<String, Object>> type2Data = handleOneTypeData(params, date, times);
        result.add(1, type2Data);

        //计算各车站去向分线路时段汇总数据：先以O站+D线路+时段进行分组，最后计算汇总
        List<Map<String, Object>> sumResultData = new ArrayList<>();
        Map<String, List<Map<String, Object>>> groupData = type2Data.stream().collect(
                Collectors.groupingBy(e -> e.get("oStation") +","+ e.get("dLine") +","+ e.get("periodOfTime")));
        for (String groupKey : groupData.keySet()){
            String[] groupKeyInfo = groupKey.split(",");
            List<Map<String, Object>> oneGroupData = groupData.get(groupKey);
            Map<String, Object> sumResultItem = new HashMap<>(16);
            sumResultItem.put("date", date);
            sumResultItem.put("periodOfTime", groupKeyInfo[2]);
            sumResultItem.put("oLine", oneGroupData.size()>0 ? oneGroupData.get(0).get("oLine") : "-");
            sumResultItem.put("oStation", groupKeyInfo[0]);
            sumResultItem.put("dLine", groupKeyInfo[1]);
            float countSum = 0f;
            for (Map<String, Object> item : oneGroupData){
                countSum += Convert.toFloat(item.get("count"));
            }
            sumResultItem.put("count", Math.round(countSum));
            sumResultData.add(sumResultItem);
        }
        //根据客流量排降序，并增加index字段
        sumResultData.sort(Comparator.comparingInt(e -> Convert.toInt(((Map<?, ?>)e).get("count"), 0)).reversed());
        for (int i=0; i<sumResultData.size(); i++){
            sumResultData.get(i).put("index", i+1);
        }
        result.add(2, sumResultData);

        EiInfo outInfo = new EiInfo();
        outInfo.set("data", result);
        return outInfo;
    }

    /**
     * 处理单类型数据
     */
    public List<Map<String, Object>> handleOneTypeData( Map<String, Object> params, String date, List<String> times){
        List<Map<String, Object>> queryData = queryOdStation(params);
        queryData =  queryData.stream().filter(item -> times.contains(zfUtils.getPeriodOfTime(item))).collect(Collectors.toList());
        //获取处理过格式的车站基础数据
        Map<String, Map<String, Object>> baseStations = getBaseStationMap();
        //处理数据库查询到的数据
        List<Map<String, Object>> data = new ArrayList<>();
        for (Map<String, Object> item : queryData){
            Map<String, Object> map = new HashMap<>(16);
            //日期 与 时段
            map.put("date", date);
            map.put("periodOfTime", zfUtils.getPeriodOfTime(item));
            //O站 ：线路名、车站名
            String beginStation = Convert.toStr(item.get("beginstation"));
            Map<String, Object> oStaData = Optional.ofNullable(baseStations.get(beginStation)).orElse(new HashMap<>(16));
            map.put("oLine", Convert.toStr(oStaData.get("lineName")));
            map.put("oStation", Convert.toStr(oStaData.get("stationName")));
            //D站 ：线路名、车站名
            String endStation = Convert.toStr(item.get("endstation"));
            Map<String, Object> dStaData = Optional.ofNullable(baseStations.get(endStation)).orElse(new HashMap<>(16));
            map.put("dLine", Convert.toStr(dStaData.get("lineName")));
            map.put("dStation", Convert.toStr(dStaData.get("stationName")));
            map.put("count", Convert.toStr(item.get("count")));
            data.add(map);
        }
        //根据客流量排降序，并增加index字段
        data.sort(Comparator.comparingInt(e -> Convert.toInt(((Map<?, ?>)e).get("count"), 0)).reversed());
        for (int i=0; i<data.size(); i++){
            data.get(i).put("index", i+1);
        }
        return data;
    }


    //-------------------------------------数据获取：开始--------------------------------------------


    public List<Map<String, Object>> queryOdStation(Map<String, Object> params){
//        return EplatService.queryStsDatabase("D_NOCC_PFA_KF06", params, "999999");
        return dao.query("KFOD01.queryOdStation", params);
    }

    /**
     * 获取车站基础数据
     * @return Map<String, Map<String, Object>>
     */
    public Map<String, Map<String, Object>> getBaseStationMap(){
        //车站基础数据
        List<Map<String, Object>> sectionBase = BaseDataUtils.queryBaseData("S_BASE_DATA_03", new HashMap<>(16));
        return sectionBase.stream().collect(Collectors.toMap(
                item -> item.get("sta_id").toString(),
                item -> {
                    //线路名和线路号
                    item.put("lineName", item.get("line_cname"));
                    item.put("lineNumber", item.get("line_id"));
                    //车站名和车站号
                    item.put("stationName", item.get("sta_cname"));
                    item.put("stationNumber", item.get("sta_id"));
                    //判断是否为换乘站， 0否1是
                    int isTrans = Convert.toStr(item.get("transfer_info")).isEmpty() ? 0 : 1;
                    item.put("enable", item.get("enable_status"));
                    item.put("isTrans", isTrans);
                    return item;
                },
                (v1, v2) -> v1,
                // 指定使用LinkedHashMap保持车站的顺序
                LinkedHashMap::new
        ));
    }
    //-------------------------------------数据获取：结束--------------------------------------------

  /*
        //计算各车站去向分线路全日汇总数据：先以O站分组，再以D线路分组，最后计算汇总
        List<Map<String, Object>> sumResultData = new ArrayList<>();
        Map<String, List<Map<String, Object>>> oStaGroupData = type2Data.stream().collect(
                Collectors.groupingBy(e -> Convert.toStr(e.get("oStation"), "")));
        for (String oStation : oStaGroupData.keySet()){
            List<Map<String, Object>> oStaGroup = oStaGroupData.get(oStation);
            String oLine = oStaGroup.size()>0 ? oStaGroup.get(0).get("oLine").toString() : "-";
            //以D线路分组
            Map<String, List<Map<String, Object>>> oStaLineGroup = oStaGroup.stream().collect(
                    Collectors.groupingBy(e -> Convert.toStr(e.get("dLine"))));
            //遍历，汇总
            for (String dLine : oStaLineGroup.keySet()){
                List<Map<String, Object>> groupData = oStaLineGroup.get(dLine);
                Map<String, Object> sumResultItem = new HashMap<>(16);
                sumResultItem.put("date", date);
                sumResultItem.put("periodOfTime", startTime + "-"+ endTime);
                sumResultItem.put("oLine", oLine);
                sumResultItem.put("oStation", oStation);
                sumResultItem.put("dLine", dLine);
                float countSum = 0f;
                for (Map<String, Object> item : groupData){
                    countSum += Convert.toFloat(item.get("count"));
                }
                sumResultItem.put("sumCount", Math.round(countSum));
                sumResultData.add(sumResultItem);
            }
        }
         */

}
