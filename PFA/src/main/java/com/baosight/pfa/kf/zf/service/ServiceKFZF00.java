package com.baosight.pfa.kf.zf.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.pfa.kf.common.BaseDataUtils;

import java.util.*;

/**
 * 获取基础数据公用方法
 * <AUTHOR>
 * @date 2023/8/25 11:44
 */
public class ServiceKFZF00 extends ServiceBase{
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 创建一个map集合
     * @param id 组装所需的value
     * @param name 组装所需的label
     * @return Map：包含了两个key->value,label
     * <AUTHOR>
     */
    private static Map<String, String> createChildMap(String id, String name) {
        return new HashMap<String,String>(){{put("value",id); put("label",name);}};
    }

    /**
     * 根据线路编号或线路名查询线路基础数据(S_KF_ZF_0001)
     * @param info
     * lineId->线路号
     * lineCname->线路中文名
     * enableStatus->启用状态(默认true)
     * @return List<Map<String, String>>
     *     格式化后数据格式如下：
     *          [{"label": "1号线","value": "0100000000"}]
     * <AUTHOR>
     */
    public static List<Map<String, String>> getLines(EiInfo info){
        //获取线路基础数据
        EiInfo outInfo = BaseDataUtils.queryLine(info);
        //提取线路集合数据
        List<Map<String,String>> lineList = new ArrayList<Map<String,String>>();
        List<Map<String,String>> result = outInfo.getBlock("result").getRows();
        //开始数据格式化
        result.forEach(item->{
            Map<String,String> cmap = createChildMap(item.get("line_id"),item.get("line_cname"));
            lineList.add(cmap);
        });

        return lineList;
    }

    /**
     * 根据车站编号或车站名查询车站基础数据(S_KF_ZF_0002)
     * @param info
     * lineId->线路号
     * lineCname->线路中文名
     * staId->车站编号
     * @return List<Map<String, String>>
     *     格式化后数据格式如下：
     *      {"label": "1号线","value": "0100000000","children": [{"label": "屯里车辆段","value": "010006"}]}
     * <AUTHOR>
     */
    public static List<Map<String, Object>> getStations(EiInfo info){
        //获取车站基础数据
        EiInfo outInfo = BaseDataUtils.queryStation(info);
        List<Map<String,String>> result = outInfo.getBlock("result").getRows();

        // 格式化后的结果
        List<Map<String, Object>> formattedList = new ArrayList<>();
        //开始数据格式化
        for (Map<String, String> element : result) {
            String lineId = element.get("line_id");
            String lineName = element.get("line_cname");
            String staId = element.get("sta_id");
            String staName = element.get("sta_cname");
            //是否获取到线路
            boolean foundLine = false;
            for (Map<String, Object> line : formattedList) {
                if (line.get("value").equals(lineId)) {
                    List<Map<String, String>> children = (List<Map<String, String>>) line.get("children");
                    children.add(createChildMap(staId, staName));
                    foundLine = true;
                    break;
                }
            }

            if (!foundLine) {
                Map<String, Object> newLine = new HashMap<>();
                newLine.put("value", lineId);
                newLine.put("label", lineName);
                List<Map<String, String>> children = new ArrayList<>();
                children.add(createChildMap(staId, staName));
                newLine.put("children", children);
                formattedList.add(newLine);
            }
        }
        return formattedList;
    }

    private static Map<String, String> createSectionMap(String sectionId, String startStaCname, String endStaCname) {
        Map<String, String> map = new HashMap<>();
        map.put("value", sectionId);
        map.put("label", startStaCname + "-" + endStaCname);
        return map;
    }

    /**
     * 根据区间编号或区间名查询区间基础数据(S_KF_ZF_0003)
     * @param info
     * lineId->线路号
     * lineCname->线路中文名
     * direction->方向
     * sectionId->区间编号
     * @return List<Map<String, String>>
     *     格式化后数据格式如下：
     *          [{"children": [{"children": [{"label": "石埠站-南职院站","value": "010000000001XX"}],
                  "label": "1号线上行",
                  "value": "0100000000-UP"}
                ]
     * <AUTHOR>
     */
    public static List<Map<String, Object>> getSections(EiInfo info) {
        //获取断面基础数据
        EiInfo outInfo = BaseDataUtils.querySection(info);
        List<Map<String, String>> result = outInfo.getBlock("result").getRows();

        // 格式化后的结果
        List<Map<String, Object>> formattedList = new ArrayList<>();

        for (Map<String, String> element : result) {
            String lineId = element.get("line_id"); //线路编号
            String lineName = element.get("line_cname");//线路中文名
            String direction = element.get("direction");//方向UP：上行，DOWN：下行
            String sectionId = element.get("section_id");//断面编号
            String startStaCname = element.get("start_sta_cname");//起始位置
            String endStaCname = element.get("end_sta_cname");//结束位置
            String sectionAllNum = element.get("start_sta_id")+"-"+element.get("end_sta_id");

            // 拼接value值 拼接label值
            String value = lineId;
            String label = lineName;
            if ("UP".equals(direction)) {
                value += "-UP";
                label += "上行";
            } else if ("DOWN".equals(direction)) {
                value += "-DOWN";
                label += "下行";
            }

            boolean foundLine = false;
            for (Map<String, Object> line : formattedList) {
                if (line.get("value").equals(lineId)) {
                    List<Map<String, Object>> children = (List<Map<String, Object>>) line.get("children");
                    boolean foundDirection = false;

                    for (Map<String, Object> directionMap : children) {
                        if (directionMap.get("value").equals(value)) {
                            List<Map<String, String>> sections = (List<Map<String, String>>) directionMap.get("children");
                            sections.add(createSectionMap(sectionAllNum, startStaCname, endStaCname));
                            foundDirection = true;
                            break;
                        }
                    }

                    if (!foundDirection) {
                        Map<String, Object> newDirection = new HashMap<>();
                        newDirection.put("value", value);
                        newDirection.put("label", label);
                        List<Map<String, String>> sections = new ArrayList<>();
                        sections.add(createSectionMap(sectionAllNum, startStaCname, endStaCname));
                        newDirection.put("children", sections);
                        children.add(newDirection);
                    }

                    foundLine = true;
                    break;
                }
            }

            if (!foundLine) {
                Map<String, Object> newLine = new HashMap<>();
                newLine.put("value", lineId);
                newLine.put("label", lineName);
                List<Map<String, Object>> directions = new ArrayList<>();
                Map<String, Object> newDirection = new HashMap<>();
                newDirection.put("value", value);
                newDirection.put("label", label);
                List<Map<String, String>> sections = new ArrayList<>();
                sections.add(createSectionMap(sectionAllNum, startStaCname, endStaCname));
                newDirection.put("children", sections);
                directions.add(newDirection);
                newLine.put("children", directions);
                formattedList.add(newLine);
            }
        }

        for (Map<String, Object> line : formattedList) {
            List<Map<String, Object>> children = (List<Map<String, Object>>) line.get("children");
            if("0300000000".equals(line.get("value"))){
                Map<String, Object> map = children.get(0);
                List<Map<String, String>> mapList = (List<Map<String, String>>) map.get("children");
                Collections.reverse(mapList);
            }else{
                Map<String, Object> map = children.get(1);
                List<Map<String, String>> mapList = (List<Map<String, String>>) map.get("children");
                Collections.reverse(mapList);
            }
        }
        return formattedList;
    }

    /**
     * @description 获取线网-线路-车站基础数据(S_KF_ZF_0004)
     * @param info 获取ids:组键ID
     * @return 返回固定格式参数
     * <AUTHOR>
     * @date 2023/8/25
     */
    public static EiInfo queryAllDatas(EiInfo info){
        //获取格式化线路数据
        List<Map<String, String>> lines = getLines(info);
        //获取格式化车站数据
        List<Map<String, Object>> stations = getStations(info);
        //获取格式化断面数据
        List<Map<String, Object>> sections = getSections(info);
        //深度封装线路-车站-断面
        Map<String,Object> lineMap = new HashMap<String,Object>(){{
            put("value","line");
            put("label","线路");
            put("children",lines);
        }};
        Map<String,Object> staMap = new HashMap<String,Object>(){{
            put("value","station");
            put("label","车站");
            put("children",stations);
        }};
        Map<String,Object> sectMap = new HashMap<String,Object>(){{
            put("value","section");
            put("label","断面");
            put("children",sections);
        }};
        List<Map<String,Object>> resultList = new ArrayList<Map<String,Object>>(){{add(lineMap);add(staMap);add(sectMap);}};

        info.set("baseResult",resultList);
        return info;
    }

    /**
     * 封装车站(S_KF_ZF_0005)
     * @return 车站编号：车站中文名
     * <AUTHOR>
     * @date 2023/9/8
     */
    public Map<String,String> mapStation(EiInfo info){
        //获取车站基础数据
        EiInfo station = BaseDataUtils.queryStation(info);
        List<Map<String,String>> result = station.getBlock("result").getRows();
        Map<String,String> map = new HashMap<>();
        result.forEach(item->{
            String sta_id = item.get("sta_id");
            String sta_cname = item.get("sta_cname");
            map.put(sta_id,sta_cname);
        });
        return map;
    }

    /**
     * 封装区间(S_KF_ZF_0006)
     * @param info ->params 参数，防止和圆info的参数冲突，多封装一层
     * @return 起始车站编号-结束车站编号：车站中文名-车站中文名==方向
     * <AUTHOR>
     * @date 2023/9/8
     */
    public List<Map<String,String>> mapSection(EiInfo info){
        //获取断面基础数据
        EiInfo section = BaseDataUtils.querySection(info);
//        Map<String,String> map = new HashMap<>();
//        result.forEach(item->{
//            String sta_id = item.get("start_sta_id")+"-"+item.get("end_sta_id");
//            String sta_cname = item.get("start_sta_cname")+"-"+item.get("end_sta_cname");
//            String direction = "UP".equals(item.get("direction"))?"==上行":"==下行";
//            map.put(sta_id,sta_cname+direction);
//        });
        return section.getBlock("result").getRows();
    }

    /**
     * 获取换乘站数据
     * 返回参数格式同车站数据
     */
    public List<Map<String, Object>> getTranStation(EiInfo inInfo){
        //获取车站基础数据
        EiInfo outInfo = BaseDataUtils.queryStation(inInfo);
        List<Map<String,String>> result = outInfo.getBlock("result").getRows();

        // 格式化后的结果
        List<Map<String, Object>> formattedList = new ArrayList<>();
        //开始数据格式化
        for (Map<String, String> element : result) {
            if (element.get("transfer_info").isEmpty()) {
                continue;
            }
            String lineId = element.get("line_id");
            String lineName = element.get("line_cname");
            String staId = element.get("sta_id");
            String staName = element.get("sta_cname");
            //是否获取到线路
            boolean foundLine = false;
            for (Map<String, Object> line : formattedList) {
                if (line.get("value").equals(lineId)) {
                    List<Map<String, String>> children = (List<Map<String, String>>) line.get("children");
                    children.add(createChildMap(staId, staName));
                    foundLine = true;
                    break;
                }
            }

            if (!foundLine) {
                Map<String, Object> newLine = new HashMap<>();
                newLine.put("value", lineId);
                newLine.put("label", lineName);
                List<Map<String, String>> children = new ArrayList<>();
                children.add(createChildMap(staId, staName));
                newLine.put("children", children);
                formattedList.add(newLine);
            }
        }
        return formattedList;
    }

    /**
     * 获取换乘站数据(S_KF_ZF_0008)
     * 返回参数格式同车站数据
     */
    public List<Map<String,String>> getTranStations(EiInfo inInfo){
        List<Map<String,String>> resuList = new ArrayList<>();
        //获取车站基础数据
        EiInfo outInfo = BaseDataUtils.queryStation(inInfo);
        List<Map<String,String>> result = outInfo.getBlock("result").getRows();

        // 格式化后的结果
        List<Map<String, String>> formattedList = new ArrayList<>();
        //开始数据格式化
        for (Map<String, String> element : result) {
            if (!element.get("transfer_info").isEmpty()) {
                formattedList.add(element);
            }
        }
        int transSize = formattedList.size();
        Map<String,String> resuMap = new HashMap<>();
        Map<String,String> stationMap = new HashMap<>();
        for(int i=0;i<transSize-1;i++){
            StringBuilder sb = new StringBuilder();
            Map<String, String> map1 = formattedList.get(i);
            String staName = map1.get("sta_cname");
            String lineId = map1.get("line_id");
            String staId = map1.get("sta_id");
            sb.append(lineId).append("-").append(staId).append(",");
            stationMap.put(staId,staName);
            for(int j=i+1;j<transSize;j++){
                Map<String, String> map2 = formattedList.get(j);
                String staName2 = map2.get("sta_cname");
                if(staName.equals(staName2)){
                    String lineId2 = map2.get("line_id");
                    String staId2 = map2.get("sta_id");
                    sb.append(lineId2).append("-").append(staId2).append(",");
                }
            }
            if(!resuMap.containsKey(staName)){
                resuMap.put(staName,sb.toString());
            }
        }

        String staName = formattedList.get(transSize-1).get("sta_cname");
        String staId = formattedList.get(transSize-1).get("sta_id");
        stationMap.put(staId,staName);
        resuList.add(resuMap);
        resuList.add(stationMap);
        return resuList;
    }
}

