package com.baosight.pfa.kf.common;

import com.alibaba.fastjson.JSONArray;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.pfa.kf.common.util.file.FileUpload;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 日期工具类
 * <AUTHOR>
 * @date 2023/9/7 22:02
 */
public class TimeUtils {

    //num 表示数字对应的中文，lower表示小写，upper表示大写
    private static final String[] num_lower = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] unit_lower = {"", "十", "百", "千"};
    private static final String[] unit_common = {"", "万", "亿", "兆", "京", "垓", "秭", "穰", "沟", "涧", "正", "载"};

    public static String[] weekDatas(){
        return new String[]{"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
    }

    public static String[] monthDatas(){
        return new String[]{"1号","2号","3号","4号","5号","6号","7号","8号","9号","10号","11号","12号","13号","14号","15号","16号","17号","18号","19号","20号","21号","22号","23号","24号","25号","26号","27号","28号","29号","30号","31号"};
    }

    public static String[] yearDatas(){
        return new String[]{"1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"};
    }

    public static final List<String> sixtyHour = Arrays.asList("1999-02-04 04:00","1999-02-04 05:00","1999-02-04 06:00","1999-02-04 07:00","1999-02-04 08:00","1999-02-04 09:00","1999-02-04 10:00","1999-02-04 11:00","1999-02-04 12:00","1999-02-04 13:00","1999-02-04 14:00","1999-02-04 15:00","1999-02-04 16:00","1999-02-04 17:00","1999-02-04 18:00","1999-02-04 19:00","1999-02-04 20:00","1999-02-04 21:00","1999-02-04 22:00","1999-02-04 23:00","1999-02-05 00:00","1999-02-05 01:00","1999-02-05 02:00","1999-02-05 03:00","1999-02-05 04:00");

    public static String addZero(String str){
        return str.length()==1?("0"+str):str;
    }
    public static String addFourHour(String dayStr){
        return dayStr + " 04:00";
    }
    public static String addZeroHour(String dayStr){
        return dayStr + " 00:00";
    }

    /**
     * 判断是否是前一天
     * @param inputDate 判定日期
     * @param format 格式话类型
     * @return 日期
     */
    public static String ifBeforeDay(String inputDate,String format) {
        LocalDate date = LocalDate.parse(inputDate, DateTimeFormatter.ofPattern(format));
        LocalDate today = LocalDate.now();
        if (date.isBefore(today)) {
            LocalDate nextDay = date.plusDays(1);
            return nextDay.toString();
        }
        return today.format(DateTimeFormatter.ofPattern(format));
    }

    /**
     * 获得今日日期
     * @return
     */
    public static String todayYYYmmDD(){
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return today.format(formatter);
    }

    /**
     * 获得下一天日期
     */
    public static String getNextDay(String dateStr){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateStr, formatter);
        LocalDate nextDay = date.plusDays(1);
        return nextDay.format(formatter);
    }

    /**
     * 获得今日日期
     * @return
     */
    public static String todayYYYMMDDHHmmss(){
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        return now.format(formatter);
    }

    /**
     * 将2024-02-02  格式化未 20240202
     */
    public static String toyyyyMMdd(String str){
        return str.replace("-","");
    }

    /**
     * 判断是否是今年
     * @param dateStr 给定年字符串 “2023”
     * @return true:今年
     */
    public static boolean isNowYear(String dateStr){
        int value = Year.now().getValue();
        return dateStr.equals(String.valueOf(value));
    }


    /**
     * 给定一日期 获得今日开始时间，结束时间
     * @param timeStr 给定日期
     * @return ["2023-12-05 00:00","2023-12-06 00:00"]
     */
    public static String[] getTodayTimeArr(String timeStr){
        timeStr = timeStr.substring(0,10);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(timeStr, formatter);
        LocalDate nextDay = date.plusDays(1);
        String todayStr = addZeroHour(timeStr);
        String nextStr = addZeroHour(nextDay.toString());
        return new String[]{todayStr,nextStr};
    }

    /**
     * 给定日期，获取前一天
     * @param dateStr 给定日期yyyy-MM-dd
     * @return 2023-12-05 --> 2023-12-04
     */
    public static String getBeforDay(String dateStr){
        dateStr = subymd(dateStr);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateStr, formatter);
        LocalDate previousDate = date.minusDays(1);
        return previousDate.format(formatter);
    }

    /**
     * 根据每周开始时间得到每周结束时间日期
     * @param startTimeStr 每周开始时间
     * @param amount 增进的时间天
     * @return String 每周的结束时间
     * <AUTHOR>
     */
    public static String getSeventhDay(String startTimeStr,int amount){
        String seventhStr = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date startTime = sdf.parse(startTimeStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);
            // 获取第七天的日期
            calendar.add(Calendar.DATE, amount);
            Date seventhDay = calendar.getTime();
            seventhStr =  sdf.format(seventhDay);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return seventhStr;
    }

    /**
     * 根据年月，得到本月开始时间，结束时间
     * @param monthStr 月
     * @return String[] [每月开始时间，结束时间]
     * <AUTHOR>
     */
    public static String[] getMonthStarEnd(String monthStr){
        YearMonth yearMonth = YearMonth.parse(monthStr);
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        String[] strings = new String[2];
        strings[0]=String.valueOf(startDate);
        strings[1]=String.valueOf(endDate);
        return strings;
    }

    /**
     * 根据年，得到本年开始时间，结束时间
     * @param yearStr 年
     * @return String[] [每年开始时间，结束时间]
     * <AUTHOR>
     */
    public static String[] getYearMonth(String yearStr) {
        LocalDate currentDate = LocalDate.now();
        int month = currentDate.getMonth().getValue();
        String[] strings = new String[2];
        if(month == 1){
            int yearInt = Integer.parseInt(yearStr)-1;
            strings[0] = yearInt+"-01-01 00:00";
            strings[1] = yearStr+"-01-01 00:00";
        }else{
            strings[0] = yearStr+"-01-01 00:00";
            String strMonth = addZero(String.valueOf(month));
            strings[1] = yearStr+"-"+strMonth+"-01 00:00";
        }
        return strings;
    }

    /**
     * 根据年，得到本年开始时间，结束时间
     * @param yearStr 年
     * @return String[] [每年开始时间，结束时间]
     * <AUTHOR>
     */
    public static String[] getYearDay(String yearStr) {
        String[] strings = new String[2];
        strings[0] = yearStr+"-01-01 00:00";
        strings[1] = todayYYYmmDD()+" 00:00";
        return strings;
    }

    /**
     * 根据年，得到本年开始时间，结束时间
     * @param yearStr 年
     * @return String[] [每年开始时间，结束时间]
     * <AUTHOR>
     */
    public static String[] getYearStartEnd(String yearStr) {
        String[] strings = new String[2];
        strings[0] = yearStr+"-01-01 00:00";
        int nextYear = Integer.parseInt(yearStr) + 1;
        strings[1] = nextYear+"-01-01 00:00";
        return strings;
    }

    /**
     * 给定分钟，
     * @param timestr:当前的时间格式为‘yyyy-mm-dd’
     * @param minutes:需要步进的分钟数，例如240代表240分钟，也就是4小时
     * @return console.log(addMinutes('2023-9-12', 240)); 输出 '2023-9-12 04:00'
     * <AUTHOR>
     */
    public static String addMinutes(String timestr, int minutes) {
        // 将传入的时间字符串转换为Date对象
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = sdf.parse(timestr);
            // 将分钟数转换为小时数和分钟数
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MINUTE, minutes);

            // 创建一个新的Date对象，表示当前时间加上转换后的小时数和分钟数
            Date resultDate = calendar.getTime();
            // 格式化输出
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            return outputFormat.format(resultDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 给定分钟，
     * @param startTimeStr:当前的时间格式为‘yyyy-mm-dd’
     * @param amount 增进的时间天
     * @param minutes:需要步进的分钟数，例如240代表240分钟，也就是4小时
     * @return console.log(getScalDay('2023-9-12', 1,240)); 输出 '2023-9-13 04:00'
     * <AUTHOR>
     */
    public static String getScalDay(String startTimeStr,int amount,int minutes){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date startTime = sdf.parse(startTimeStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);
            // 获取第七天的日期
            calendar.add(Calendar.DATE, amount);
            calendar.add(Calendar.MINUTE, minutes);
            // 创建一个新的Date对象，表示当前时间加上转换后的小时数和分钟数
            Date resultDate = calendar.getTime();
            // 格式化输出
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            return outputFormat.format(resultDate);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String ymdHm(String str){
        SimpleDateFormat sfomat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            Date parse = sfomat.parse(str);
            str = sfomat.format(parse);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return str;
    }

    public static String subymdHm(String str){
        if(str.length()<16){
            return str;
        }
        return str.substring(0,16);
    }

    public static String subymd(String str){
        if(str.length()<10){
            return str;
        }
        return str.substring(0,10);
    }

    public static String mdHm(String str){
        if(str.length()<16){
            return str;
        }
        return str.substring(11,16);
    }

    public static String subTime(String str,int start,int end){
        if(str.length()<end){
            return str;
        }
        return str.substring(start,end);
    }

    public static String yyyyMMdd(String date){
        return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6);
    }
    /**
     * 给定开始时间，结束时间，步进分钟数，返回一组时间轴
     * @param stimeStr 开始时间
     * @param etimeStr 中止时间
     * @param minutes 步进分钟数
     * @return 一组时间轴
     */
    public static List<String> getTimeIntervals(String stimeStr, String etimeStr, int minutes) {
        List<String> timeList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        stimeStr = subymdHm(stimeStr);
        etimeStr = subymdHm(etimeStr);
        LocalDateTime stime = LocalDateTime.parse(stimeStr, formatter);
        LocalDateTime etime = LocalDateTime.parse(etimeStr, formatter);
        while (!stime.isAfter(etime)) {
            timeList.add(stime.toString().replace("T"," "));
            stime = stime.plusMinutes(minutes);
        }
        return timeList;
    }

    /**
     * 给定开始日期和结束日期 得到该时间段内天数
     * @param date1 开始时间
     * @param date2 结束时间
     * @return 2023-07-01 2023-07-07 --> 7
     */
    public static int getDayRange(String date1,String date2){
        LocalDate startDate = LocalDate.parse(date1);
        LocalDate endDate = LocalDate.parse(date2);
//        Period period = Period.between(startDate, endDate);
//        return period.getDays()+1;
        long daysDifference = ChronoUnit.DAYS.between(startDate, endDate);
        return (int) daysDifference + 1;
    }

    /**
     * 给定起始时间，结束时间，得到该时间段内所有日期
     * @param startDate 起始时间
     * @param endDate 结束时间
     * @return 时间集合
     */
    public static List<String> getDatesBetween(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        LocalDate currentDate = start;
        while (!currentDate.isAfter(end)) {
            dateList.add(currentDate.toString());
            currentDate = currentDate.plusDays(1);
        }
        return dateList;
    }

    /**
     * 给定起始时间，结束时间，得到该时间段内所有月
     * @param startDateString 起始时间
     * @param endDateString 结束时间
     * @return 时间集合
     */
    public static List<String> getMonthBetween(String startDateString, String endDateString) {
        String syear = startDateString.substring(0, 4);
        String eyear = endDateString.substring(0, 4);
        int num = Integer.parseInt(eyear) - Integer.parseInt(syear);
        int smonth = Integer.parseInt(startDateString.substring(5, 7));
        List<String> month = new ArrayList<>();
        if(num==0){
            int emonth = Integer.parseInt(endDateString.substring(5, 7));
            for(int i= smonth;i<=emonth;i++){
                String mon = syear +"-"+ addZero(String.valueOf(i));
                month.add(mon);
            }
        }else{
            for(int i= smonth;i<=12;i++){
                String mon = syear +"-"+ addZero(String.valueOf(i));
                month.add(mon);
            }
            month.add(endDateString.substring(0,7));
        }
        return month;
    }

    /**
     * 根据传入数字天数返回
     * @param n 数字
     * @return 2--> {"第1天","第2天"}
     */
    public static String[] getDays(int n) {
        String[] result = new String[n];
        for (int i = 0; i < n; i++) {
            result[i] = "第" + (i+1) + "天";
        }
        return result;
    }

    /**
     * 数字转化为小写的汉字
     * @param intnum 将要转化的数字
     * @return
     */
    public static String toChineseLower(int intnum) {
        String s = String.valueOf(intnum);
        return formatIntPart(s);
    }


    /**
     * 分割数字，每4位一组
     * @param num
     * @return
     */
    private static Integer[] split2IntArray(String num) {
        String prev = num.substring(0, num.length() % 4);
        String stuff = num.substring(num.length() % 4);
        if (!"".equals(prev)) {
            num = String.format("%04d", Integer.valueOf(prev)) + stuff;
        }
        Integer[] ints = new Integer[num.length() / 4];
        int idx = 0;
        for (int i = 0; i < num.length(); i += 4) {
            String n = num.substring(i, i + 4);
            ints[idx++] = Integer.valueOf(n);
        }
        return ints;
    }

    /**
     * 格式化4位整数
     * @param num
     * @param numArray
     * @return
     */
    private static String formatInt(int num, String[] numArray, String[] unit) {
        char[] val = String.valueOf(num).toCharArray();
        int len = val.length;
        StringBuilder sb = new StringBuilder();
        boolean isZero = false;
        for (int i = 0; i < len; i++) {
            //获取当前位的数值
            int n = Integer.parseInt(val[i] + "");
            if (n == 0) {
                isZero = true;
            } else {
                if (isZero) {
                    sb.append(numArray[Integer.parseInt(val[i - 1] + "")]);
                }
                sb.append(numArray[n]);
                sb.append(unit[(len - 1) - i]);
                isZero = false;
            }
        }
        return sb.toString();
    }

    /**
     * 格式化整数部分
     * @param num 整数部分
     * @return
     */
    private static String formatIntPart(String num) {
        //按4位分割成不同的组（不足四位的前面补0）
        Integer[] intnums = split2IntArray(num);
        boolean zero = false;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < intnums.length; i++) {
            //格式化当前4位
            String r = formatInt(intnums[i], TimeUtils.num_lower, TimeUtils.unit_lower);
            if ("".equals(r)) {//
                if ((i + 1) == intnums.length) {
                    sb.append(TimeUtils.num_lower[0]);//结果中追加“零”
                } else {
                    zero = true;
                }
            } else {//当前4位格式化结果不为空（即不为0）
                if (zero || (i > 0 && intnums[i] < 1000)) {//如果前4位为0，当前4位不为0
                    sb.append(TimeUtils.num_lower[0]);//结果中追加“零”
                }
                sb.append(r);
                sb.append(unit_common[intnums.length - 1 - i]);//在结果中添加权值
                zero = false;
            }
        }
        return sb.toString();
    }

    public static List<Map<String, Object>> toCamelCase(List<Map<String, Object>> list) {
        list.forEach(map ->
                new HashSet<>(map.keySet()).forEach(key -> {
                    if (key.contains("_")) {
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < key.length(); i++) {
                            char ch = key.charAt(i);
                            if (ch == '_'){
                                i++;
                                sb.append(Character.toUpperCase(key.charAt(i)));
                            }else{
                                sb.append(ch);
                            }
                        }
                        Object value = map.get(key);
                        map.remove(key);
                        map.put(sb.toString(), value);
                    }
                })
        );

        return list;
    }


    public static List<Map<String, Object>> toCamelCase2(List<Map<String, Object>> list) {
        // 转换 key 的形式
        for (Map<String, Object> map : list) {
            Set<String> keys = new HashSet<>(map.keySet());
            for (String key : keys) {
                if (key.contains("_")) {
                    String[] parts = key.split("_");
                    StringBuilder sb = new StringBuilder(parts[0]);
                    for (int i = 1; i < parts.length; i++) {
                        sb.append(Character.toUpperCase(parts[i].charAt(0)));
                        if (parts[i].length() > 1) {
                            sb.append(parts[i].substring(1));
                        }
                    }
                    Object value = map.get(key);
                    map.remove(key);
                    map.put(sb.toString(), value);
                }
            }
        }
        return list;
    }

    public static EiInfo callXService(EiInfo inInfo, String serviceId){
        inInfo.set(EiConstant.serviceId, serviceId);
        EiInfo outInfo = XServiceManager.call(inInfo);
        //注意必须对outInfo的status状态进行校验
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
        return outInfo;
    }

    /**
*         System.out.println(convertToMinutes("0")); // 输出 0
*         System.out.println(convertToMinutes("61")); // 输出 1
*         System.out.println(convertToMinutes("132")); // 输出 2
     */
    public static int convertToMinutes(String input) {
        int num = Integer.parseInt(input);
        if (num < 60) {
            return 0;
        } else {
            return (num - 60) / 60 + 1;
        }
    }


    public static List<String> getDateRange(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        List<String> dateRange = new ArrayList<>();
        while (!start.isAfter(end)) {
            dateRange.add(start.toString());
            start = start.plusDays(1);
        }

        return dateRange;
    }

    public static void main(String[] args) {
        List<String> between = getMonthBetween("2024-02-26", "2024-03-26");
        between.forEach(e-> System.out.println(e));

    }

}
