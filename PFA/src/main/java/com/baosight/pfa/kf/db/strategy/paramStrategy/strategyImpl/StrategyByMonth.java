package com.baosight.pfa.kf.db.strategy.paramStrategy.strategyImpl;

import cn.hutool.core.convert.Convert;
import com.baosight.pfa.kf.db.strategy.paramStrategy.ParamStrategy;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class StrategyByMonth implements ParamStrategy {
   @Override
   public boolean isMatch(Map<String, Object> params) {
      return params.get("timeLatitude").equals("月");
   }

   @Override
   public Map<String, Map<String, Object>> getParams(Map<String, Object> params) {
      Map<String, Map<String, Object>> resultParam = new HashMap<>();
      String referenceStartTime = params.get("referenceDate").toString() + "-01";
      String referenceEndTime = monthDays(params.get("referenceDate").toString());
      String contrastStartTime = params.get("contrastDate").toString() + "-01";
      String contrastEndTime = monthDays(params.get("contrastDate").toString());
      //List<String> typeParams = (List<String>) params.get("typeParams");
      //String lineNumber = typeParams.get(0);
      //String stationNumber = typeParams.get(1);
      List<?> typeParams = Convert.toList(params.get("typeParams"));
      String lineNumber = typeParams.get(0).toString();
      List<?> stationNumber = Convert.toList(typeParams.get(1));
      LinkedHashMap<String, Object> rEmptyTimes = emptyTimes(referenceStartTime, referenceEndTime);
      LinkedHashMap<String, Object> cEmptyTimes = emptyTimes(contrastStartTime, contrastEndTime);
      Map<String, Object> referenceParam = new HashMap<>();
      Map<String, Object> contrastParam = new HashMap<>();
      //参考数据参数
      referenceParam.put("lineNumber", lineNumber);
      referenceParam.put("stationNumber", stationNumber);
      referenceParam.put("intervalT", 410005);
      referenceParam.put("timeLatitude", "月");
      referenceParam.put("startTime", referenceStartTime);
      referenceParam.put("endTime", referenceEndTime);
      referenceParam.put("emptyTimes", rEmptyTimes);
      //对比数据参数
      contrastParam.put("lineNumber", lineNumber);
      contrastParam.put("stationNumber", stationNumber);
      contrastParam.put("intervalT", 410005);
      contrastParam.put("timeLatitude", "月");
      contrastParam.put("startTime", contrastStartTime);
      contrastParam.put("endTime", contrastEndTime);
      contrastParam.put("emptyTimes", cEmptyTimes);
      //返回参数Map
      resultParam.put("referenceParam", referenceParam);
      resultParam.put("contrastParam", contrastParam);
      return resultParam;
   }

   //判断每月结束日期
   private String monthDays(String date){
      String[] dateArr = date.split("-");
      LocalDate startOfMonth = LocalDate.of(Integer.parseInt(dateArr[0]), Integer.parseInt(dateArr[1]), 1);
      int daysInMonth = startOfMonth.lengthOfMonth();
      String resultDate  = dateArr[0] + "-" +dateArr[1]+"-"+ daysInMonth;
      return resultDate;
   }
   private LinkedHashMap<String,Object> emptyTimes(String startDate,String endDate){
      LinkedHashMap<String, Object> dates = new LinkedHashMap<>();
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
      LocalDate start = LocalDate.parse(startDate, formatter);
      LocalDate end = LocalDate.parse(endDate, formatter);
      while (!start.isAfter(end)) {
         dates.put(start.toString(),null);
         start = start.plusDays(1);
      }
      return dates;
   }

   @Override
   public String strategyMessage() {
      return "以月为时间单位的参数处理策略";
   }
}
