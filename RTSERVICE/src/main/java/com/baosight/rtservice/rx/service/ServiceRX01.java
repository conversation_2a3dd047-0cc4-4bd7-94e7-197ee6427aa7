package com.baosight.rtservice.rx.service;

import com.baosight.iplat4j.core.cache.CacheManager;
import com.baosight.iplat4j.core.cache.impl.RedisTempleteCache;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.base.RtConstant;
import com.baosight.rtservice.common.rx.constant.AuditFlag;
import com.baosight.rtservice.common.rx.domain.Publish;
import com.baosight.rtservice.common.utils.EiInfoUtil;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.ValidationUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class ServiceRX01 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceRX01.class);
    private EiInfo outInfo;

    private static final String CACHE_KEY = "irail:rtservice:rx:publishCache";
    private static final String REDIS_KEY_PREFIX = "RX01_UUIDS";
    public static final long REDIS_EXPIRE_TIME = 300000L;
    private final Map redisCache = CacheManager.getCache(CACHE_KEY, "redis");


    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 已批准发布
     *
     * @param inInfo { auditOper 审核人
     * @return EiInfo
     */
    public EiInfo approvedPublish(EiInfo inInfo) {
        try {
            Publish publish = JavaBeanUtil.mapToBean(inInfo.getAttr(), Publish.class);
            //参数校验{UUIDs,发布目标系统类型,发布人（当前审批人）,发布时间（审批时间）}
            String errorMsg = ValidationUtil.validateOne(publish);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            //检查该请求是否已经处理过
            if (checkProcessed(publish.getUUIDs())) {
                return EiInfoUtil.setError("[" + publish.getUUIDs() + "]" + "请求已经处理过，请勿重复操作"); // 返回成功响应，表示请求已处理
            }
            // 标记该请求已经被处理过
            markProcessed(publish.getUUIDs());

            //修改审核状态
            updateAuditStatus(inInfo);

            //调用内部发布接口
            processInnerPublish(inInfo);

            //发送全局通知
            sendGlobalNotification(inInfo);
            outInfo = Response.success();
        } catch (Exception exception) {
            redisCache.remove(REDIS_KEY_PREFIX + ":" + inInfo.get("UUIDs"));
            logger.error("approvedPublish Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
        }
        return outInfo;
    }

    private void updateAuditStatus(EiInfo inInfo) {
        //修改审核状态
        EiInfo ainInfo = inInfo;
        ainInfo.set(EiConstant.serviceName, "RX00");
        ainInfo.set(EiConstant.methodName, "updateAuditRecord");
        outInfo = XLocalManager.call(ainInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
    }

    private void processInnerPublish(EiInfo inInfo) {
        //调用内部发布接口
        EiInfo binInfo = inInfo;
        binInfo.set(EiConstant.serviceName, "RXXF01");
        binInfo.set(EiConstant.methodName, "innerPublish");
        outInfo = XLocalManager.call(binInfo);
        if (outInfo.getStatus() < 0) {
            throw new PlatException(outInfo.getMsg());
        }
    }

    private void sendGlobalNotification(EiInfo inInfo) {
        //发布失败发送全局通知
        EiInfo cinInfo = inInfo;
        if (AuditFlag.APPROVED == outInfo.getInt("auditFlag")) {
            cinInfo.set(RtConstant.rtMessageCode, outInfo.get((RtConstant.rtMessageCode)));
            cinInfo.set(EiConstant.serviceId, RtConstant.RN_NF_SERVICE_ID);
            outInfo = XServiceManager.call(cinInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
        }
    }


    private boolean checkProcessed(String UUIDs) {
        return redisCache.containsKey(REDIS_KEY_PREFIX + ":" + UUIDs);
    }

    private void markProcessed(String UUIDs) {
        if (redisCache instanceof RedisTempleteCache) {
            RedisTempleteCache redisTempleteCache = (RedisTempleteCache) redisCache;
            redisTempleteCache.setExpireTime(REDIS_EXPIRE_TIME);
            redisCache.put(REDIS_KEY_PREFIX + ":" + UUIDs, UUIDs);
        }

    }

}
