package com.baosight.rtservice.rx.xs.service;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.Response;
import com.baosight.rtservice.common.base.RtConstant;
import com.baosight.rtservice.common.rx.constant.AuditFlag;
import com.baosight.rtservice.common.rx.constant.PublishTarget;
import com.baosight.rtservice.common.rx.domain.Publish;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.ValidationUtil;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 审核驳回
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class ServiceRXXS03 extends ServiceBase {
    private static final Logger logger = LoggerFactory.getLogger(ServiceRXXS03.class);
    private EiInfo outInfo;

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * @param inInfo{ auditOper 审核人
     *                auditTime 审核时间
     *                UUIDs 唯一标识
     *                }
     * @return
     * @function 审核驳回
     */
    public EiInfo overrule(EiInfo inInfo) {
        try {
            //参数校验{UUIDs,发布方式,审批人,审批时间}
            Publish publish = JavaBeanUtil.mapToBean(inInfo.getAttr(), Publish.class);
            String errorMsg = ValidationUtil.validateOne(publish);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }

            EiInfo info = new EiInfo();
            info.set("UUIDs", inInfo.getString("UUIDs"));
            info.set(EiConstant.serviceName, "RX02");
            info.set(EiConstant.methodName, "queryStateToUid");
            outInfo = XLocalManager.call(info);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }
            int auditFlag = outInfo.getInt("auditFlag");
            if (auditFlag != 80002) {
                outInfo.setStatus(-1);
                outInfo.setMsg(String.valueOf(auditFlag));
                return outInfo;
            }

            //修改审核状态
            EiInfo ainInfo = inInfo;
            ainInfo.set("auditFlag", AuditFlag.OVERRULE);
            ainInfo.set(EiConstant.serviceName, "RX00");
            ainInfo.set(EiConstant.methodName, "updateAuditRecord");
            outInfo = XLocalManager.call(ainInfo);
            if (outInfo.getStatus() < 0) {
                throw new PlatException(outInfo.getMsg());
            }

            if (publish.isNotify()) {
                //全局通知
                EiInfo binInfo = inInfo;
                int publishTarge = binInfo.getInt("publishTarget");
                if (publishTarge == PublishTarget.PUBLISH_TARGET_PA) {
                    binInfo.set(RtConstant.rtMessageCode, RtConstant.RN_REJECTED_CODE_PA);
                } else if (publishTarge == PublishTarget.PUBLISH_TARGET_PCC) {
                    binInfo.set(RtConstant.rtMessageCode, RtConstant.RN_REJECTED_CODE_PCC);
                } else if (publishTarge == PublishTarget.PUBLISH_TARGET_APP ||
                        publishTarge == PublishTarget.PUBLISH_TARGET_TRAFFIC ||
                        publishTarge == PublishTarget.PUBLISH_TARGET_MICRO ||
                        publishTarge == PublishTarget.PUBLISH_TARGET_CELLPHONE) {
                    binInfo.set(RtConstant.rtMessageCode, RtConstant.RN_REJECTED_CODE_INFO);
                } else if (publishTarge == PublishTarget.PUBLISH_TARGET_FILE) {
                    binInfo.set(RtConstant.rtMessageCode, RtConstant.RN_REJECTED_CODE_FS);
                } else if (publishTarge == PublishTarget.PUBLISH_TARGET_ENTERPRISE_WECHAT) {
                    return Response.success();
                } else if (publishTarge == PublishTarget.PUBLISH_TARGET_CMP) {
                    binInfo.set(RtConstant.rtMessageCode, RtConstant.RN_REJECTED_CODE_CMP);
                } else {
                    throw new PlatException("发布类型未匹配成功,publishTarget:" + publishTarge);
                }
                //自定义通知接口中的options参数
                Map<String, Integer> mapPublishTarge = new HashMap();
                mapPublishTarge.put("publishTarget", publishTarge);
                Map<String, Object> mapParams = new HashMap();
                mapParams.put("params", mapPublishTarge);
                binInfo.set("options", mapParams);

                //全局通知服务ID
                binInfo.set(EiConstant.serviceId, RtConstant.RN_NF_SERVICE_ID);
                outInfo = XServiceManager.call(binInfo);
                if (outInfo.getStatus() < 0) {
                    throw new PlatException(outInfo.getMsg());
                }
            }

            outInfo = Response.success();
        } catch (Exception exception) {
            logger.error("overrule Exception：{}", exception.getMessage());
            outInfo = Response.error(exception.getMessage());
        }
        return outInfo;
    }
}
