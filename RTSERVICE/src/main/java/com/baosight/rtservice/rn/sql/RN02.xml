<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="RN02">

    <select id="query" resultClass="java.util.HashMap">
        SELECT message_code as "messageCode",
        message_source as "messageSource",
        message_title as "messageTitle",
        user_type as "userType",
        notify_user as "notifyUser",
        url as "url",
        width as "width",
        height as "height",
        offset as "offset",
        remark as "remark",
        rec_create_time as "recCreateTime"
        FROM ${platSchema}.rs_rn_popup_window
        where 1=1
        <isNotEmpty prepend="and" property="messageCode">
            message_code like '%$messageCode$%'
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                message_code asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM${platSchema}.rs_rn_popup_window WHERE 1=1
        <isNotEmpty prepend=" AND " property="messageCode">
            message_code = #messageCode#
        </isNotEmpty>
    </select>

    <select id="checkCount" resultClass="int">
        SELECT COUNT(*) FROM ${platSchema}.rs_rn_popup_window WHERE 1=1
        <isNotEmpty prepend=" AND " property="messageCode">
            message_code = #messageCode#
        </isNotEmpty>
    </select>

    <insert id="insert">
        INSERT INTO ${platSchema}.rs_rn_popup_window
        (message_code,
        message_title,
        user_type,
        notify_user,
        url,
        message_source
        <isNotEmpty prepend="," property="width">width</isNotEmpty>
        <isNotEmpty prepend="," property="height">height</isNotEmpty>
        <isNotEmpty prepend="," property="offset">offset</isNotEmpty>
        <isNotEmpty prepend="," property="remark">remark</isNotEmpty>
        <isNotEmpty prepend="," property="recCreateTime">rec_create_time</isNotEmpty>
        )
        VALUES(#messageCode:VARCHAR#,
        #messageTitle:VARCHAR#,
        #userType:VARCHAR#,
        #notifyUser#,
        #url#,
        #messageSource:VARCHAR#
        <isNotEmpty prepend="," property="width">#width#</isNotEmpty>
        <isNotEmpty prepend="," property="height">#height#</isNotEmpty>
        <isNotEmpty prepend="," property="offset">#offset#</isNotEmpty>
        <isNotEmpty prepend="," property="remark">#remark:VARCHAR#</isNotEmpty>
        <isNotEmpty prepend="," property="recCreateTime">#recCreateTime:VARCHAR#</isNotEmpty>
        );
    </insert>

    <delete id="delete">
        DELETE FROM ${platSchema}.rs_rn_popup_window
        WHERE
        message_code = #messageCode#
    </delete>

    <update id="update">
        UPDATE ${platSchema}.rs_rn_popup_window
        SET
        message_source = #messageSource#,
        message_title = #messageTitle#,
        user_type = #userType#,
        notify_user = #notifyUser#,
        url = #url# ,
        width = #width#,
        height = #height#,
        offset = #offset#,
        remark = #remark#
        WHERE
        message_code = #messageCode#
    </update>

    <select id="queryByCode" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        SELECT message_code as "messageCode",
        message_source as "messageSource",
        message_title as "messageTitle",
        user_type as "userType",
        notify_user as "notifyUser",
        url as "url",
        width as "width",
        height as "height",
        offset as "offset",
        remark as "remark",
        rec_create_time as "recCreateTime"
        FROM ${platSchema}.rs_rn_popup_window
        where message_code = #messageCode#
    </select>
</sqlMap>