package com.baosight.rtservice.rn.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.util.DateUtils;

import java.util.HashMap;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/10/12
 */

public class ServiceRN01 extends ServiceBase {

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        EiBlock resultBlock = inInfo.getBlock(EiConstant.resultBlock);
        int rowCount = resultBlock.getRowCount();

        int i;
        String messageCode;
        for (i = 0; i < rowCount; ++i) {
            messageCode = inInfo.getBlock("result").getCellStr(i, "messageCode");
            if (!this.check(messageCode)) {
                inInfo.setMsgByKey("ep.0002", new String[]{String.valueOf(i + 1), "新增", "无法添加记录，该消息标识已经存在!"});
                return inInfo;
            }

            resultBlock.getRow(i).values().removeIf(value -> value.equals(" "));
            resultBlock.getRow(i).put("recCreateTime", DateUtils.curDateTimeStr14());
        }
        super.insert(inInfo, "RN01.insert");
        inInfo.setMsgByKey("ep.1000", new String[]{String.valueOf(i), "新增"});

        return inInfo;
    }

    @Override
    public EiInfo delete(EiInfo inInfo) {
        return super.delete(inInfo, "RN01.delete");
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        return super.update(inInfo, "RN01.update");
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        return super.query(inInfo, "RN01.query");
    }

    private boolean check(String messageCode) {
        int count = super.count("RN01.checkCount", new HashMap<String, String>(1) {{
            put("messageCode", messageCode);
        }});
        return count == 0;
    }

}
