package com.baosight.rtservice.rn.nf.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.text.StrBuilder;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.rtservice.common.base.ResultCode;
import com.baosight.rtservice.common.base.RtConstant;
import com.baosight.rtservice.common.rn.constant.DialogType;
import com.baosight.rtservice.common.utils.JavaBeanUtil;
import com.baosight.rtservice.common.utils.MapUtils;
import com.baosight.rtservice.common.utils.ValidationUtil;
import com.baosight.rtservice.rn.domain.Dialog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/10/10
 */

@Slf4j
public class ServiceRNNF01 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 全局通知
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo globalNotification(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //参数校验
            Dialog dialog = JavaBeanUtil.mapToBean(inInfo.getAttr(), Dialog.class);
            String errorMsg = ValidationUtil.validateOne(dialog);
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new PlatException(errorMsg);
            }
            log.info("*******************Dialog[{}]：{}", "globalNotification", dialog.toString());

            //勿扰模式过滤
            EiInfo onInfo = dialog.filter(DialogType.NOTIFICATION_TYPE);
            if (onInfo.getStatus() == ResultCode.SUCCESS.getCode()) {
                return onInfo;
            }

            //根据消息标识获取配置
            List<Map<String, Object>> list = queryByMessageCode(inInfo);
            Map<String, Object> resultMap = list.stream().findFirst().orElse(null);
            if (MapUtils.isEmpty(resultMap)) {
                throw new PlatException("全局通知发送失败，原因[消息标识未找到!]");
            }

            //弹窗配置数据组织
            outInfo.set("result", setMessage(resultMap, dialog));
            //调用消息中心接口
            outInfo.set(EiConstant.serviceId, RtConstant.RP_NF_SERVICE_ID);
            EiInfo eiInfo = XServiceManager.call(outInfo);
            if (eiInfo.getStatus() < 0) {
                log.error("*******************notification{}：{}", RtConstant.RP_NF_SERVICE_ID, eiInfo.getMsg());
                throw new PlatException(eiInfo.getMsg());
            }
            log.info("*******************notification：{}", outInfo.getAttr().toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        }
        return outInfo;
    }

    private Map<String, Object> setMessage(Map<String, Object> resultMap, Dialog dialog) {
        Map<String, Object> messageMap = new HashMap<>(0);
        for (String key : resultMap.keySet()) {
            if ("notifyUser".equals(key)) {
                messageMap.put(key, String.valueOf(resultMap.get(key)).split(","));
            } else if ("url".equals(key)) {
                messageMap.put(key, String.valueOf(resultMap.get(key)).concat("?UUIDs=").concat(dialog.getUUIDs()));
            } else if ("showCancelButton".equals(key) || "showConfirmButton".equals(key)) {
                messageMap.put(key, "1".equals(resultMap.get(key)));
            } else {
                messageMap.put(key, resultMap.get(key));
            }
        }

        //动态设置消息内容
        if (null != dialog.getMsgKeys()) {
            String pattern = String.valueOf(resultMap.get(RtConstant.rtMessage));
            messageMap.put(RtConstant.rtMessage, MessageFormat.format(pattern, dialog.getMsgKeys().split(",")));
        }

        if (null != dialog.getOptions()) {
            messageMap.putAll(dialog.getOptions());

            //动态设置url传参
            if (dialog.getOptions().containsKey(RtConstant.rtParams)) {
                Map<String, Object> paramMap = Convert.convert(new TypeReference<Map<String, Object>>() {
                }, dialog.getOptions().get(RtConstant.rtParams));
                StrBuilder builder = StrBuilder.create();
                for (String key : paramMap.keySet()) {
                    builder.append("&").append(key).append("=").append(paramMap.get(key));
                }
                messageMap.put(RtConstant.rtUrl, String.valueOf(messageMap.get(RtConstant.rtUrl)).concat(builder.toString()));
            }
        }
        messageMap.put(RtConstant.rtUUID, dialog.getUUIDs());
        return messageMap;
    }


    /**
     * 查询消息代码
     *
     * @param inInfo ei
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    public List<Map<String, Object>> queryByMessageCode(EiInfo inInfo) {
        return dao.query("RN01.queryByCode", inInfo.getAttr());
    }

}
