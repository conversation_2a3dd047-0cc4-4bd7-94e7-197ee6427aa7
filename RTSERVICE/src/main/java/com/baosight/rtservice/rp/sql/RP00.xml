<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="RP00">

    <select id="query" resultClass="java.util.HashMap">
        SELECT service_id as "serviceId",
        routing_key as "routingKey",
        routing_key_cname as "routingKeyCname",
        project_cname as "projectCname",
        project_ename as "projectEname",
        create_time as "createTime",
        message_key as "messageKey"
        FROM ${platSchema}.rs_rp_param
        where 1=1
        <isNotEmpty prepend="and" property="serviceId">
            service_id like '%$serviceId$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="routingKey">
            routing_key like '%$routingKey$%'
        </isNotEmpty>
        <isNotEmpty prepend="and" property="messageKey">
            message_key like '%messageKey%'
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                service_id asc
            </isEmpty>
        </dynamic>
    </select>

    <select id="count" resultClass="int">
        SELECT COUNT(*) FROM${platSchema}.rs_rp_param WHERE 1=1
        <isNotEmpty prepend=" AND " property="serviceId">
            service_id = #serviceId#
        </isNotEmpty>
    </select>

    <select id="checkCount" resultClass="int">
        SELECT COUNT(*) FROM ${platSchema}.rs_rp_param WHERE 1=1
        <isNotEmpty prepend=" AND " property="serviceId">
            service_id = #serviceId#
        </isNotEmpty>
    </select>

    <insert id="insert">
        INSERT INTO ${platSchema}.rs_rp_param
        (service_id, routing_key, routing_key_cname, project_cname, project_ename, create_time,message_key)
        VALUES(#serviceId:VARCHAR#, #routingKey#, #routingKeyCname#, #projectCname#, #projectEname#,
        #createTime#,#messageKey#);
    </insert>

    <delete id="delete">
        DELETE FROM ${platSchema}.rs_rp_param
        WHERE
        service_id = #serviceId#
    </delete>

    <update id="update">
        UPDATE ${platSchema}.rs_rp_param
        SET
        routing_key=#routingKey#,
        routing_key_cname=#routingKeyCname#,
        project_cname=#projectCname#,
        project_ename=#projectEname#,
        message_key=#messageKey#
        WHERE
        service_id = #serviceId#
    </update>

    <!--查询审核记录-->
    <select id="queryRoutingKey" resultClass="java.util.HashMap">
        SELECT
        service_id as "serviceId",
        routing_key as "routingKey",
        routing_key_cname as "routingKeyCname",
        project_cname as "projectCname",
        project_ename as "projectEname",
        create_time as "createTime",
        message_key as "messageKey"
        FROM ${platSchema}.rs_rp_param
        <dynamic>
            <isNotEmpty prepend="WHERE" property="serviceId">
                service_id = #serviceId#
            </isNotEmpty>
        </dynamic>
    </select>
</sqlMap>