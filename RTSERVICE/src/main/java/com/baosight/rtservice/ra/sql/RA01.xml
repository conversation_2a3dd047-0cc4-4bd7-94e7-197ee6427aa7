<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<!-- 
 @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 @createDate:2023/10/8 18:07
-->

<sqlMap namespace="RA01">
    <insert id="insert" parameterClass="java.util.HashMap">
        insert into ${platSchema}.rs_action_report
        (user_id,user_name,title,url,time,ua,screen,trace_id,type,data,sdk_version,date_time,device_id)
        values(#uid#,#uName#,#title#,#url#,#time#,#ua#,#screen#,#traceId#,#type#,#data#,#sdk#,#dateTime#,#deviceId#)
    </insert>

    <select id="select" resultClass="java.util.HashMap">
        SELECT
        user_id as "userID",
        user_name as "userName",
        title as "title",
        url as "url",
        time as "time",
        ua as "ua",
        screen as "screen",
        trace_id as "traceId",
        type as "type",
        data as "data",
        sdk_version as "sdk",
        date_time as "dateTime",
        device_id as "deviceId"
        FROM ${platSchema}.rs_action_report
        WHERE  1=1
        <isNotEmpty prepend="and" property="userID">
            user_id = #userID#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="userName">
            user_name = #userName#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="type">
            type = #type#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="traceId">
            trace_id = #traceId#
        </isNotEmpty>
        <isNotEmpty prepend="and" property="dateTime">
            date_time = #dateTime#
        </isNotEmpty>
        <dynamic prepend="ORDER BY">
            <isNotEmpty property="orderBy">
                $orderBy$
            </isNotEmpty>
            <isEmpty property="orderBy">
                date_time desc
            </isEmpty>
        </dynamic>
    </select>
</sqlMap>