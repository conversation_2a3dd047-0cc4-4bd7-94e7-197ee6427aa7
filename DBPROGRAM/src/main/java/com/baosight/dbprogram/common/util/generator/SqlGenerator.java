package com.baosight.dbprogram.common.util.generator;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import com.gbasedbt.msg.sqli;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用于生成SQL插入、更新、删除语句的工具类。
 * 根据数据库中的表结构生成对应的 SQL 语句，并将结果保存到一个文件中。
 *
 * <p>使用方法:
 * 1. 配置数据库连接信息，包括数据库驱动类名称、URL、用户名和密码。
 * 2. 运行相应的方法，将会自动生成对应的 SQL 语句并保存到指定文件中。
 *
 * <p>注意事项:
 * - 此工具类依赖数据库驱动，请确保已正确导入数据库驱动。
 * - 自动生成的 SQL 语句可能需要根据实际情况进行进一步修改和优化。
 * - 生成的 SQL 语句仅适用于特定数据库的语法规范，如需在其他数据库中使用，请自行调整。
 *
 * <p>内部字段:
 * - connection: 数据库连接实例
 *
 * <AUTHOR>
 */
public class SqlGenerator {
    private static final String driverClassName = "com.gbasedbt.jdbc.Driver";
    private static final String url = "jdbc:gbasedbt-sqli://**************:9088/nocciplat:GBASEDBTSERVER=gbaseserver;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;";
    //    private static final String url = "jdbc:gbasedbt-sqli://************:9088/iplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_CN.57372;";
    private static final String username = "gbasedbt";
    private static final String password = "Ba0sight";
    private static final String schemaName = "irailmetropfm";
    private static final String tablePrefix = "t_";
    private static final String columnPrefix = "fd_";

    public static void main(String[] args) {
        try {
            SqlGenerator sqlGenerator = new SqlGenerator();
            sqlGenerator.generateSqlMap("insert", "DBKM02", "/outFile", "DBKM02.xml");
//            sqlGenerator.generateSqlMap("update", "KMUpdate", "/outFile", "KMUpdate.xml");
//            sqlGenerator.generateSqlMap("delete", "", "", "");
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }

    }

    /**
     * 数据库连接实例。
     * 使用单例模式获取，如果连接已关闭，则在需要时重新创建。
     */
    private static Connection connection = null;

    /**
     * 获取数据库连接。
     *
     * @return 数据库连接
     * @throws ClassNotFoundException 如果找不到数据库驱动类
     * @throws SQLException           如果数据库连接失败
     */
    private static Connection getConnection() throws ClassNotFoundException, SQLException {
        if (connection == null || connection.isClosed()) {
            Class.forName(driverClassName);
            connection = DriverManager.getConnection(url, username, password);
        }
        return connection;
    }

    /**
     * 生成 iBatis SQL Map，并将生成的 Insert、Update 和 Delete 语句放入 `<sqlMap>` 标签之间。
     *
     * @param generaType     生成类型（"insert"或"update"）
     * @param namespace      iBatis SQL Map 的命名空间
     * @param outputFilePath 输出文件的路径（相对于资源文件夹的根目录）
     * @param fileName       输出文件名
     * @throws ClassNotFoundException 如果找不到数据库驱动类
     * @throws SQLException           如果数据库连接失败
     */
    public void generateSqlMap(String generaType, String namespace, String outputFilePath, String fileName) throws ClassNotFoundException, SQLException {
        Connection connection = getConnection();
        DatabaseMetaData metaData = connection.getMetaData();
        List<String> tableNames = getTableNames(metaData, "t_pfp_dq");
        StringBuilder xmlContent = new StringBuilder();
        xmlContent.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        xmlContent.append("<!DOCTYPE sqlMap PUBLIC \"-//iBATIS.com//DTD SQL Map 2.0//EN\" \"http://www.ibatis.com/dtd/sql-map-2.dtd\">\n");
        xmlContent.append("<sqlMap namespace=\"").append(namespace).append("\">\n\n");

        for (String tableName : tableNames) {
            List<Column> columns = getColumns(metaData, tableName);
            List<PrimaryKey> primaryKeys = getPrimaryKeyColumns(metaData, tableName);
            if ("insert".equals(generaType)) {
                String insertStatement = generateInsertStatement(tableName, columns);
                xmlContent.append(insertStatement).append("\n");
            } else if ("update".equals(generaType)) {
                String updateStatement = generateUpdateStatement(tableName, columns, primaryKeys);
                xmlContent.append(updateStatement).append("\n");
            } else if ("delete".equals(generaType)) {
                generateAndExecuteDeleteStatement(tableName);
            } else {
                System.out.println(generaType + "类型不正确！");
            }
        }


        connection.close();
        xmlContent.append("</sqlMap>");
        writeToFile(xmlContent.toString(), outputFilePath, fileName);
        System.out.println(generaType + "执行完成！");
    }

    private List<String> getTableNames(DatabaseMetaData metaData) throws SQLException {
        return getTableNames(metaData, null);
    }

    /**
     * 获取数据库中指定模式下的所有表名。
     *
     * @param metaData 数据库元数据
     * @param prefix   匹配表格前缀
     * @return 表名列表
     * @throws SQLException 如果在获取表名时发生 SQL 异常
     */
    private List<String> getTableNames(DatabaseMetaData metaData, String prefix) throws SQLException {
        List<String> result = new ArrayList<>();
        String tableNamePattern;
        if (prefix != null && !prefix.isEmpty()) {
            tableNamePattern = String.format("%s%%", prefix);
        } else {
            tableNamePattern = null;
        }
        // 获取表的元数据
        ResultSet resultSet = metaData.getTables(null, schemaName, tableNamePattern, new String[]{"TABLE"});
        while (resultSet.next()) {
            String tableName = resultSet.getString("TABLE_NAME");
            result.add(tableName);
        }
        resultSet.close();
        return result;
    }

    /**
     * 获取指定表的所有列信息。
     *
     * @param metaData  数据库元数据
     * @param tableName 表名
     * @return 列信息列表
     * @throws SQLException 如果在获取列信息时发生 SQL 异常
     */
    private List<Column> getColumns(DatabaseMetaData metaData, String tableName) throws SQLException {
        List<Column> columns = new ArrayList<>();
        //获取指定表的列信息元数据
        ResultSet resultSet = metaData.getColumns(null, schemaName, tableName, null);
        while (resultSet.next()) {
            String columnName = resultSet.getString("COLUMN_NAME");
            String dataType = resultSet.getString("TYPE_NAME");
            int columnSize = resultSet.getInt("COLUMN_SIZE");
            boolean isNullable = "NO".equals(resultSet.getString("IS_NULLABLE"));
            Column column = new Column(columnName, dataType, columnSize, isNullable);
            columns.add(column);
        }
        resultSet.close();
        return columns;
    }


    /**
     * 获取指定表的主键列信息列表。
     *
     * @param metaData  数据库元数据
     * @param tableName 表名
     * @return 主键列信息列表
     * @throws SQLException 如果在获取主键列信息时发生 SQL 异常
     */
    private List<PrimaryKey> getPrimaryKeyColumns(DatabaseMetaData metaData, String tableName) throws SQLException {
        List<PrimaryKey> primaryKeys = new ArrayList<>();
        // 获取指定表的主键信息
        ResultSet resultSet = metaData.getPrimaryKeys(null, schemaName, tableName);
        // 遍历结果集，提取主键信息
        while (resultSet.next()) {
            String columnName = resultSet.getString("COLUMN_NAME");
            int keySeq = resultSet.getInt("KEY_SEQ");
            String pkName = resultSet.getString("PK_NAME");
            PrimaryKey pk = new PrimaryKey(columnName, keySeq, pkName);
            primaryKeys.add(pk);
        }
        resultSet.close();
        return primaryKeys;
    }


    /**
     * 生成 DELETE 语句并执行。
     *
     * @param tableName 表名
     * @throws SQLException 如果在生成或执行 DELETE 语句时发生 SQL 异常
     */
    private void generateAndExecuteDeleteStatement(String tableName) throws SQLException, ClassNotFoundException {
        if (tableName.contains("t_pfm_conf")) {
            return;
        }
        String deleteStatement = "DELETE FROM " + schemaName + "." + tableName + " where fd_date !='2023-07-12'";

        Connection connection = getConnection();
        try (Statement statement = connection.createStatement()) {
            statement.executeUpdate(deleteStatement);
        }
    }

    private String generateInsertStatement(String tableName, List<Column> columns) {
        String formattedTableName = formatTableName(tableName);
        boolean isFirstColumn = true;
        StringBuilder insertStatement = new StringBuilder("<insert id=\"insert")
                .append(formattedTableName).append("\" parameterClass=\"java.util.HashMap\">\n")
                .append("INSERT INTO ${pfmProjectSchema}.").append(tableName).append("\n(");
        StringBuilder values = new StringBuilder(") \nVALUES(\n");

        for (Column column : columns) {
            String columnName = column.getColumnName();
            boolean isNullable = column.isNullable();
            if (columnName.startsWith("fd_extend")) {
                continue; // 不包含 "fd_extend" 的字段
            }
            if (!isFirstColumn && isNullable) {
                insertStatement.append(",");
                values.append(",\n");
            } else {
                insertStatement.append("\n");
                isFirstColumn = false;
            }
            insertStatement.append(generateColumnInsert(columnName, isNullable));
            values.append(generateColumnValueInsert(columnName, isNullable));
        }
        insertStatement.append(values).append("\n)</insert>\n");
        return insertStatement.toString();
    }

    private String generateUpdateStatement(String tableName, List<Column> columns, List<PrimaryKey> primaryKeys) {
        boolean isFirstColumn = true;
        String formattedTableName = formatTableName(tableName);
        StrBuilder newValueBuilder = StrBuilder.create();
        StrBuilder conditionBuilder = StrBuilder.create();


        for (Column column : columns) {
            String columnName = column.getColumnName();
            if (columnName.startsWith("fd_extend")) {
                continue; // 不包含 "fd_extend" 的字段
            }
            if (!isFirstColumn) {
                processNotEmptyTag(newValueBuilder, columnName);
            } else {
                newValueBuilder.append(columnName).append(" = ").append("#").append(formatColumnName(columnName)).append("#\n");
                isFirstColumn = false;
            }
        }

        for (PrimaryKey primaryKey : primaryKeys) {
            String columnName = primaryKey.getColumnName();
            if (columnName.startsWith("fd_upload_time")) {
                continue; // 不包含 "fd_upload_time" 的字段
            }
            if (!isFirstColumn) {
                conditionBuilder.append(columnName).append(" = ").append("#").append(formatColumnName(columnName)).append("#\n");
            } else {
                conditionBuilder.append("AND ").append(columnName).append(" = ").append("#").append(formatColumnName(columnName)).append("#\n");
                isFirstColumn = true;
            }
        }

        String updateSql = StrUtil.format("UPDATE ${pfmProjectSchema}.{}\n SET {} WHERE {}", tableName,
                newValueBuilder.toString(), conditionBuilder.toString());

        return StrUtil.format("<update id=\"update{}\" parameterClass=\"java.util.HashMap\">\n{}\n</update>\n",
                formattedTableName, updateSql);
    }

    private static void processNotEmptyTag(StrBuilder xmlBuilder, String column) {
        xmlBuilder.append("<isNotEmpty prepend=\",\" property='").append(formatColumnName(column)).append("'>")
                .append(column).append(" = ").append("#").append(formatColumnName(column)).append("#").append(" </isNotEmpty>\n");
    }

    private static void processNotEmptyTagWithPrepend(StrBuilder xmlBuilder, String column) {
        xmlBuilder.append("<isNotEmpty prepend=\"AND\" property='").append(column).append("'>")
                .append(column).append(" = ").append(formatColumnName(column)).append(" </isNotEmpty>\n");
    }


    /**
     * 生成插入语句中的列部分。
     *
     * @param columnName 列名
     * @param isRequired 是否必填（非NULL）
     * @return 生成的插入语句片段
     */
    private static String generateColumnInsert(String columnName, boolean isRequired) {
        StringBuilder insertStatement = new StringBuilder();

        if (isRequired) {
            insertStatement.append(columnName);
        } else {
            insertStatement.append("<isNotEmpty prepend=\",\" property='").append(formatColumnName(columnName)).append("'>").append(columnName).append("</isNotEmpty>");
        }

        return insertStatement.toString();
    }

    /**
     * 生成插入语句中的值部分。
     *
     * @param columnName 列名
     * @param isRequired 是否必填（非NULL）
     * @return 生成的插入语句片段
     */
    private static String generateColumnValueInsert(String columnName, boolean isRequired) {
        StringBuilder values = new StringBuilder();
        if (isRequired) {
            values.append("\t#")
                    .append(formatColumnName(columnName))
                    .append("#");
        } else {
            values.append("<isNotEmpty prepend=\",\" property='").append(formatColumnName(columnName)).append("'>").append("#").append(formatColumnName(columnName)).append("#").append("</isNotEmpty>");
        }

        return values.toString();
    }

    /**
     * 格式化表名，将下划线命名转换为大驼峰命名。
     *
     * @param tableName 表名
     * @return 格式化后的表名
     */
    private static String formatTableName(String tableName) {
        if (tableName.startsWith(tablePrefix)) {
            return toPascalCase(tableName.substring(2));
        }
        return toPascalCase(tableName);
    }

    /**
     * 格式化列名，将列名中的"fd_"前缀去除，并转换为下划线命名。
     *
     * @param columnName 列名
     * @return 格式化后的列名
     */
    private static String formatColumnName(String columnName) {
        if (columnName.startsWith(columnPrefix)) {
            columnName = columnName.substring(3);
        }
        return convertToUnderscoreCase(columnName);
    }


    /**
     * 将字符串下划线转换为大驼峰命名。
     *
     * @param input 输入字符串
     * @return 大驼峰命名的字符串
     */
    private static String toPascalCase(String input) {
        String[] words = input.split("_");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            result.append(Character.toUpperCase(word.charAt(0))).append(word.substring(1).toLowerCase());
        }
        return result.toString();
    }

    /**
     * 将字符串下划线转换为小驼峰命名。
     *
     * @param input 输入字符串
     * @return 小驼峰命名的字符串
     */
    private static String toCamelCase(String input) {
        String[] words = input.split("_");
        StringBuilder result = new StringBuilder(words[0].toLowerCase());
        for (int i = 1; i < words.length; i++) {
            result.append(Character.toUpperCase(words[i].charAt(0))).append(words[i].substring(1).toLowerCase());
        }
        return result.toString();
    }

    /**
     * 将字符串转换为下划线命名。
     *
     * @param input 输入字符串
     * @return 下划线命名的字符串
     */
    private static String convertToUnderscoreCase(String input) {
        String[] words = input.split("_");
        StringBuilder result = new StringBuilder(words[0].toLowerCase());
        for (int i = 1; i < words.length; i++) {
            result.append("_").append(words[i].toLowerCase());
        }
        return result.toString();
    }

    /**
     * 将内容写入文件中。
     *
     * @param content    要写入的内容
     * @param outputPath 输出路径（相对于资源文件夹的根目录）
     * @param fileName   输出文件名
     */
    private static void writeToFile(String content, String outputPath, String fileName) {
        try {
            String absolutePath = Objects.requireNonNull(SqlGenerator.class.getResource("/")).getPath();
            String fullPath = absolutePath + outputPath;
            File folder = new File(fullPath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            String filePath = fullPath + "/" + fileName;
            FileWriter fileWriter = new FileWriter(filePath);
            fileWriter.write(content);
            fileWriter.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
