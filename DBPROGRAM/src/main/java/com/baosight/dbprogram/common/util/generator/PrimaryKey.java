package com.baosight.dbprogram.common.util.generator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 主键
 *
 * <AUTHOR>
 * @date 2023/07/07
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class PrimaryKey implements Serializable {

    private String columnName;
    /**
     * 主键序列号
     */
    private int keySeq;
    /**
     * 主键名称
     */
    private String pkName;
}
