# Maven版本管理指南

## 🎯 项目架构

```
cqyy (根项目 - 版本管理中心)
├── irailebs-parent (企业框架层)
│   ├── irailebs-pm (业务依赖管理层)
│   │   └── irailebs-pm-cqyy (具体业务模块)
│   └── irailebs-web (最终部署war)
├── irailebs-common (通用模块)
└── irailebs-vue (前端模块)
```

## 📋 版本属性说明

在根项目 `pom.xml` 中定义的版本属性：

- `irailebs-common.version`: 通用模块版本
- `irailebs-pm.version`: 业务管理层版本
- `irailebs-pm-cqyy.version`: 具体业务模块版本
- `irailebs-web.version`: Web应用版本
- `irailebs-vue.version`: 前端模块版本

## 🛠️ Maven Versions Plugin 使用方法

### 1. 更新单个模块版本

```bash
# 更新irailebs-pm-cqyy模块版本
mvn versions:set-property -Dproperty=irailebs-pm-cqyy.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 更新irailebs-web模块版本
mvn versions:set-property -Dproperty=irailebs-web.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 更新irailebs-common模块版本
mvn versions:set-property -Dproperty=irailebs-common.version -DnewVersion=1.0.1
```

### 2. 批量更新业务模块版本

```bash
# 更新所有业务模块到相同版本
mvn versions:set-property -Dproperty=irailebs-pm.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT
mvn versions:set-property -Dproperty=irailebs-pm-cqyy.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT
mvn versions:set-property -Dproperty=irailebs-web.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT
```

### 3. 更新项目基础版本

```bash
# 更新根项目版本
mvn versions:set -DnewVersion=1.1.0

# 更新基础版本属性
mvn versions:set-property -Dproperty=project.base.version -DnewVersion=1.1.0
```

### 4. 版本管理最佳实践

```bash
# 1. 检查当前版本状态
mvn versions:display-property-updates

# 2. 更新版本
mvn versions:set-property -Dproperty=irailebs-pm-cqyy.version -DnewVersion=1.0.1-7.1.0-SNAPSHOT

# 3. 验证构建
mvn clean compile

# 4. 提交版本更改
mvn versions:commit

# 5. 如果有问题，回滚版本
mvn versions:revert
```

## ✅ 版本更新检查清单

当需要更新业务模块版本时：

- [ ] 确定要更新的模块和新版本号
- [ ] 使用 `mvn versions:set-property` 更新版本属性
- [ ] 运行 `mvn clean compile` 验证构建
- [ ] 运行 `mvn versions:commit` 提交更改
- [ ] 如有问题，运行 `mvn versions:revert` 回滚

## 🚀 构建和部署

```bash
# 构建所有模块
mvn clean install

# 只构建业务模块
mvn clean install -pl irailebs-parent/irailebs-pm/irailebs-pm-cqyy

# 构建并打包war
mvn clean package -pl irailebs-parent/irailebs-web
```

## 📝 注意事项

1. **版本属性集中管理**：所有版本在根项目统一定义
2. **依赖版本继承**：子模块通过 dependencyManagement 继承版本
3. **备份文件**：versions插件会生成 `.versionsBackup` 文件，可用于回滚
4. **SNAPSHOT版本**：开发阶段使用SNAPSHOT，发布时切换到正式版本
