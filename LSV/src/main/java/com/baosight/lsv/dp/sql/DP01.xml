<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DP01">
    <!--  线网营收 -->
    <select id="queryInComeNet" resultClass="java.util.HashMap">
        select
        fd_income as "value"
        from ${tepProjectSchema}.t_rep_day_002
        where fd_line_number = '0000000000'
        <isNotEmpty prepend="AND" property="date"> fd_date=#date# </isNotEmpty>
    </select>

    <!--查询小代码-->
    <select id="queryCodes" resultClass="java.util.HashMap">
        select
        codeset_code as "codeType",
        item_code as "itemCode",
        item_cname as "itemCname"
        from ${platSchema}.tedcm01
        where 1=1 and archive_flag != '1'
        <isNotEmpty prepend=" AND " property="codeType">
            codeset_code = #codeType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="itemCname">
            item_cname = #itemCname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="itemCode">
            item_code = #itemCode#
        </isNotEmpty>
    </select>
</sqlMap>