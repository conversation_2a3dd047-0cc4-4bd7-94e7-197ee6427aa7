package com.baosight.imp.jc.ep.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapBuilder;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author:YangZiAng
 * @createDate:2023/9/21 19:42
 **/
public class ServiceJCEP01 extends ServiceBase {
    List<Map<String,Object>> lineList = new ArrayList<Map<String,Object>>(){{
        add(MapBuilder.create(new HashMap<String,Object>()).put("lineNumber","1").put("lineName","1号线").build());
        add(MapBuilder.create(new HashMap<String,Object>()).put("lineNumber","2").put("lineName","2号线").build());
        add(MapBuilder.create(new HashMap<String,Object>()).put("lineNumber","3").put("lineName","3号线").build());
        add(MapBuilder.create(new HashMap<String,Object>()).put("lineNumber","4").put("lineName","4号线").build());
        add(MapBuilder.create(new HashMap<String,Object>()).put("lineNumber","5").put("lineName","5号线").build());
    }};

    Map<String,Object> substationList = new HashMap<String,Object>(){{
        put("1号线",new ArrayList<>(Arrays.asList("万力主变电站","秋屋主变电站")));
        put("2号线",new ArrayList<>(Arrays.asList("朋云主变电站","秀灵主变电站","乐荣主变电站")));
        put("3号线",new ArrayList<>(Arrays.asList("荔园主变电站","秀灵主变电站","乐荣主变电站")));
        put("4号线",new ArrayList<>(Arrays.asList("壮宁主变电站","朋云主变电站")));
        put("5号线",new ArrayList<>(Arrays.asList("仁和主变电站","业平主变电站")));
    }};

    List<Map<String,Object>> deviceList = new ArrayList<Map<String,Object>>(){{
        add(MapBuilder.create(new HashMap<String,Object>()).put("deviceName","110KV设备").put("deviceNameValue","110kV").build());
        add(MapBuilder.create(new HashMap<String,Object>()).put("deviceName","35kV设备").put("deviceNameValue","35kV").build());
    }};

    List<Map<String,Object>> gridList = new ArrayList<Map<String,Object>>(){{
        add(MapBuilder.create(new HashMap<String,Object>())
                .put("indexNum","1")
                .put("creatTime","2024-03-20 10:06:53")
                .put("lineName","1号线")
                .put("substationName","万力主变电站")
                .put("deviceName","110KV1#进线柜")
                .put("signalName","103断路器位置")
                .put("info","分位")
                .build());
        add(MapBuilder.create(new HashMap<String,Object>())
                .put("indexNum","2")
                .put("creatTime","2024-03-20 10:06:53")
                .put("lineName","1号线")
                .put("substationName","万力主变电站")
                .put("deviceName","110KV2#进线柜")
                .put("signalName","104断路器位置")
                .put("info","分位")
                .build());
    }};
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        //默认查当天全部数据
        Date curDate = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String curDay =  dateFormat.format(curDate);

        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String curDay1 =  dateFormat1.format(curDate);

        inInfo.set("inqu_status-0-startTime",curDay + " 00:00:00");
        inInfo.set("inqu_status-0-endTime",curDay1);
        inInfo.set("result-limit",20);

        //查询gbase数据库
        List<Map<String,String>> result = dao.query("JCEP01.querySOEInfo", inInfo.getBlock("inqu_status").getRow(0), 0, 20);
        List countResult = dao.query("JCEP01.countSOEInfo", inInfo.getBlock("inqu_status").getRow(0));
        for (int i = 0; i < result.size(); i++) {
            result.get(i).put("indexNum",(i+1) + "");
        }
        inInfo.getBlock("result").addRows(result);
        inInfo.getBlock("result").set(EiConstant.countStr, countResult.get(0));
        inInfo.getBlock("result").set("showCount", "true");
        return inInfo;
    }

    public EiInfo lineInit(EiInfo inInfo){
        EiBlock block = new EiBlock("line");
        block.setRows(lineList);
        inInfo.addBlock(block);
        return inInfo;
    }
    public EiInfo deviceInit(EiInfo inInfo){
        EiBlock block1 = new EiBlock("device");
        block1.setRows(deviceList);
        inInfo.addBlock(block1);
        return inInfo;
    }
    public EiInfo substationInit(EiInfo inInfo){
        List<Map<String,Object>> substationNames = (List<Map<String, Object>>) getSubstationsName(inInfo).get("data");
        EiBlock block2 = new EiBlock("substation");
        block2.setRows(substationNames);
        inInfo.addBlock(block2);
        return inInfo;
    }

    /**
     * 获取主变电站
     * @param inInfo
     * @return
     */
    public EiInfo getSubstationsName(EiInfo inInfo){
        Map<String,Object> lists = new HashMap<>();
        if (inInfo.get("lineNumber") != null && inInfo.get("lineNumber") != ""){
            String lineNumber = inInfo.getString("lineNumber");
            lists.put(lineNumber,substationList.get(lineNumber));
        }else {
            lists = substationList;
        }
        List<Map<String,Object>> substationNames = new ArrayList<Map<String,Object>>();
        for (String key : lists.keySet()){
            Convert.toList(lists.get(key)).forEach(o->{
                Map<String,Object> substationMap = new HashMap();
                substationMap.put("substationName",o);
                String value = String.valueOf(o).substring(0,2) + "主所";
                substationMap.put("substationNameValue",value);
                substationNames.add(substationMap);
            });
        }
        List<Map<String,Object>>  distinctSubstationNames = substationNames.stream().distinct().collect(Collectors.toList());
        EiInfo outInfo = new EiInfo();
        outInfo.set("data",distinctSubstationNames);
        return outInfo;
    }

    public EiInfo gridQuery(EiInfo inInfo){
//        EiInfo outInfo = new EiInfo();
        EiBlock gridblock = inInfo.getBlock("result");
        int limit = gridblock.getInt(EiConstant.limitStr);
        int offset = gridblock.getInt(EiConstant.offsetStr);
        Map inqu_status = inInfo.getBlock("inqu_status").getRow(0);
        List<Map<String,String>> result = dao.query("JCEP01.querySOEInfo",inqu_status,offset,limit);
        List countResult = dao.query("JCEP01.querySOEInfo", inInfo.getBlock("inqu_status").getRow(0));
        for (int i = 0; i < result.size(); i++) {
            result.get(i).put("indexNum",(offset + i+1) + "");
        }
        inInfo.addBlock("result").addRows(result);
        inInfo.getBlock("result").setAttr(gridblock.getAttr());
        inInfo.getBlock("result").set(EiConstant.countStr, countResult.size());
        inInfo.getBlock("result").set("showCount", "true");
        return inInfo;
    }

    public List<Map<String,Object>> dataFilter(Map param){
        return gridList.stream().filter(o->o.get("lineName").toString().contains(param.get("lineNumber").toString())
                        && o.get("deviceName").toString().contains(param.get("deviceName").toString())
                && o.get("substationName").toString().contains(param.get("substationName").toString())
                && o.get("creatTime").toString().compareTo(param.get("startTime").toString()) >= 0
                && o.get("creatTime").toString().compareTo(param.get("endTime").toString()) <= 0)
                .collect(Collectors.toList());
    }
}