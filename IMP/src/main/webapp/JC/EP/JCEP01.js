var timeRefreshid;
var isStop = 0;//默认值，自动刷新
$(function () {




    IPLATUI.EFSelect = {
        "inqu_status-0-lineNumber":{
            change:function (e){
                if (IPLAT.EFSelect.value($("#inqu_status-0-lineNumber")) == ""){
                    changeSubstation(new EiInfo());
                    IPLAT.EFSelect.value( $("#inqu_status-0-substationName"), "" );
                    return;
                }else {
                    let inInfo = new EiInfo();
                    inInfo.set("lineNumber",IPLAT.EFSelect.value($("#inqu_status-0-lineNumber")));
                    changeSubstation(inInfo);
                }
            }
        },
    }


    //实时SOE限制查询当天的数据
    IPLATUI.EFDateSpan = {
        "inqu_status-0-startTime": {//开始日期
            max: getCurrentDate() + ' 23:59:00',
            min: getCurrentDate() + ' 00:00:00'
        },
        "inqu_status-0-endTime": {//结束日期
            max: getCurrentDate() + ' 23:59:00',
            min: getCurrentDate() + ' 00:00:00'
        },
    };

    IPLATUI.EFGrid = {
        "result":{
            query:function (){
                let inInfo = new EiInfo();
                inInfo.setById("inqu_status-0-lineNumber");
                inInfo.setById("inqu_status-0-deviceName");
                inInfo.setById("inqu_status-0-substationName");
                inInfo.setById("inqu_status-0-startTime");
                inInfo.setById("inqu_status-0-endTime");
                return inInfo;
            },
            pageable: {
                pageSize: 20,
                pageSizes:[5, 10, 20, 50, 100, 1000]//可选的单页展示条数列表
            }
        }
    }
    $(window).on("load", function () {
        $(".i-grid-pager").css("float", "right");//右移动翻页组件
        dateInit();
        timeRefreshid = setInterval(function() {
            resultGrid.dataSource.page(1);
        }, 10000)// 每10s刷新表单
    });

    //切换主变电站数据源
    function changeSubstation(inInfo){
        IPLAT.sendService("JCEP01","getSubstationsName",inInfo,(res)=>{
            let substationList = [];
            res.extAttr.data.forEach(data => {
                var select = {
                    "textField":data.substationName,
                    "valueField":data.substationNameValue
                }
                substationList.push(select);
            })

            var dataSource = new kendo.data.DataSource({
                data:substationList
            });
            IPLAT.EFSelect.setDataSource($("#inqu_status-0-substationName"), dataSource);
        });
    }

    function dateInit(){
        let yesDt = getCurrentDate() + ' 00:00:00';
        let nowDt = dayjs().format('YYYY-MM-DD HH:mm:ss');
        $("#inqu_status-0-startTime").val(yesDt);
        $("#inqu_status-0-endTime").val(nowDt);
    }

    function cancelFilter(){
        dateInit();
        changeSubstation(new EiInfo());
        IPLAT.EFSelect.value( $("#inqu_status-0-substationName"), "" );
        IPLAT.EFSelect.value( $("#inqu_status-0-lineNumber"), "" );
        IPLAT.EFSelect.value( $("#inqu_status-0-deviceName"), "" );
    }

    $("#cancel").click(function (){
        cancelFilter();
        gridFresh();
        if (isStop == 1){
            timeRefreshid = setInterval(function() {
                resultGrid.dataSource.page(1);
            }, 10000)// 每10s刷新表单
            isStop = 0;
        }
    });

    $("#query").click(function (){
        gridFresh();
        // 解除定时器
        clearInterval(timeRefreshid);
        isStop = 1;
    });

    function gridFresh(){
        resultGrid.dataSource.page(1);
    }
})




/**
 * 获取当前日期
 * @returns {string}
 * format:yyyy-MM-dd
 */
function getCurrentDate() {
    const today = new Date();
    today.setDate(today.getDate());
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}