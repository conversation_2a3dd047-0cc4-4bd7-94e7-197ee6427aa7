<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
<EF:EFPage title="主所历史SOE" prefix="nocc">
        <style>
            .condition{
                text-align: center;
                margin-left: 10rem;
                margin-top: 4rem;
            }
            .condition div{
                padding: 0;
            }
            .condition .form-group>div{
                padding: 0 !important;
            }
            .condition .form-group>label{
                padding: 8px 0 0 0!important
            }
            /*.time{*/
            /*    margin-left: 1rem;*/
            /*}*/
            .time .form-group>label{
                padding: 5px 0 0 0!important
            }
            .grid{
                margin-top: 2rem;
            }
            .k-content{
                background-color: transparent;
            }
        </style>
        <div class="row">
            <div class="page-title">主所历史SOE</div>
            <div class="condition">
                <div class="row">
                    <div class="col-md-5">
                        <div class="col-md-3">
                            <EF:EFSelect textField="lineName"  optionLabel="请选择"  valueField="lineName"
                                         colWidth="12" ratio="4:8" ename="inqu_status-0-lineNumber"
                                         cname="线路" resultId="line" serviceName="JCEP02" methodName="lineInit">
                            </EF:EFSelect>
                        </div>
                        <div class="col-md-5">
                            <EF:EFSelect textField="substationName"  optionLabel="请选择"  valueField="substationNameValue"
                                         colWidth="12" ename="inqu_status-0-substationName" ratio="5:7"
                                         cname="主变电站名称" resultId="substation" serviceName="JCEP02" methodName="substationInit">
                            </EF:EFSelect>
                        </div>
                        <div class="col-md-4">
                            <EF:EFSelect  textField="deviceName"  optionLabel="请选择"  valueField="deviceNameValue"
                                          colWidth="12" ratio="4:8" ename="inqu_status-0-deviceName"
                                          cname="设备名称" resultId="device" serviceName="JCEP02" methodName="deviceInit">
                            </EF:EFSelect>
                        </div>
                    </div>
                    <div class="col-md-4 time">
                        <EF:EFDateSpan startName="inqu_status-0-startTime" format="yyyy-MM-dd HH:mm:ss" bindRatio="1:5:5"
                                       endName="inqu_status-0-endTime" bindWidth="12" bindName="日期"
                                       extStyle="true" extChar="—" role="datetime"/>
                    </div>
                    <div class="col-md-2">
                        <EF:EFButton ename="query" cname="查询"></EF:EFButton>
                        <EF:EFButton ename="cancel" cname="取消过滤"></EF:EFButton>
                    </div>
                </div>
                <div class="grid row">
                    <div class="col-md-11">
                        <EF:EFGrid readonly="true" blockId="result" autoDraw="override" serviceName="JCEP02"
                                   autoBind="false" queryMethod="gridQuery" enable="hidden"  pagerPosition="right"  height="550px"
                                   checkMode="single,row" copyToAdd="false" toolbarConfig="{hidden:'all'}">
                            <EF:EFColumn ename="indexNum" align="center" cname="序号"></EF:EFColumn>
                            <EF:EFColumn ename="creattime" align="center" cname="产生时间"></EF:EFColumn>
                            <EF:EFColumn ename="linename" align="center" cname="线路"></EF:EFColumn>
                            <EF:EFColumn ename="substationname" align="center" cname="主变电所名称"></EF:EFColumn>
                            <EF:EFColumn ename="devicename" align="center" cname="设备名称"></EF:EFColumn>
                            <EF:EFColumn ename="signalname" align="center" cname="信号点名称"></EF:EFColumn>
                            <EF:EFColumn ename="info" align="center" cname="SOE动作信息"></EF:EFColumn>
                        </EF:EFGrid>
                    </div>
                </div>
            </div>

        </div>
</EF:EFPage>
</div>