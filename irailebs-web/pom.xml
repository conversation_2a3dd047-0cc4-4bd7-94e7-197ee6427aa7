<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.irailebs</groupId>
        <artifactId>cqyy</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>irailebs-web</artifactId>
    <version>1.0.0-7.1.0-SNAPSHOT</version>
    <packaging>war</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <!-- 业务模块依赖 -->
        <dependency>
            <groupId>com.baosight.irailebs</groupId>
            <artifactId>irailebs-pm-cqyy</artifactId>
        </dependency>

        <!-- 达梦数据库驱动 -->
        <dependency>
            <groupId>com.dm</groupId>
            <artifactId>dm8-jdbc-driver-18</artifactId>
            <version>8.1.1.190</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>cqyy</finalName>
        <resources>
            <!-- 配置资源目录及其过滤规则 -->
            <resource>
                <!-- 指定资源目录为src/main/webapp，此目录下的资源将被处理 -->
                <directory>src/main/webapp</directory>
                <!-- 注意此次必须要放在此目录下才能被访问到 -->
                <targetPath>META-INF/resources</targetPath>
                <!-- 包含所有子目录下的所有文件和文件夹 -->
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>

            <resource>
                <!-- 指定资源目录为src/main/java，此目录下的资源将被处理 -->
                <directory>src/main/java</directory>
                <!-- 仅包含此目录下所有的XML文件 -->
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>

            <resource>
                <!-- 指定资源目录为src/main/resources，此目录下的资源将被处理 -->
                <directory>src/main/resources</directory>
                <!-- 排除此目录下所有properties子目录中的文件 -->
                <excludes>
                    <exclude>properties/**</exclude>
                </excludes>
            </resource>

            <!-- 配置资源目录 -->
            <resource>
                <!-- 根据激活的配置文件动态加载资源 -->
                <directory>src/main/resources/properties/${profiles.active}</directory>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <!--用以打war包-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <warSourceExcludes>src/main/resources/META-INF/**</warSourceExcludes>
                    <packagingExcludes>WEB-INF/classes/META-INF/**</packagingExcludes>
                    <webResources>
                        <resource>
                            <directory>src/main/resources/META-INF/resources</directory>
                            <filtering>false</filtering>
                            <targetPath>/</targetPath>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
