package com.notice.domain;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class NoticeRecord {
    /**
     * uuid
     */
    private String uuids;
    /**
     * 标题
     */
    @Alias("messageTitle")
    private String title;
    /**
     * 消息
     */
    private String message;
    /**
     * 跳转页面url
     */
    @Alias("url")
    private String content;
    /**
     * 用户名
     */
    @Alias("notifyUser")
    private List<String> recipients;
    /**
     * 取消按钮文本
     */
    private String cancelText;
    /**
     * 确认按钮文本
     */
    private String confirmText;
    /**
     * 显示取消按钮
     */
    private boolean showCancelButton;
    /**
     * 显示确认按钮
     */
    private boolean showConfirmButton;
    /**
     * 跳转页面打开区域
     */
    private String openAddress;

    /**
     * 弹窗类型
     */
    private String type = "";

    /**
     * 优先级
     */
    private Integer priority = 0;

    /**
     * 判断这个收信人是否在这条消息的推送列表中
     *
     * @param recipient
     * @return
     */
    public Boolean isContain(String recipient) {
        if (this.recipients.contains(recipient)) {
            return true;
        } else {
            return false;
        }
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<String> recipients) {
        this.recipients = recipients;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getUuids() {
        return uuids;
    }

    public void setUuids(String uuids) {
        this.uuids = uuids;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCancelText() {
        return cancelText;
    }

    public void setCancelText(String cancelText) {
        this.cancelText = cancelText;
    }

    public String getConfirmText() {
        return confirmText;
    }

    public void setConfirmText(String confirmText) {
        this.confirmText = confirmText;
    }

    public boolean isShowCancelButton() {
        return showCancelButton;
    }

    public void setShowCancelButton(boolean showCancelButton) {
        this.showCancelButton = showCancelButton;
    }

    public boolean isShowConfirmButton() {
        return showConfirmButton;
    }

    public void setShowConfirmButton(boolean showConfirmButton) {
        this.showConfirmButton = showConfirmButton;
    }

    public String getOpenAddress() {
        return openAddress;
    }

    public void setOpenAddress(String openAddress) {
        this.openAddress = openAddress;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
}
