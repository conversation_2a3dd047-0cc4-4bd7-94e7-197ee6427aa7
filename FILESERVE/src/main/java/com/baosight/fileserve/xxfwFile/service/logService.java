package com.baosight.fileserve.xxfwFile.service;

import com.baosight.iplat4j.core.ei.EiInfo;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.*;
import java.util.*;

public class logService {
    //导出标题
    private static  final String[] arrtitle1 = new String[]{"事件名称", "事发时间", "结束时间","选用预案", "现场指挥长", "事件详情","消息通知", "电话通知","消息已读", "电话已接", "人员签到"};
    private static  final Map<String, String> mapTitle1 = new HashMap();

    private static  final String[] arrtitle2 = new String[]{"事发地点", "事件等级", "事件来源","抢险负责人"};
    private static  final String[] arrtitle3 = new String[]{"现场指挥长发布记录", "抢险负责人发布记录", "OCC发布记录","NOCC发布记录","OCC处置记录","NOCC处置记录"};

    private static  final String[] arrIndex = new String[]{"时间", "处置人", "处置措施","附件"};
    private static  final Map<String, String> mapRelese = new HashMap();

    private static int[] rowArr = {0,5,6,7,8,9,10};

    static {
        mapRelese.put("时间","time");
        mapRelese.put("处置人","name");
        mapRelese.put("处置措施","dispose");
        mapRelese.put("附件","org");

        mapTitle1.put("事件名称","eventName");
        mapTitle1.put("事发时间","startTime");
        mapTitle1.put("结束时间","endTime");
        mapTitle1.put("选用预案","plan");
        mapTitle1.put("现场指挥长","commander");
        mapTitle1.put("事件详情","details");
        mapTitle1.put("消息已读","msgNotice");
        mapTitle1.put("电话已接","cellNotice");
        mapTitle1.put("消息通知","msgNotices");
        mapTitle1.put("电话通知","cellNotices");
        mapTitle1.put("人员签到","sighName");
    }

    /**
     * 导出入口
     * @param data 数据块
     * @param
     * @return
     */
    public static EiInfo excelOutput(List<Map<String, Object>> data) {
        EiInfo eiInfo = new EiInfo();
        try {
            dataToOutStream(data);
        } catch (Exception e){
            e.printStackTrace();
        }
        return eiInfo;
    }

    /**
     * 处置日志导出方法
     * @param
     * @param rowList   数据集合
     * @return
     */
    public static byte[] dataToOutStream(List<Map<String,Object>> rowList){
        Map<String, Object> map = rowList.get(0);

        List<Map<String, Object>> sceneDispose = (List<Map<String, Object>>) rowList.get(0).get("sceneDispose");//现场处置记录
        List<Map<String, Object>> commanderRelease = (List<Map<String, Object>>) rowList.get(0).get("commanderRelease");//现场指挥长发布记录
        List<Map<String, Object>> dutyRelease = (List<Map<String, Object>>) rowList.get(0).get("dutyRelease");//抢险负责人发布记录
        List<Map<String, Object>> occRelease = (List<Map<String, Object>>) rowList.get(0).get("occRelease");//OCC发布记录
        List<Map<String, Object>> noccRelease = (List<Map<String, Object>>) rowList.get(0).get("noccRelease");//NOCC发布记录
        List<Map<String, Object>> occDispose = (List<Map<String, Object>>) rowList.get(0).get("occDispose");//OCC处置记录
        List<Map<String, Object>> noccDispose = (List<Map<String, Object>>) rowList.get(0).get("noccDispose");//NOCC处置记录

        List<List<Map<String, Object>>> list = new ArrayList();
        list.add(commanderRelease);
        list.add(dutyRelease);
        list.add(occRelease);
        list.add(noccRelease);
        list.add(occDispose);
        list.add(noccDispose);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        try {
            //计算单元格行数
            int rowsNum = 19 + sceneDispose.size() + commanderRelease.size() + dutyRelease.size() + occRelease.size() + noccRelease.size() + occDispose.size() + noccDispose.size();
            //创建文本对象
            XWPFDocument docxDocument = new XWPFDocument();
            //创建文本表格
            XWPFTable table = docxDocument.createTable(rowsNum, 4);
            table.setWidth("100%");
            table.setWidthType(TableWidthType.PCT);//设置表格相对宽度
            table.setTableAlignment(TableRowAlign.CENTER);
            //合并单元格
//            for (int i = 0; i < 6; i=i+4){
//                XWPFTableRow row1 = table.getRow(i);
//                getCellMerge(1,3, row1);
//            }

            for (int i : rowArr) {
                XWPFTableRow row1 = table.getRow(i);
                getCellMerge(1,3, row1);
            }
//            for (int i = 5; i < 9; i++){
//                XWPFTableRow row2 = table.getRow(i);
//                getCellMerge(1,3, row2);
//            }
            XWPFTableRow row2 = table.getRow(9);//合并第十行

            //标题写入单元格,插入前十一行标题
            for (int i = 0; i < arrtitle1.length; i++){
                XWPFTableRow rows = table.getRow(i);
                XWPFTableCell cells = rows.getCell(0);
                cells.setText(arrtitle1[i]+":");
                cells.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            }
            for (int j = 1;j <= arrtitle2.length; j++){
                XWPFTableRow rows = table.getRow(j);
                XWPFTableCell cells = rows.getCell(2);
                cells.setText(arrtitle2[j-1]+":");
                cells.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            }
            //将数据写入前十一行单元格中
            for (int i = 0;i < arrtitle1.length;i++){
                XWPFTableRow rows = table.getRow(i);
                XWPFTableCell cell1 = rows.getCell(1);
                XWPFTableCell cell2 = rows.getCell(3);
                if (i == 0) {
                    XWPFTableCell cells = rows.getCell(1);
                    cells.setText((String) map.get("eventName"));
                    cells.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
                if (i == 1){
                    cell1.setText((String) map.get("startTime"));
                    cell1.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                    cell2.setText((String) map.get("place"));
                    cell2.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
                if (i == 2){
                    cell1.setText((String) map.get("endTime"));
                    cell1.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                    cell2.setText((String) map.get("level"));
                    cell2.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
                if (i == 3){
                    cell1.setText((String) map.get("plan"));
                    cell1.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                    cell2.setText((String) map.get("source"));
                    cell2.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
                if (i == 4){
                    cell1.setText((String) map.get("commander"));
                    cell1.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                    cell2.setText((String) map.get("dutyName"));
                    cell2.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
                if (i > 4){
                    String key = mapTitle1.get(arrtitle1[i]);
                    String val = (String) map.get(key);
                    cell1.setText(val);
                    cell1.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                }
            }
            //将字段标题写入现场处置记录的单元格中
            XWPFTableRow rowOf9 = table.getRow(11);
            getCellMerge(0,3, rowOf9);
            XWPFTableCell cells = rowOf9.getCell(0);
            cells.setText("现场处置记录");
            XWPFTableRow rowOf10 = table.getRow(12);
            XWPFTableCell cellOf0 = rowOf10.getCell(0);
            XWPFTableCell cellOf1 = rowOf10.getCell(1);
            XWPFTableCell cellOf2 = rowOf10.getCell(2);
            XWPFTableCell cellOf3 = rowOf10.getCell(3);
            cellOf0.setText("时间");
            cellOf1.setText("处置人");
            cellOf2.setText("处置措施");
            cellOf3.setText("附件");
            //现场处置记录数据写入单元格
            for (int i = 13; i < 13+sceneDispose.size(); i++){
                XWPFTableRow row = table.getRow(i);
                for (int j = 0; j < 4; j++){
                    XWPFTableCell cell = row.getCell(j);
                    String key = mapRelese.get(arrIndex[j]);


                    if ("org".equals(key)) {
                        List<Object> imgObjects = (List<Object>) sceneDispose.get(i - 13).get(key);
                        if (imgObjects == null || imgObjects.isEmpty()) {
                            continue;
                        }

                        int imgCount = imgObjects.size();
                        int imgsPerRow = 3;
                        int rowCount = (int) Math.ceil((double) imgCount / imgsPerRow);

                        XWPFParagraph paragraph = row.getCell(j).addParagraph();

                        for (int rowIndex = 0; rowIndex < rowCount; rowIndex++) {
                            for (int colIndex = 0; colIndex < imgsPerRow && rowIndex * imgsPerRow + colIndex < imgCount; colIndex++) {
                                Object obj = imgObjects.get(rowIndex * imgsPerRow + colIndex);
                                byte[] byteImg = Base64.getDecoder().decode(obj.toString());

                                XWPFRun run = paragraph.createRun();
                                run.addPicture(new ByteArrayInputStream(byteImg), XWPFDocument.PICTURE_TYPE_JPEG,
                                        "image" + (rowIndex * imgsPerRow + colIndex + 1) + ".jpg", Units.toEMU(80 / 3), Units.toEMU(80 / rowCount));
                                run.addTab();
                                if (colIndex >= imgsPerRow - 1) {
                                    run.addCarriageReturn();
                                }
                            }

                            if (rowIndex < rowCount - 1) {
                                paragraph.createRun().addBreak();
                            }
                        }
                    } else {
                        String val = (String) sceneDispose.get(i - 13).get(key);
                        cell.setText(val);
                    }

//                    if ("org".equals(key) ){//&& StringUtils.isNotEmpty((String) sceneDispose.get(i-11).get(key))
//                        Object obj = sceneDispose.get(i-11).get(key);
//                        if(obj == null){
//                            continue;
//                        }
////                        byte[] byteImg = (byte[]) obj;
//                        byte[] byteImg = Base64.getDecoder().decode(obj.toString());
//                        // 将图片插入到文档中
//                        XWPFParagraph paragraph = docxDocument.createParagraph();
//                        paragraph = row.getCell(j).addParagraph();
//                        XWPFRun run = paragraph.createRun();
//    //                    byte[]  val = (byte[]) sceneDispose.get(i-11).get(key);
//
////                        String val = (String) sceneDispose.get(i-11).get(key);
////                        byte[] byteImg = Base64.getDecoder().decode(val);
//                        run.addPicture(new ByteArrayInputStream(byteImg), XWPFDocument.PICTURE_TYPE_JPEG,
//                                "image.jpg", Units.toEMU(80), Units.toEMU(80));
//                    }else{
//                        String val = (String) sceneDispose.get(i-11).get(key);
//                        cell.setText(val);
//                    }
                }
            }
            //从第14行开始累加行数
            int countRow = 13+sceneDispose.size()+1;//初始开始行数（从第14行开始）
            //合并单元格
            int index = 0;
            for (int k = countRow-1;k < rowsNum; k = k+list.get(index++).size()+1){
                XWPFTableRow row = table.getRow(k);
                getCellMerge(0,3, row);
                XWPFTableCell cell = row.getCell(0);
                cell.setText(arrtitle3[index]);
            }
            for (int i = 0; i < list.size(); i++){
                for (int j = countRow; j < countRow + list.get(i).size();j++){
                    XWPFTableRow row = table.getRow(j);
                    for (int k = 0; k < 4; k++){

                        String key = mapRelese.get(arrIndex[k]);

                        if ("org".equals(key) ){

                            List<Object> imgObjects = (List<Object>) list.get(i).get(j-countRow).get(key);
                            if (imgObjects == null || imgObjects.isEmpty()) {
                                continue;
                            }
                            int imgCount = imgObjects.size();
                            int imgsPerRow = 3;
                            int rowCount = (int) Math.ceil((double) imgCount / imgsPerRow);
                            XWPFParagraph paragraph = row.getCell(k).addParagraph();

                            for (int rowIndex = 0; rowIndex < rowCount; rowIndex++) {
                                for (int colIndex = 0; colIndex < imgsPerRow && rowIndex * imgsPerRow + colIndex < imgCount; colIndex++) {
                                    Object obj = imgObjects.get(rowIndex * imgsPerRow + colIndex);
                                    byte[] byteImg = Base64.getDecoder().decode(obj.toString());

                                    XWPFRun run = paragraph.createRun();
                                    run.addPicture(new ByteArrayInputStream(byteImg), XWPFDocument.PICTURE_TYPE_JPEG,
                                            "img" + (rowIndex * imgsPerRow + colIndex + 1) + ".jpg", Units.toEMU(80 / 3), Units.toEMU(80 / rowCount));
                                    run.addTab();
                                    if (colIndex >= imgsPerRow - 1) {
                                        run.addCarriageReturn();
                                    }
                                }

                                if (rowIndex < rowCount - 1) {
                                    paragraph.createRun().addBreak();
                                }
                            }
                        }else{
                            String val = (String) list.get(i).get(j-countRow).get(key);
                            XWPFTableCell cell = row.getCell(k);
                            cell.setText(val);
                        }

//                        if ("org".equals(key) ){//&& StringUtils.isNotEmpty((String) list.get(i).get(j-countRow).get(key))
//                            // 将图片插入到文档中
//                            Object obj = list.get(i).get(j-countRow).get(key);
//                            if(obj == null){
//                                continue;
//                            }
//                            byte[] byteImg = Base64.getDecoder().decode(obj.toString());
//                            XWPFParagraph paragraph = docxDocument.createParagraph();
//                            paragraph = row.getCell(k).addParagraph();
//                            XWPFRun run = paragraph.createRun();
////                            String  val = (String) list.get(i).get(j-countRow).get(key);
////                            byte[] byteImg = Base64.getDecoder().decode(val);
//                            run.addPicture(new ByteArrayInputStream(byteImg), XWPFDocument.PICTURE_TYPE_JPEG,
//                                    "image.jpg", Units.toEMU(80), Units.toEMU(80));
//                        }else{
//                            String val = (String) list.get(i).get(j-countRow).get(key);
//                            cell.setText(val);
//                        }

                    }
                }
                countRow = countRow + list.get(i).size()+1;
            }
            //遍历所有单元格设置水平居中
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    CTTc cttc = cell.getCTTc();
                    CTTcPr ctPr = cttc.addNewTcPr();
                    ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                    cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
                }
            }
            //文件生成
            String fileName = "处置日志";
            String outPath = fileName + ".docx";
            File outdocxFile = new File(outPath);
            int fileVersion = 0;
            while (outdocxFile.exists()) {
                fileVersion++;
                outPath = fileName + "(" + fileVersion + ").docx";
                outdocxFile = new File(outPath);
            }
            OutputStream fileOutputStream = null;
            //转换成byte数据格式
                fileOutputStream = new FileOutputStream(outdocxFile);
                docxDocument.write(fileOutputStream);
    //          start 转byte数组
                FileInputStream fileInputStream = new FileInputStream(outdocxFile);
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
    //                System.out.write(buffer, 0, bytesRead);
                }
                fileInputStream.close();
                fileOutputStream.close();
            outdocxFile.delete();
    //            end
        } catch (Exception e) {
            e.printStackTrace();
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 合并单元格方法
     * @param
     * @return
     */
    public static void getCellMerge(int firstCell, int lastCell, XWPFTableRow row){
        for (int cellIndex = firstCell; cellIndex <= lastCell; cellIndex++) {
            XWPFTableCell cellMerge = row.getCell(cellIndex);
            if ( cellIndex == firstCell ) {
                // The first merged cell is set with RESTART merge value
                cellMerge.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cellMerge.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }
}
