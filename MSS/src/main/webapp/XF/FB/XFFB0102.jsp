<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="window-background">
    <EF:EFPage title="信息发布通知情况" prefix="nocc">
        <style>

            /*覆盖掉nocc-head.jsp里的auto,不然表头不对齐*/
            .i-theme-nocc .k-grid:not(.k-grid-lockedcolumns) > .k-grid-header > .k-grid-header-wrap {
                width: 1280px !important;
            }

            /*表头设置固定值,防止表头偏移*/
            div.k-grid-header-wrap.k-auto-scrollable {
                width: 1279px !important;
            }
            /*直接显示y轴滚动条,隐藏x轴滚动条*/
            #ef_grid_result > div.k-grid-content.k-auto-scrollable {
                overflow-y: scroll !important;
                overflow-x: hidden;
            }

            /*主容器*/
            #container {
                width: 100%;
                height: 100%;
            }

            .tab_label {
                display: flex;
                align-items: center;
            }

            ul.k-tabstrip-items.k-reset {
                display: flex;
                align-items: center;
            }

            /*消息和电话总块*/
            .msg_phone_block {
                margin-top: 10px;
            }

            /*提示框内容样式*/
            div.kendo-modal-add-message {
                text-align: center !important;
            }

            /*二次确认框按钮位置互换*/
            div.kendo-modal-form-bottom {
                display: flex;
                flex-direction: row-reverse;
                justify-content: center;
            }

            /*去掉tab的背景色*/
            .i-theme-nocc .k-tabstrip.k-widget ul.k-tabstrip-items .k-state-active {
                background: #217bb2 !important;
                box-shadow: inset 0 -4px 13px 0 #56c2ff !important;
                position: relative;
            }
            #tabButton > ul > li {
                border: 1px solid #0EF1FF;
                box-shadow: none;
                background: none;
                /*top: 25px;*/
                /*left: 25px;*/
            }

        </style>

        <div id="container">

            <div class="tab_label">
                <EF:EFTab id="tabButton" contentType="iframe">
                    <ul>
                        <li id="msgTab">消息通知人</li>
                        <li id="phoneTab">电话通知人</li>
                    </ul>
                </EF:EFTab>
                <div id="button_group" style="margin-left: 910px;display: flex;">
                    <EF:EFButton ename="export" cname="导出"></EF:EFButton>
                    <div id="replayButton" style="display: none;">
                        <EF:EFButton ename="replay" cname="一键重播"></EF:EFButton>
                    </div>
                </div>
            </div>

            <div class="msg_phone_block">
                <EF:EFInput ename="inqu_status-0-fdInfoHistoryId" cname="消息id" type="hidden"></EF:EFInput>
                <EF:EFInput ename="inqu_status-0-fdType" cname="类型" type="hidden"></EF:EFInput>
                <div class="msg_block">
                    <EF:EFRegion head="hidden" style="border:none !important">
                        <EF:EFGrid blockId="result" autoDraw="no" autoBind="false" height="380" enable="false"
                                   checkMode="single" rowNo="false" sort="setted" pagerPosition="bottom">
                            <EF:EFColumn ename="fdUuids" cname="uuid" hidden="true"/>
                            <EF:EFColumn ename="fdInfoHistoryUuid" cname="绑定的发布id" hidden="true" enable="false"/>
                            <EF:EFColumn ename="fdNumberId" cname="钉钉id" hidden="true" enable="false"/>
                            <EF:EFColumn ename="fdType" cname="类型" hidden="true" enable="false"/>
                            <EF:EFColumn ename="num" cname="序号" align="center" width="60" enable="false"/>
                            <EF:EFColumn ename="post" cname="岗位" width="190" align="center" enable="false"/>
                            <EF:EFColumn ename="name" cname="姓名" width="160" align="center" enable="false"/>
                            <EF:EFColumn ename="phoneNumber" cname="手机号" width="190" align="center" enable="false"/>
                            <EF:EFComboColumn ename="fdPublishResult" cname="发布状态" width="150" textField="textField" valueField="valueField" enable="false" style="text-align:center">
                                <EF:EFOption label="发布失败" value="0"/>
                                <EF:EFOption label="发布成功" value="1"/>
<%--                                <EF:EFOption label="未反馈" value="2"/>--%>
                            </EF:EFComboColumn>
                            <EF:EFColumn ename="publishTime" cname="发布时间" width="200" align="center" enable="false" style="white-space: normal;"/>
                            <EF:EFComboColumn ename="receiveResult" cname="响应状态" width="120" textField="textField" valueField="valueField" enable="false" style="text-align:center">
                                <EF:EFOption label="未接听" value="0"/>
                                <EF:EFOption label="已接听" value="1"/>
                            </EF:EFComboColumn>
                            <EF:EFColumn ename="replyTime" cname="响应时间" width="200" align="center" enable="false"/>
                        </EF:EFGrid>
                    </EF:EFRegion>
                </div>
            </div>

        </div>
    </EF:EFPage>
</div>