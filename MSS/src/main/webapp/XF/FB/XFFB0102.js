$(function () {

    /**
     * 页面加载时获取父页面传过来的值
     */
    $(window).load(function () {
        //获取父窗口传的值
        var fdUuids = IPLAT.getTransValue("fdUuids", "releaseInfoDetailWindow");
        //将值传给隐藏输入框,用于点击tab按钮切换时使用,表明时该条信息发布记录的消息通知和电话通知
        $("#inqu_status-0-fdInfoHistoryId").val(fdUuids);
        //隐藏响应时间和响应状态列
        $("#ef_grid_result").data("kendoGrid").hideColumn("receiveResult");
        $("#ef_grid_result").data("kendoGrid").hideColumn("replyTime");
    });

    /**
     * 点击消息通知按钮
     */
    $("#msgTab").on("click", function () {
        $("#msgTab").css("color", "rgba(255,255,255,1)");
        $("#phoneTab").css("color", "rgba(255,255,255,0.5)");
        $("#replayButton").css("display","none");
        $("#button_group").css("margin-left", "910px");
        //0表示消息通知
        $("#inqu_status-0-fdType").val("0");
        //表格刷新
        resultGrid.dataSource.page(1);
        //隐藏响应时间和响应状态列
        $("#ef_grid_result").data("kendoGrid").hideColumn("receiveResult");
        $("#ef_grid_result").data("kendoGrid").hideColumn("replyTime");
    });

    /**
     * 点击电话通知按钮
     */
    $("#phoneTab").on("click", function () {
        $("#phoneTab").css("color", "rgba(255,255,255,1)");
        $("#msgTab").css("color", "rgba(255,255,255,0.5)");
        $("#replayButton").css("display","block");
        $("#button_group").css("margin-left", "790px");
        //1表示电话通知
        $("#inqu_status-0-fdType").val("1");
        //表格刷新
        resultGrid.dataSource.page(1);
        //显示响应时间和响应状态列
        $("#ef_grid_result").data("kendoGrid").showColumn("receiveResult");
        $("#ef_grid_result").data("kendoGrid").showColumn("replyTime");
    });

    /**
     * 导出
     */
    $("#export").on("click", function () {
        IPLAT.confirm({
            title: '确认',
            message: '<b>是否导出？</b>',
            okFn: function (e) {
                var inInfo = resultGrid.wrapEiBlock();
                EiCommunicator.send("XFFB0102", "export", inInfo, {
                    onSuccess: function (response) {
                        IPLAT.alert({
                            message: '<b>'+response.msg+'</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                    },
                });
            }
        });
    });

    /**
     * 一键重播
     */
    $("#replay").on("click", function () {
        IPLAT.confirm({
            title: '确认',
            message: '<b>是否一键重播？</b>',
            okFn: function (e) {
                var rows = resultGrid.getDataItems();
                var uuids = rows[0].fdInfoHistoryUuid;
                var inInfo = new EiInfo();
                inInfo.set("uuids", uuids);
                EiCommunicator.send("XFFB0102", "replay", inInfo, {
                    onSuccess: function (response) {
                        IPLAT.alert({
                            message: '<b>'+response.msg+'</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                    },
                });
            }
        });
    });

    IPLATUI.EFGrid = {
        "result": {
            columns: [ //自定义表格字段
                {
                    field: "publishTime",
                    title: "发布时间",
                    width: "200",
                    template: function (e) {
                        console.log(e)
                        let type = e.fdType;
                        var publishTime = e.publishTime;
                        if (type == '1' && publishTime.length > 25) {
                            let str = '';
                            let timeArray = publishTime.split(',');
                            for (let i = 0; i < timeArray.length; i++) {
                                str = str + timeArray[i] + '\n'
                            }
                            publishTime = str;
                            console.log(timeArray)
                        }
                        return publishTime;
                    }
                },
            ],
            exportGrid: false, //隐藏右侧自定义导出按钮
            pageable: {
                pageSize: 10, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                // pageSizes: [10, 20, 50, 100] // "all"] // 分页配置
            },
        },
    };

});

/**
 * 重播事件
 * @param uuids-事件id
 * @param numberId-钉钉id数组
 */
// var replayFunction = function (uuids, numberId) {
//     if (uuids == null || uuids == "" || uuids === undefined) {
//         IPLAT.alert({
//             message: '<b>主键丢失,请重新操作</b>',
//             okFn: function (e) {},
//             title: '提示'
//         });
//         return;
//     }
//     var inInfo = new EiInfo();
//     inInfo.set("uuids", uuids);
//     inInfo.set("numberId", numberId);
//     EiCommunicator.send("XFFB0102", "replay", inInfo, {
//         onSuccess: function (response) {
//             IPLAT.alert({
//                 message: response.msg,
//                 okFn: function (e) {},
//                 title: '提示'
//             });
//         },
//     });
// }
