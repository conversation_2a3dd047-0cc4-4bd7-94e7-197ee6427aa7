var templateEventFlag = false; // 表示是否改变模板名称值
var skipFlag = false; //判断是否从应急处置跳转过来
$(function () {

    /**
     * 点击NOCC发布按钮时改变NOCC和OCC的字体颜色，并查询NOCC下的信息
     */
    $("#NOCCPublish").on("click", function () {
        $("#inqu_status-0-publishTarget").val("40050001");
        //显示查看列
        $("#ef_grid_result").data("kendoGrid").showColumn("check");
        resultGrid.dataSource.page(1);
    });

    /**
     * 点击OCC发布按钮时改变OCC和NOCC的字体颜色，并查询OCC下的信息
     */
    $("#OCCPublish").on("click", function () {
        $("#inqu_status-0-publishTarget").val("40050002");
        //隐藏查看列
        $("#ef_grid_result").data("kendoGrid").hideColumn("check");
        resultGrid.dataSource.page(1);
    });

    /*页面加载时触发函数*/
    $(window).on("load", function () {
        //获取登录人账号
        userName = IPLAT.getParameterByName("userName");
        //当日凌晨到次日凌晨
        let nowDt = timeFormat(new Date(new Date(new Date().toLocaleDateString()).getTime()));
        let lastDt = timeFormat(new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 ));
        $("#inqu_status-0-startTime").val(nowDt);
        $("#inqu_status-0-endTime").val(lastDt);

        //模板名称搜索框加上提示文字
        let modelNameFilter = document.querySelector(".k-list-filter > .k-textbox");
        modelNameFilter.placeholder = '请输入模板名称';

        //禁用表头的多选框
        $(".check-all").attr("disabled", true);

        //判断url是否存在eventFrom参数,没有就是正常页面打开,有就是其他页面打开信息发布页面
        let paramIndex = window.location.href.indexOf("options");
        if (paramIndex != -1) {
            let url = window.location.href;
            let options = url.split('options=')[1];
            let decodeParam =  decodeURIComponent(window.atob(options))
            let jsonOptions = JSON.parse(decodeParam);
            let data = jsonOptions.active;
            let eventSource = data.eventSource;
            let eventId = data.eventId;
            if (isNullAndEmpty(eventId)) {
                eventSource = '1';
            }
            skipFlag = true;
            //获取应急演练或者应急事件的名称回填至下拉框
            getEmergencyData(eventSource,eventId);
            //根据eventSource和eventId获取应急事件或者演练的记录,根据记录的数据回填
            getEmergencyAndBackFill(eventSource,eventId);
        }

        //通知跳转
        notificationSkipToPage();

        //监听点击事件,点击就关闭显示弹窗
        document.addEventListener('click', function(event) {
            $("#showInputText").hide();
        });

    });

    /**
     * 获取应急数据
     * @param eventSource-区分演练还是事件
     * @param eventId-表示id
     */
    function getEmergencyData(eventSource,eventId) {
        let inInfo = new EiInfo();
        inInfo.set("eventSource", eventSource);
        EiCommunicator.send("XFFB01", "getEmergencyData", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() === -1) {
                    IPLAT.alert({
                        message: '<b>' + response.msg + '</b>',
                        title: '提示'
                    });
                    return;
                }
                //选择多选框
                if (eventSource == '1') {
                    $(':checkbox[name="infoForm-0-emergencyEvent"]').prop('checked', true);
                    $("#eventsBlock").css("display","flex");
                } else {
                    $(':checkbox[name="infoForm-0-emergencyDrill"]').prop('checked', true);
                    $("#eventsBlock").css("display","flex");
                }
                //数据源配置和值设置
                $("#infoForm-0-emergencyEvents").kendoDropDownList({
                    //下拉框数据源
                    dataSource: response.extAttr.eventList,
                    dataTextField: "eventName", //显示值
                    dataValueField: "eventId" //真实值
                });
                IPLAT.EFSelect.value($("#infoForm-0-emergencyEvents"), eventId);
            },
        });
    }

    /**
     * 根据eventSource和eventId获取应急事件或者演练的记录,根据记录的数据回填
     * @param eventSource-区分演练还是事件
     * @param eventId-表示id
     */
    function getEmergencyAndBackFill(eventSource,eventId) {
        let inInfo = new EiInfo();
        inInfo.set("eventSource", eventSource);
        inInfo.set("eventId", eventId);
        EiCommunicator.send("XFFB01", "getEmergencyAndBackFill", inInfo, {
            onSuccess: function (response) {
                if (response.getStatus() === -1) {
                    IPLAT.alert({
                        message: '<b>' + response.msg + '</b>',
                        title: '提示'
                    });
                    return;
                }
                //数据回填
                let data = response.extAttr.dataMap;
                console.log(data)
                IPLAT.EFInput.value($("#infoForm-0-fdMsgAddressContent"), data.msgNoticeName);
                IPLAT.EFInput.value($("#infoForm-0-fdPhoneAddressContent"), data.phoneNoticeName);
                $("#infoForm-0-fdMsgAddressValue").val(data.msgNoticeObj);
                $("#infoForm-0-fdPhoneAddressValue").val(data.phoneNoticeObj);
                IPLAT.EFSelect.value($("#infoForm-0-fdPublishType"), "0");
                //查询模板类型后回填
                queryTemplateScene(data.msgTypeCode, data.msgName, data.msgStage, "");

            },
        });
    }

    /*查询方法*/
    $("#QUERY").on("click", function (e) {
        resultGrid.dataSource.page(1);
    });

    /*enter查询方法*/
    $("#inqu_status-0-publishContent").keydown(function(event) {
        if (event.which === 13) {
            resultGrid.dataSource.page(1);
        }
    });

    /*弹出消息通知窗口*/
    $("#infoPlus").on("click", function () {
        IPLAT.ParamWindow({
            id: "insertInfoRecipient",
            formEname: "XFFB0101",
            params: "msgDataObject="
            // params: "msgDataObject=" + IPLAT._encodeURI($("#infoForm-0-fdMsgAddressValue").val())
        })
    });

    /*弹出电话通知窗口*/
    $("#phonePlus").on("click", function () {
        IPLAT.ParamWindow({
            id: "insertPhoneRecipient",
            formEname: "XFFB0101",
            params: "phoneDataObject="
            // params: "phoneDataObject=" + IPLAT._encodeURI($("#infoForm-0-fdPhoneAddressValue").val())
        })
    });

    /*清空表单数据*/
    $("#CLEAR").on("click",function (e) {
        IPLAT.confirm({
            title: '确认',
            message: '<b>是否清空输入框的数据？</b>',
            okFn: function (e) {
                //清空输入框值
                clearData();
            }
        });
    });

    /**
     * 信息发布
     * 点击发布按钮后校验内容是否超过300个字，然后向后端发送数据块
     */
    $("#RELEASE").on("click",function (e) {
        if (isNullAndEmpty($("#infoForm-0-fdContent").val())) {
            IPLAT.alert({
                message: '<b>内容不能为空</b>',
                okFn: function (e) {},
                title: '提示'
            });
            return;
        }
        //判断内容字数是否超300个字，超过后弹提示框
        if ($("#infoForm-0-fdContent").val().length > 300) {
            IPLAT.alert({
                message: '<b>内容字数不能超过300</b>',
                okFn: function (e) {},
                title: '提示'
            });
            return;
        };
        var inInfo = new EiInfo();
        IPLAT.confirm({
            title: '确认',
            message: '<b>是否发布？</b>',
            okFn: function (e) {
                //消息通知数据设置
                var msgStrValue = $("#infoForm-0-fdMsgAddressValue").val();
                //判断是否有选择数据
                if (!isNullAndEmpty(msgStrValue)) {
                    var msgJsonValue = JSON.parse(msgStrValue);
                    inInfo.set("fdMsgDept", msgJsonValue.dept.join());
                    inInfo.set("fdMsgDeptPerson", msgJsonValue.deptPerson.join());
                    inInfo.set("fdMsgGroup", msgJsonValue.group.join());
                    inInfo.set("fdMsgGroupPerson", msgJsonValue.groupPerson.join());
                } else {
                    //传空值是为了让后端拿到这个key，不然报错
                    inInfo.set("fdMsgDept", "");
                    inInfo.set("fdMsgDeptPerson", "");
                    inInfo.set("fdMsgGroup", "");
                    inInfo.set("fdMsgGroupPerson", "");
                }
                //电话通知数据设置
                var phoneStrValue = $("#infoForm-0-fdPhoneAddressValue").val();
                //判断是否有选择数据，没有就不用设置回填，不然报错
                if (!isNullAndEmpty(phoneStrValue)) {
                    var phoneJsonValue = JSON.parse(phoneStrValue);
                    inInfo.set("fdPhoneDept", phoneJsonValue.dept.join());
                    inInfo.set("fdPhoneDeptPerson", phoneJsonValue.deptPerson.join());
                    inInfo.set("fdPhoneGroup", phoneJsonValue.group.join());
                    inInfo.set("fdPhoneGroupPerson", phoneJsonValue.groupPerson.join());
                } else {
                    //传空值是为了让后端拿到这个key，不然报错
                    inInfo.set("fdPhoneDept", "");
                    inInfo.set("fdPhoneDeptPerson", "");
                    inInfo.set("fdPhoneGroup", "");
                    inInfo.set("fdPhoneGroupPerson", "");
                }
                //数据块设置
                inInfo.setByNode("infoForm");

                //判断是否选择同步应急事件或同步应急演练,是设置参数
                var emergencyEvent = checkBoxIsSelect("infoForm-0-emergencyEvent");
                var emergencyDrill = checkBoxIsSelect("infoForm-0-emergencyDrill");
                if (emergencyEvent || emergencyDrill) {
                    inInfo.set("emergencyFlag", "true");
                    if (emergencyEvent) {
                        inInfo.set("eventSource", $('input:checkbox[name="infoForm-0-emergencyEvent"]:checked').val());
                    } else if (emergencyDrill) {
                        inInfo.set("eventSource", $('input:checkbox[name="infoForm-0-emergencyDrill"]:checked').val());
                    }
                    inInfo.set("eventId", $("#infoForm-0-emergencyEvents").val());
                    inInfo.set("eventName", IPLAT.EFSelect.text($("#infoForm-0-emergencyEvents")));
                }
                inInfo.set("userName", userName);
                //发送请求：1接口名 2接口里的方法名 3参数
                EiCommunicator.send("XFFB01", "infoRelease", inInfo, {
                    onSuccess: function (response) {
                        IPLAT.progress($('#' + IPLATUI.FORM_ENAME), false);
                        var status = response.getStatus();
                        if (status == "-1") {
                            IPLAT.confirm({
                                message: response.msg,
                                okFn: function (e) {
                                },
                                title: '提示'
                            });
                            return;
                        }
                        IPLAT.alert({
                            message: response.msg,
                            okFn: function (e) {
                                //操作成功时表格刷新
                                // resultGrid.dataSource.page(1);
                                location.reload();
                                //清空表单数据
                                clearData();
                            },
                            title: '提示'
                        });
                    },
                });
            }
        });
    });

    /*下拉选择框选择事件*/
    IPLATUI.EFSelect = {
        "infoForm-0-fdTemplateClass": {
            select: function (e) {
                //当选中的模板真实值为空时清空发布类型下拉框的数据源，有值时就发送请求获取发布类型数据源
                if (e.dataItem.valueField != "") {
                    //查询模板类型后回填
                    queryTemplateScene(e.dataItem.valueField,"","","");
                } else {//清空
                    let dataSource = new kendo.data.DataSource({
                        data: []
                    });
                    IPLAT.EFSelect.setDataSource($("#infoForm-0-fdTemplateEvent"), dataSource);
                    IPLAT.EFSelect.setDataSource($("#infoForm-0-fdPublishPhase"), dataSource);
                    IPLAT.EFInput.value($("#infoForm-0-fdContent"), "");
                    //重新给模板名称赋值
                    initTemplateName();
                }
            }
        }
    };

    /**
     * 选择线路
     */
    $("#infoForm-0-fdPublishLine").on("change", function (e) {
        var selectedText = $(this).find("option:selected").map(function() {
            return $(this).text();
        }).get().join(",");
        IPLAT.EFInput.value($("#infoForm-0-fdPublishLineText"), selectedText);
        console.log(selectedText);
    });


    /**
     * 勾选获取数据后将应急事件或者应急演练重新获取
     */
    $("#infoForm-0-emergencyEvents").on("change", function () {
        if (this.value === "") {
            //默认应急事件
            let eventSource = "1";
            let emergencyDrill = $("input[name = 'infoForm-0-emergencyDrill']:checked").val();
            if (!isNullAndEmpty(emergencyDrill)) {
                eventSource = emergencyDrill;
            }
            getEmergencyData(eventSource);
        }
    });


    IPLATUI.EFGrid = {
        "result": {
            exportGrid: false, //隐藏右侧自定义导出按钮
            columns: [ //自定义表格字段
                {
                    field: "check",
                    title: "通知人",
                    width: "20",
                    template: function (e) {
                        var html = '<a href="javascript:void(0);" onclick="showReleaseInfo(\'' + e.fdUuids + '\')">'+"查看"+'</a>';
                        return html;
                    }
                },
            ],
            pageable: {
                pageSize: 20, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                // pageSizes: [10, 20, 50, 100] // "all"] // 分页配置
            },
            onCheckRow: function (e) {
                //隐藏显示框
                $("#showInputText").hide();
                //点击发布历史记录，将发布内容回填到信息发布框的内容中
                if (e.checked == true) {
                    //设置字数
                    totalChar(e.model.fdContent.length);
                    // IPLAT.EFInput.value($("#infoForm-0-fdContent"), e.model.fdContent);
                    IPLAT.EFInput.value($("#infoForm-0-fdMsgAddressContent"), e.model.fdMsgAddressContent);
                    IPLAT.EFInput.value($("#infoForm-0-fdPhoneAddressContent"), e.model.fdPhoneAddressContent);
                    IPLAT.EFSelect.value($("#infoForm-0-fdTemplateClass"), e.model.fdTemplateClass);
                    IPLAT.EFSelect.value($("#infoForm-0-fdPublishType"), e.model.fdPublishType);
                    if (!isNullAndEmpty(e.model.fdPublishLine)) {
                        $("#infoForm-0-fdPublishLine").data("kendoMultiSelect").value(e.model.fdPublishLine.split(","));
                        var selectedText = $("#infoForm-0-fdPublishLine").find("option:selected").map(function() {
                            return $(this).text();
                        }).get().join(",");
                        //回填线路名称
                        IPLAT.EFInput.value($("#infoForm-0-fdPublishLineText"), selectedText);
                    }
                    //查询模板类型后回填
                    queryTemplateScene(e.model.fdTemplateClass, e.model.fdTemplateEvent, e.model.fdPublishPhase, e.model.fdContent);
                    //将消息通知和电话通知的所有数据获取并组装成JSON数据
                    msgJSONData(e.model);
                    phoneJSONData(e.model);
                    //回填绑定应急数据
                    backfillEmergencyData(e.model);
                } else {
                    //清空数据
                    // clearData();
                }
            },
            onRowClick: function (e) {
                //点击行后勾选该行
                resultGrid.setCheckedRows(e.row);
            },
        }
    };

    /**
     * 回填消息通知窗口数据
     * @param data
     */
    var msgJSONData = function (data) {
        var group = [];
        var groupPerson = [];
        var dept = [];
        var deptPerson = [];
        var text = [];
        if (!isNullAndEmpty(data.fdMsgGroup)) {
            group = data.fdMsgGroup.split(",");
        }
        if (!isNullAndEmpty(data.fdMsgGroupPerson)) {
            groupPerson = data.fdMsgGroupPerson.split(",");
        }
        if (!isNullAndEmpty(data.fdMsgDept)) {
            dept = data.fdMsgDept.split(",");
        }
        if (!isNullAndEmpty(data.fdMsgDeptPerson)) {
            deptPerson = data.fdMsgDeptPerson.split(",");
        }
        if (!isNullAndEmpty(data.fdMsgAddressContent)) {
            text = data.fdMsgAddressContent.split(",");
        }
        //消息通知对象
        var msgDataObject = {
            "group": group,
            "groupPerson": groupPerson,
            "dept": dept,
            "deptPerson": deptPerson,
            "text": text
        }
        //直接回填到input隐藏框中，打开窗口时传直接获取input标签就行了
        $("#infoForm-0-fdMsgAddressValue").val(JSON.stringify(msgDataObject));
    }

    /**
     * 回填电话通知窗口数据
     * @param data
     */
    var phoneJSONData = function (data) {
        var group = [];
        var groupPerson = [];
        var dept = [];
        var deptPerson = [];
        var text = [];
        if (!isNullAndEmpty(data.fdPhoneGroup)) {
            group = data.fdPhoneGroup.split(",");
        }
        if (!isNullAndEmpty(data.fdPhoneGroupPerson)) {
            groupPerson = data.fdPhoneGroupPerson.split(",");
        }
        if (!isNullAndEmpty(data.fdPhoneDept)) {
            dept = data.fdPhoneDept.split(",");
        }
        if (!isNullAndEmpty(data.fdPhoneDeptPerson)) {
            deptPerson = data.fdPhoneDeptPerson.split(",");
        }
        if (!isNullAndEmpty(data.fdPhoneAddressContent)) {
            text = data.fdPhoneAddressContent.split(",");
        }
        //电话通知对象
        var phoneDataObject = {
            "group": group,
            "groupPerson": groupPerson,
            "dept": dept,
            "deptPerson": deptPerson,
            "text": text
        }
        //直接回填到input隐藏框中，打开窗口时传直接获取input标签就行了
        $("#infoForm-0-fdPhoneAddressValue").val(JSON.stringify(phoneDataObject));
    }

    /**
     * 回填绑定的应急数据
     * @param data
     */
    var backfillEmergencyData = function (data) {
        if (!isNullAndEmpty(data.eventId)) {
            //判断类型是事件还是演练,勾选并显示下拉框
            if (data.eventType === '1') {
                $(':checkbox[name="infoForm-0-emergencyEvent"]').prop('checked', true);
                $(':checkbox[name="infoForm-0-emergencyDrill"]').prop('checked', false);
            } else if(data.eventType === '2') {
                $(':checkbox[name="infoForm-0-emergencyDrill"]').prop('checked', true);
                $(':checkbox[name="infoForm-0-emergencyEvent"]').prop('checked', false);
            }
            $("#eventsBlock").css("display","flex");
            //还得判断emergencyResult里是否存在该应急事件或演练,不存在得将该记录加进去再回填
            $("#infoForm-0-emergencyEvents").kendoDropDownList({
                //下拉框数据源
                dataSource: [
                        {
                            eventName: "--获取数据--",
                            eventId: ""
                        },
                        {
                            eventName:data.eventName,
                            eventId:data.eventId
                        }
                    ],
                dataTextField: "eventName", //显示值
                dataValueField: "eventId" //真实值
            });
            IPLAT.EFSelect.value($("#infoForm-0-emergencyEvents"), data.eventId);
        } else {
            $(':checkbox[name="infoForm-0-emergencyEvent"]').prop('checked', false);
            $(':checkbox[name="infoForm-0-emergencyDrill"]').prop('checked', false);
            $("#eventsBlock").css("display","none");
        }
    }

    /**
     * 查询模板名称（事件情景）
     * @param templateType-事件情景
     * @param templateName-模板名称
     * @param publishPhase-发布阶段
     * @param content-发布内容
     */
    var queryTemplateScene = function(templateType,templateName,publishPhase,content)  {
        //判断模板类型是否为空,为空就是预警发布传过来的值,无模板类型、名称和发布类型
        if (!isNullAndEmpty(templateType)) {
            var inInfo = new EiInfo();
            //传入模板真实值
            inInfo.set("fdType", templateType);
            //发送请求
            EiCommunicator.send("XFFB01", "queryTemplateScene", inInfo, {
                onSuccess: function (response) {
                    let sceneResult = response.extAttr.data;
                    let dataArray = [];
                    sceneResult.forEach(data => {
                        let options = {
                            "textField": data.fdScene,
                            "valueField": data.fdScene
                        }
                        dataArray.push(options);
                    })
                    let dataSource = new kendo.data.DataSource({
                        data: dataArray
                    });
                    IPLAT.EFSelect.setDataSource($("#infoForm-0-fdTemplateEvent"), dataSource);
                    /**判断是下拉框级联还是数据列表的回填,如果是级联,则一层一层赋值就行;
                     * 如果是数据列表回填,则数据源获取之后将从列表获取的值回填
                     **/
                    if (!isNullAndEmpty(templateName)) {
                        IPLAT.EFSelect.value($("#infoForm-0-fdTemplateEvent"), templateName);
                    } else {
                        IPLAT.EFSelect.value($("#infoForm-0-fdTemplateEvent"), sceneResult[0].fdScene);
                        //如果事件情景只有一条数据情况下，发布阶段和内容直接回填
                        if (sceneResult.length == 1) {
                            let publishPhaseArray = [];
                            let singleData = {
                                "textField": sceneResult[0].fdClass,
                                "valueField": sceneResult[0].fdClass
                            }
                            publishPhaseArray.push(singleData);
                            let publishPhaseDataSource = new kendo.data.DataSource({
                                data: publishPhaseArray
                            });
                            IPLAT.EFSelect.setDataSource($("#infoForm-0-fdPublishPhase"), publishPhaseDataSource);
                            IPLAT.EFInput.value($("#infoForm-0-fdContent"), sceneResult[0].fdContent);
                        }
                    }
                    //直接全部级联下去
                    getPhase(publishPhase,content);
                }
            });
        } else {
            let dataSource = new kendo.data.DataSource({
                data: []
            });
            IPLAT.EFSelect.setDataSource($("#infoForm-0-fdTemplateEvent"), dataSource);
            IPLAT.EFSelect.setDataSource($("#infoForm-0-fdPublishPhase"), dataSource);
            IPLAT.EFInput.value($("#infoForm-0-fdContent"), content);
        }
    }

    /**
     * 根据事件情景（模板名称）和类型查询发布阶段
     */
    $("#infoForm-0-fdTemplateEvent").on("change", function () {
        //先判断模板分类是否为空,为空表示根据模板名称进行选择
        let templateEvent = $("#infoForm-0-fdTemplateClass").val();
        if (isNullAndEmpty(templateEvent)) {
            //修改值
            templateEventFlag = true;
        }
        //判断如果值为空,把发布阶段和内容清空
        if (isNullAndEmpty(this.value)) {
            let publishPhaseDataSource = new kendo.data.DataSource({
                data: []
            });
            IPLAT.EFSelect.setDataSource($("#infoForm-0-fdPublishPhase"), publishPhaseDataSource);
            IPLAT.EFInput.value($("#infoForm-0-fdContent"), "");
        } else {
            getPhase("");
        }
    });

    /**
     * 获取发布阶段函数
     * @param publishPhase-发布阶段
     * @param content-发布内容
     */
    var getPhase = function(publishPhase,content) {
        let inInfo = new EiInfo();
        let templateEvent = $("#infoForm-0-fdTemplateEvent").val();
        inInfo.set("fdScene", templateEvent);
        //判断:如果是通过模板名称进行级联,将不设置fdType参数
        let templateClass = $("#infoForm-0-fdTemplateClass").val();
        if (!templateEventFlag && !isNullAndEmpty(templateClass)) {
            inInfo.set("fdType", templateClass);
        }
        templateEventFlag = false;
        EiCommunicator.send("XFFB01", "queryTemplateContent", inInfo, {
            onSuccess: function (response) {
                let publishPhaseData = response.extAttr.data;
                let publishPhaseArray = [];
                publishPhaseData.forEach(data => {
                    let options = {
                        "textField": data.fdClass,
                        "valueField": data.fdClass
                    }
                    publishPhaseArray.push(options);
                })
                //去重
                let uniqueArray = Array.from(
                    new Map(publishPhaseArray.map(item => [item.textField, item])).values()
                );
                let publishPhaseDataSource = new kendo.data.DataSource({
                    data: uniqueArray
                });
                IPLAT.EFSelect.setDataSource($("#infoForm-0-fdPublishPhase"), publishPhaseDataSource);
                //发布阶段回填(看以后是否有一对一),如果一对一就直接回填内容
                //判断是否为列表回填,是直接列表数据回填;否则是级联回填,如果级联回填的数据只有1条,则直接回填内容
                if (!isNullAndEmpty(publishPhase)) {
                    IPLAT.EFSelect.value($("#infoForm-0-fdPublishPhase"), publishPhase);
                } else {
                    IPLAT.EFSelect.value($("#infoForm-0-fdPublishPhase"), publishPhaseData[0].fdClass);
                    if (response.extAttr.data.length == 1) {
                        //回填内容
                        //todo 新版模板需要改成cmpContent
                        IPLAT.EFInput.value($("#infoForm-0-fdContent"), response.extAttr.data[0].fdContent);
                        totalChar(response.extAttr.data[0].fdContent.length);
                    }
                }
                //直接全部级联下去
                getContent(content);
            }
        });
    }

    /**
     * 根据事件情景（模板名称）、类型和发布阶段查询内容
     */
    $("#infoForm-0-fdPublishPhase").on("change", function () {
        getContent("");
    });

    /**
     * 获取内容函数
     * @param content-内容
     */
    var getContent = function(content) {
        //判断内容是否为空,不为空就是列表数据回填,不需要请求接口返回值,直接从列表回填
        if (!isNullAndEmpty(content)) {
            IPLAT.EFInput.value($("#infoForm-0-fdContent"),content);
        } else {
            let inInfo = new EiInfo();
            inInfo.set("fdScene", $("#infoForm-0-fdTemplateEvent").val());
            //判断:如果是通过模板名称进行级联,将不设置fdType参数
            if (!templateEventFlag) {
                inInfo.set("fdType", $("#infoForm-0-fdTemplateClass").val());
                templateEventFlag = false;
            }
            inInfo.set("fdClass", $("#infoForm-0-fdPublishPhase").val());
            EiCommunicator.send("XFFB01", "queryTemplateContent", inInfo, {
                 onSuccess: function (response) {
                    let fdMajor = response.extAttr.data[0].fdMajor
                    //组织JSON数组,回填专业群组,还需要判断是否从处置过来
                    if (!isNullAndEmpty(fdMajor) && !skipFlag) {
                        let majorName = '';
                        //查询全部专业
                        let allMajor = response.extAttr.allMajor;
                        let majorMap = new Map();
                        for (let i = 0; i < allMajor.length; i++) {
                            majorMap.set(allMajor[i].profession_id,allMajor[i].profession);
                        }
                        let fdMajorArray = fdMajor.split(',');
                        for (let i = 0; i < fdMajorArray.length; i++) {
                            majorName = majorMap.get(fdMajorArray[i]) +","+ majorName;
                        }
                        if (majorName.endsWith(',')) {
                            majorName = majorName.slice(0, -1);
                        }
                        //给输入框设置值
                        IPLAT.EFInput.value($("#infoForm-0-fdMsgAddressContent"), majorName);
                        IPLAT.EFInput.value($("#infoForm-0-fdPhoneAddressContent"), majorName);
                        let obj = {
                            "fdMsgGroup": fdMajor,
                        };
                        let obj2 = {
                            "fdPhoneGroup": fdMajor,
                        };
                        msgJSONData(obj);
                        phoneJSONData(obj2);
                    }
                    skipFlag = false;
                    //回填内容
                    //todo 新版模板需要改成cmpContent
                    IPLAT.EFInput.value($("#infoForm-0-fdContent"), response.extAttr.data[0].fdContent);
                    //通过模板名称回填模板分类
                    IPLAT.EFSelect.value($("#infoForm-0-fdTemplateClass"), response.extAttr.data[0].fdType);
                    totalChar(response.extAttr.data[0].fdContent.length);
                }
            });
        }
    }

    /**
     * 初始化模板名称
     */
    function initTemplateName() {
        var inInfo = new EiInfo();
        EiCommunicator.send("XFFB01", "initTemplateScene", inInfo, {
            onSuccess: function (response) {
                let sceneResult = response.getBlock("sceneResult").getRows();
                let dataArray = [];
                for (let i = 0; i < sceneResult.length; i++) {
                    let options = {
                        "textField": sceneResult[i][0],
                        "valueField": sceneResult[i][0]
                    }
                    dataArray.push(options);
                }
                let dataSource = new kendo.data.DataSource({
                    data: dataArray
                });
                IPLAT.EFSelect.setDataSource($("#infoForm-0-fdTemplateEvent"), dataSource);
            }
        });
    }

    /*输入框鼠标移动上去时触发函数,显示输入框内容*/
    $("#infoForm-0-fdMsgAddressContent").mouseover(function (e) {
        var showText = $("#infoForm-0-fdMsgAddressContent").val();
        textDisplayBox("infoForm-0-fdMsgAddressContent", "showInputText", showText);
    });

    /*输入框鼠标移动上去时触发函数,显示输入框内容*/
    $("#infoForm-0-fdPhoneAddressContent").mouseover(function (e) {
        var showText = $("#infoForm-0-fdPhoneAddressContent").val();
        textDisplayBox("infoForm-0-fdPhoneAddressContent", "showInputText", showText);
    });

    // /*消息通知输入框鼠标移出时触发函数,隐藏输入框内容*/
    // $("#infoForm-0-fdMsgAddressContent").click(function (e) {
    //     $("#showInputText").hide();
    // });
    //
    // /*电话通知输入框鼠标移出时触发函数,隐藏输入框内容*/
    // $("#infoForm-0-fdPhoneAddressContent").click(function (e) {
    //     $("#showInputText").hide();
    // });


    /**
     * div显示输入框内容方法
     * @param inputId-input标签id
     * @param labelId-显示内容的标签id
     * @param showValue-展示值
     */
    function textDisplayBox(inputId, labelId, showValue) {
        //首先获取输入框的位置
        var inputPosition = $("#"+inputId+"").offset();
        var inputHeight = $("#"+inputId+"").outerHeight();
        // 设置div的位置
        $("#showInputText").css({
            top: inputPosition.top + inputHeight + 10, // 10是你想要的垂直间距
            left: inputPosition.left,
        });
        //将值设置到显示框中
        if (!isNullAndEmpty(showValue)) {
            $("#"+labelId+"").text(showValue);
            //显示显示框
            $("#"+labelId+"").show();
        }
    }

    /**
     * 判断多选框是否勾选
     * @param labelName-标签name
     * @returns {boolean}
     */
    function checkBoxIsSelect(labelName) {
        let result = false;
        $('input[name='+labelName+']').each(function() {
            let isChecked = $(this).prop('checked');
            if (isChecked) {
                result = true;
            }
        });
        return result;
    }

    /**
     * 应急事件选择事件
     */
    $(':checkbox[name="infoForm-0-emergencyEvent"]').change(function() {
        //判断是否勾选,如果勾选查询应急事件数据并显示下拉框;取消勾选隐藏下拉框
        if ($(this).is(":checked")) {
            $(':checkbox[name="infoForm-0-emergencyDrill"]').prop('checked', false);
            $("#eventLabel").empty().append("应急事件");
            //调用获取应急数据方法
            getEmergencyData(this.value,"");
            $("#eventsBlock").css("display","flex");
        } else {
            $("#eventsBlock").css("display","none");
        }
    });

    /**
     * 应急演练选择事件
     */
    $(':checkbox[name="infoForm-0-emergencyDrill"]').change(function() {
        //判断是否勾选,如果勾选查询应急演练数据并显示下拉框;取消勾选隐藏下拉框
        if ($(this).is(":checked")) {
            $(':checkbox[name="infoForm-0-emergencyEvent"]').prop('checked', false);
            $("#eventLabel").empty().append("应急演练");
            //调用获取应急数据方法
            getEmergencyData(this.value,"");
            $("#eventsBlock").css("display","flex");
        } else {
            $("#eventsBlock").css("display","none");
        }
    });

    /**
     * 从后台获取UUIDs判断是否是从通知跳转至该页面
     */
    function notificationSkipToPage() {
        //从后台获取UUIDs
        if (!isNullAndEmpty(__eiInfo.get("UUIDs"))) {
            //隐藏查看列
            $("#ef_grid_result").data("kendoGrid").hideColumn("check");
            //选中occ发布tab,延迟10毫秒是为了等tab加载完之后再设置,不然会被重新覆盖
            setTimeout(function(){
                $("#tabButton").attr("aria-activedescendant", "OCCPublish");
                $("#NOCCPublish").removeClass("k-tab-on-top k-state-active");
                $("#OCCPublish").addClass("k-tab-on-top k-state-active");
            }, 10);
        }
    }

    /**
     * 时间日期转化为 yyyy:MM:dd hh:mm:ss
     * @param time
     * @returns {string}
     */
    var timeFormat = function(time) {
        var formattedDate = time.getFullYear() + '-' +

            (time.getMonth() < 9 ? '0' : '') + (time.getMonth() + 1) + '-' +

            (time.getDate() < 10 ? '0' : '') + time.getDate() + ' ' +

            (time.getHours() < 10 ? '0' : '') + time.getHours() + ':' +

            (time.getMinutes() < 10 ? '0' : '') + time.getMinutes() + ':' +

            (time.getSeconds() < 10 ? '0' : '') + time.getSeconds();

        return formattedDate;
    };

    /**
     * 清空表单数据
     */
    var clearData = function () {
        IPLAT.EFSelect.value($("#infoForm-0-fdTemplateClass"), "");
        IPLAT.EFInput.value($("#infoForm-0-fdMsgAddressContent"), "");
        IPLAT.EFInput.value($("#infoForm-0-fdMsgAddressValue"), "");
        IPLAT.EFInput.value($("#infoForm-0-fdPhoneAddressContent"), "");
        IPLAT.EFInput.value($("#infoForm-0-fdPhoneAddressValue"), "");
        IPLAT.EFInput.value($("#infoForm-0-fdContent"), "");
        IPLAT.EFSelect.value($("#infoForm-0-fdPublishType"), "");
        let dataSource = new kendo.data.DataSource({
            data: []
        });
        IPLAT.EFSelect.setDataSource($("#infoForm-0-fdTemplateEvent"), dataSource);
        IPLAT.EFSelect.setDataSource($("#infoForm-0-fdPublishPhase"), dataSource);
        //设置模板名称搜索框的内容为空
        let modelNameFilter = document.querySelector(".k-list-filter > .k-textbox");
        modelNameFilter.value = '';
        //清空发布线路
        var payments = $("#infoForm-0-fdPublishLine").data("kendoMultiSelect");
        payments.value([]);
        IPLAT.EFInput.value($("#infoForm-0-fdPublishLineText"), "");
        //设置内容字数
        totalChar(0);
    }

    /**
     * 内容框显示字数设置
     * @param count 总数
     */
    function totalChar(count) {
        if (isNullAndEmpty(count)) {
            //更新字数显示
            total.innerHTML = 0;
        } else {
            total.innerHTML = count;
        }
    }

    //空判断
    function isNullAndEmpty(obj) {
        return obj == null || obj == "" || obj === undefined;
    }

    // 获取文本域元素
    var textarea = document.getElementById("infoForm-0-fdContent");
    // 获取用于显示字数的元素
    var total = document.getElementById("total");
    // 监听文本域的输入事件
    textarea.addEventListener("input", function() {
        // 获取文本域的内容
        var content = textarea.value;
        // 获取当前输入的字符数
        var currentCount = content.length;
        // 显示当前字符数
        total.innerHTML = currentCount;
        // 设置最大字符数限制
        var maxCount = 300;
        if (currentCount > maxCount) {
            // 如果超过了最大字符数限制，截断文本域内容为限制的长度
            textarea.value = content.substring(0, maxCount);
            // 更新字数显示
            total.innerHTML = maxCount;
        }
    });

});

/**
 * 打开信息发布记录详情窗口
 * 序号,工号,姓名,发布状态
 */
var showReleaseInfo = function (fdUuids) {
    IPLAT.ParamWindow({
        id: "releaseInfoDetail",
        formEname: "XFFB0102",
        params: "fdUuids=" + fdUuids
    })
}
