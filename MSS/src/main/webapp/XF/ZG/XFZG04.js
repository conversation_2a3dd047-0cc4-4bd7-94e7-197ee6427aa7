var directoryId = ''; //目录id，用于搜索在该目录下的文件信息
var treeNodeId = ''; //目录id,点击左侧节点时赋值
var fileId = ''; //文件信息id
var propertyPath = "TEST"
$(function () {

    /**
     * 页面加载时
     */
    $(window).on("load", function () {
        //获取全局变量
        propertyPath = __eiInfo.get("property");
    });

    /**
     * 查询按钮事件
     */
    $("#QUERY").on("click", function () {
        queryFunction();
    });

    /**
     * enter事件
     */
    $("#inqu_status-0-fileName").keydown(function(event) {
        if (event.which === 13) {
            queryFunction();
        }
    });

    /**
     * 查询方法
     */
    function queryFunction() {
        //当目录id不为空时，给控件设置值，表格刷新方法会将控件里的值传给后端
        if (!isNullAndEmpty(directoryId)) {
            $("#inqu_status-0-directoryId").val(directoryId);
        } else {
            $("#inqu_status-0-directoryId").val('');
        }
        resultGrid.dataSource.page(1);
    }

    /**
     * 上传事件
     */
    $('#UPLOAD').on('click', function () {
        // fileUrlCallBack();
        //判断是否已经选择目录节点(treeNodeId不为空)
        if (!isNullAndEmpty(treeNodeId)) {
            IPLAT.ParamWindow({
                id: "upload",
                formEname: "BIFS99",
                params: ''
            });
        } else {
            IPLAT.alert({
                message: '<b>请选择目录</b>',
                okFn: function (e) {},
                title: '提示'
            });
            return;
        }
    });

    /**
     * 下载事件
     */
    $('#DOWNLOAD').on('click', function () {
        if (isNullAndEmpty(fileId)) {
            IPLAT.alert({
                message: '<b>请选择文件</b>',
                okFn: function (e) {},
                title: '提示'
            });
            return;
        } else {
            var inInfo = new EiInfo();
            inInfo.set("fileId", fileId);
            EiCommunicator.send("XFZG04", "downloadFile", inInfo, {
                onSuccess: function (response) {
                    IPLAT.alert({
                        message: response.msg,
                        okFn: function (e) {},
                        title: '提示'
                    });
                },
            });
        }
    });

    /**
     * 表格的配置
     */
    IPLATUI.EFGrid = {
        "result": {
            exportGrid: false, //隐藏右侧自定义导出按钮
            columns: [
                {
                    field: "check",
                    width: "20",
                    template: function (e) {
                        return '<a href="javascript:void(0);" onclick="previewFile(\'' + e.fdPath + '\')">查看</a>';
                    }
                }
            ],
            //选择记录触发的函数
            onCheckRow: function (e) {
                console.log(e)
                // 勾选一条记录
                if (e.checked == true) {
                    fileId = e.model["fileId"];
                } else {
                    fileId = "";
                }
            },
            onRowClick: function (e) {
                //点击行后勾选该行
                resultGrid.setCheckedRows(e.row);
            },
            loadComplete: function () {

                /**
                 * 删除事件
                 */
                $("#DELETE").on("click", function () {
                    var rowData = resultGrid.getCheckedRows();
                    if (rowData.length != 1) {
                        IPLAT.alert({
                            message: '<b>请选择一条记录</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    var fileId = rowData[0].fileId;
                    if (isNullAndEmpty(fileId)) {
                        IPLAT.alert({
                            message: '<b>主键丢失请重试</b>',
                            okFn: function (e) {},
                            title: '提示'
                        });
                        return;
                    };
                    IPLAT.confirm('<b>请确认是否删除该文件记录</b>',  function (e) {
                        var inInfo = new EiInfo();
                        inInfo.set("fileId",fileId);
                        EiCommunicator.send("XFZG04", "deleteFile", inInfo, {
                            onSuccess: function () {
                                IPLAT.alert({
                                    message: '<b>删除成功</b>',
                                    okFn: function (e) {},
                                    title: '提示'
                                });
                                resultGrid.dataSource.page(1);
                            },
                        });
                    },function (e) {
                    },  '提示');
                });

            },
            pageable: {
                pageSize: 20, // 设置表格默认显示数据条数，DataSource设置会覆盖此处设置
                // pageSizes: [10, 20, 50, 100] // "all"] // 分页配置
            },
            query: function () {
                let inInfo = new EiInfo();
                if (!isNullAndEmpty(treeNodeId) && treeNodeId != "0") {
                    inInfo.set("inqu_status-0-directoryId", treeNodeId)
                }
                inInfo.set("inqu_status-0-fileName", $("#inqu_status-0-fileName").val());
                return inInfo;
            },
        }
    };


    /**
     * 树控件的配置
     */
    IPLATUI.EFTree = {
        "tree": {
            //隐藏的父节点
            ROOT: {value: "0", text: "文件目录", leaf: true, isRoot: true, lev: "0"},
            select: function (e) {
                //dataItem方法，传入节点DOM对象，获取结点上的数据对象
                var nodeData = this.dataItem(e.node);
                console.log(nodeData)
                //选择某个目录时查询该目录下的文件信息，传该目录真实值到后端接口
                var inInfo = new EiInfo();
                treeNodeId = nodeData.value;
                //点击根目录时查全部
                if (treeNodeId != "0") {
                    inInfo.set("nodeId", nodeData.value);
                }
                //给变量赋值，用于搜索在该目录下的文件信息
                directoryId = nodeData.value;
                //点击其他目录时，清空搜索框数据
                $("#inqu_status-0-fileName").val("");
                EiCommunicator.send("XFZG04","queryFiles", inInfo, {
                    onSuccess: function(response){
                        //自定义查询方法不能使用resultGrid.dataSource.page(1)，不然会过query()
                        //query返回的结果集合自定义查询的结果集不一样
                        //使用该方法接收后端传的block数据块就能显示自己数据块的数据
                        resultGrid.setEiInfo(response);
                    }
                });
            },
            loadComplete: function () {
                //将生成后的目录赋值给tree，用于操作目录
                tree = this;
                //全节点展开
                // $("#tree").data("kendoTreeView").expand(".k-item");
                let treeView = $("#tree").data("kendoTreeView");
                let nodes = treeView.dataSource.view();
                //根据根节点的uid获取节点元素后传给expand进行展开
                let nodeElement = treeView.findByUid(nodes[0].uid);
                $("#tree").data("kendoTreeView").expand(nodeElement);
            },
        }
    }

    /**
     * 右键对目录的操作
     */
    $("#handleMenu").kendoContextMenu({
        filter: "#tree .k-in",
        open: function (e) {
            let model = tree.dataItem(e.target);
            //目录全部显示
            document.getElementById("addNodeMenu").style.display = "block";
            document.getElementById("addChildNodeMenu").style.display = "block";
            document.getElementById("editNodeMenu").style.display = "block";
            document.getElementById("deleteNodeMenu").style.display = "block";
            //判断根目录不能添加同级目录
            if (model.lev === "0") {
                document.getElementById("addNodeMenu").style.display = "none";
            }
            //如果是2级目录就隐藏添加子目录的选项
            if (model.lev === '2') {
                document.getElementById("addChildNodeMenu").style.display = "none";
            }
        },
        select: function (e) {
            let node = e.target;
            let model = tree.dataItem(node);
            //选择的类型，新增、删除、修改
            let selectedType = $(e.item).data("type");
            //新增节点
            if (selectedType === "addSameLevelDirectory" || selectedType === "addChildDirectory") {
                //打开添加窗口
                addNodeWindow.open().center();
                //确认添加函数
                $("#ADDENTER").unbind('click').click(function () {
                    //校验节点名称
                    let validator = IPLAT.Validator({
                        id: "addNode"
                    });
                    //当节点名称不为空
                    if (validator.validate()) {
                        IPLAT.confirm({
                            title: '确认',
                            message: '<b>是否添加节点信息？</b>',
                            okFn: function (e) {
                                //存放给后端的参数
                                let inInfo = new EiInfo();
                                inInfo.set("nodeName", $("#addNodeName").val());
                                //同级目录给该目录的父节点id，子目录给当前目录id
                                //同级目录传当前选中的lev，子级目录传当前选中lev+1
                                if (selectedType === "addSameLevelDirectory") {
                                    inInfo.set("parentNodeId", model.parentId);
                                    inInfo.set("lev", model.lev);
                                } else if (selectedType === "addChildDirectory") {
                                    inInfo.set("currentNodeId", model.value);
                                    inInfo.set("lev", (1 + parseInt(model.lev)).toString());
                                }
                                //发请求
                                EiCommunicator.send("XFZG04", "insertFileDirectory", inInfo, {
                                    onSuccess: function (response) {
                                        if (response.getStatus() == "-1") {
                                            IPLAT.confirm({
                                                message: response.msg,
                                                okFn: function (e) {
                                                },
                                                title: '提示'
                                            });
                                            return;
                                        } else {
                                            IPLAT.clearNode(document.getElementById("addNodeDiv"));
                                            addNodeWindow.close();
                                            location.reload();
                                        }
                                    },
                                });
                            }
                        });
                    }
                });
            }
            //修改节点名称
            else if(selectedType === "editDirectory"){
                //回填选中节点名称
                $("#editNodeName").val(model.text);
                //打开编辑窗口
                editNodeWindow.open().center();
                //编辑确认函数
                $("#EDITENTER").unbind('click').click(function () {
                    //名称校验
                    let validator = IPLAT.Validator({
                        id: "editNode"
                    });
                    //当节点名称不为空
                    if (validator.validate()) {
                        IPLAT.confirm({
                            title: '确认',
                            message: '<b>是否修改节点信息？</b>',
                            okFn: function (e) {
                                //存放给后端的值：nodeId-节点id，nodeName-节点名称
                                let inInfo = new EiInfo();
                                inInfo.set("parentId", model.parentId);
                                inInfo.set("nodeId", model.value);
                                inInfo.set("nodeName", $("#editNodeName").val());
                                //发请求
                                EiCommunicator.send("XFZG04", "updateFileDirectory", inInfo, {
                                    onSuccess: function (response) {
                                        if (response.getStatus() == "-1") {
                                            IPLAT.confirm({
                                                message: response.msg,
                                                okFn: function (e) {
                                                },
                                                title: '提示'
                                            });
                                            return;
                                        } else {
                                            editNodeWindow.close();
                                            location.reload();
                                        }
                                    },
                                });
                            }
                        });
                    }
                });
            }
            //删除节点
            else if (selectedType === "deleteDirectory") {
                if (model.items.length != 0) {
                    IPLAT.alert({
                        message: '<b>该目录下存在其他子目录，需删除子目录</b>',
                        okFn: function (e) {},
                        title: '提示'
                    });
                    return;
                }
                //二次确认
                IPLAT.confirm({
                    title: '确认',
                    message: '<b>是否删除节点信息？</b>',
                    okFn: function (e) {
                        //存放给后端的值：nodeId-节点id
                        let inInfo = new EiInfo();
                        inInfo.set("nodeId", model.value);
                        EiCommunicator.send("XFZG04", "deleteFileDirectory", inInfo, {
                            onSuccess: function (response) {
                                if (response.getStatus() == "-1") {
                                    IPLAT.confirm({
                                        message: response.msg,
                                        okFn: function (e) {
                                        },
                                        title: '提示'
                                    });
                                    return;
                                } else {
                                    location.reload();
                                }
                            },
                        });
                    }
                });
            }
        }
    });

    /**
     * 添加节点框取消按钮事件
     */
    $("#ADDCANCEL").on('click', function () {
        addNodeWindow.close();
    });

    /**
     * 编辑节点框取消按钮事件
     */
    $("#EDITCANCEL").on('click', function () {
        editNodeWindow.close();
    });

    /**
     * 删除节点框取消按钮事件
     */
    $("#DELETECANCEL").on('click', function () {
        deleteNodeWindow.close();
    });

    /**
     * 空判断
     * @param obj 对象
     */
    function isNullAndEmpty(obj) {
        return obj == null || obj == "" || obj === undefined;
    }

});

/**
 * 预览方法
 * @param url-路径
 */
var previewFile = function (url) {
    var previewUrl = 'http://*************:80/ossrest/api/object/'+propertyPath+'/'+url+'?tenant=1';
    IPLAT.ParamWindow({
        id: "filePreview",
        formEname: "XFZG0401",
        params: 'previewFileUrl='+IPLAT._decodeURI(previewUrl)
    })
}

/**
 * 文件导入回调函数
 * * */
function fileUrlCallBack(response) {
    let fileArr = JSON.parse(response).files;
    if (fileArr.length > 1) {
        IPLAT.alert({
            message: '<b>暂不支持批量上传文件，请重新选择</b>',
            okFn: function (e) {
            },
            title: '提示'
        });
        return;
    }
    let fileName = fileArr[0]["fileName"];
    let filePath = fileArr[0]["filePath"];
    //本机测试时使用获取固定模板文件
    // let fileName = "预案模板-NOCC.xlsx";
    // let filePath = "http://************:8090/home/<USER>/files/default/FileSystemControl/project/预案模板-NOCC.xlsx";
    let eiInfo = new EiInfo();
    eiInfo.set("urlStr", filePath);
    eiInfo.set("fileName", fileName);
    //传目录节点id
    eiInfo.set("directoryId", treeNodeId);
    //调用service及服务根据自身变更
    EiCommunicator.send("XFZG04", "uploadFile", eiInfo, {
        onSuccess: function (response) {
            let msg = response.extAttr.msg;
            uploadWindow.close();
            if (response.getStatus() === 1) {
                IPLAT.alert({
                    message: '<b>文件上传成功!</b>',
                    okFn: function (e) {
                        resultGrid.dataSource.page(1);
                    },
                    title: '提示'
                });
            } else {
                IPLAT.alert({
                    message: '<b>' + response.getMsg() +'</b>',
                    okFn: function (e) {},
                    title: '提示'
                });
            }
        }
    });
}