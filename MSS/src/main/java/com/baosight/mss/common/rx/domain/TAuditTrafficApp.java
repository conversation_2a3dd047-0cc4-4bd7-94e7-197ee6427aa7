package com.baosight.mss.common.rx.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * (TAuditTrafficApp)实体类
 *
 * <AUTHOR>
 * @since 2022-09-28 16:20:01
 */
@Data
@Accessors(chain = true)
public class TAuditTrafficApp implements Serializable {
    private static final long serialVersionUID = 996081905934349267L;
    @NotBlank(message = "UUIDs不能为空")
    private String UUIDs;

    private String title;

    private String desc;

    private String publishMsg;

}

