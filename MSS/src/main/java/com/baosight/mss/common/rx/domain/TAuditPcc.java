package com.baosight.mss.common.rx.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * (TAuditPcc)实体类
 *
 * <AUTHOR>
 * @since 2022-09-28 16:20:01
 */
@Data
@Accessors(chain = true)
public class TAuditPcc implements Serializable {
    private static final long serialVersionUID = 162681185402325697L;

    @NotBlank(message = "UUIDs不能为空")
    private String UUIDs;

    private String areaSelected;

    private String title;

    private String desc;

    private String startTime;

    private String endTime;

    private Integer pccClass;

    private Integer ccMode;
}

