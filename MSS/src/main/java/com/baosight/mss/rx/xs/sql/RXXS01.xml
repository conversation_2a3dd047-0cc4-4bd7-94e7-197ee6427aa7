<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="RXXS01">
    <select id="queryUUIDs" parameterClass="java.lang.String" resultClass="java.util.HashMap">
        select fd_uuid as "UUIDs"
        from ${mssProjectSchema}.t_audit_record_r
        where fd_uuid=#UUIDs# limit 1
    </select>
</sqlMap>