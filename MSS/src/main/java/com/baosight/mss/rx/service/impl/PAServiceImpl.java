package com.baosight.mss.rx.service.impl;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.mss.common.base.RtConstant;
import com.baosight.mss.common.rx.constant.AuditFlag;
import com.baosight.mss.common.rx.constant.PublishMsg;
import com.baosight.mss.common.utils.MapUtils;
import com.baosight.mss.rx.service.IReviewRelease;

public class PAServiceImpl implements IReviewRelease {
    @Override
    public EiInfo insertAuditRecord(EiInfo inInfo) {
        inInfo.set(EiConstant.serviceName, "RX00");
        //设置方法名
        inInfo.set(EiConstant.methodName, "insertAuditPA");
        //调用新增审批记录服务
        return XLocalManager.call(inInfo);
    }

    @Override
    public EiInfo submitRelease(EiInfo inInfo) {
        return null;
    }
}
