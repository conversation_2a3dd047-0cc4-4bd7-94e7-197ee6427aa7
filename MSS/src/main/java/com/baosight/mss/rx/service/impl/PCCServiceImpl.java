package com.baosight.mss.rx.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.mss.common.constant.xf.fb.PCCPlayTypeConstant;
import com.baosight.mss.common.constant.xf.fb.PCCReleaseModeConstant;
import com.baosight.mss.common.rx.constant.AuditFlag;
import com.baosight.mss.common.rx.constant.PCCAreaTypeConstant;
import com.baosight.mss.common.utils.EiInfoBuilder;
import com.baosight.mss.common.utils.EiInfoUtil;
import com.baosight.mss.common.utils.MapUtils;
import com.baosight.mss.rx.entiy.pcc.Line;
import com.baosight.mss.rx.entiy.pcc.PCCAreaData;
import com.baosight.mss.rx.service.IReviewRelease;
import com.baosight.mss.utils.EiInfoUtils;
import com.baosight.mss.xf.fb.service.ServiceXFFB02;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * PCC策略服务
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
@Slf4j
public class PCCServiceImpl implements IReviewRelease {

	//存放线路基础数据
	private static Map<String, String> lineBaseData = new HashMap();

    @Override
    public EiInfo insertAuditRecord(EiInfo inInfo) {
        inInfo.set(EiConstant.serviceName, "RX00");
        //设置方法名
        inInfo.set(EiConstant.methodName, "insertAuditPCC");
        //调用新增审批记录服务
        return XLocalManager.call(inInfo);
    }

    @Override
    public EiInfo submitRelease(EiInfo inInfo) {
        //根据UUIDs查询PCC数据
        inInfo.set(EiConstant.serviceId, "S_RX_09");
        EiInfo qInfo = XServiceManager.call(inInfo);
        log.info("********************[PCCServiceImpl] qInfo：{}", qInfo);
        Map<String, Object> resultMap = new ObjectMapper().convertValue(qInfo.get("result"), Map.class);

        if (MapUtils.isEmpty(resultMap)) {
            throw new PlatException("PCC发布失败，原因[发布记录未找到!]");
        }

        //pcc数据解析
//        JSONObject pccData = PccDataParser.toJsonPccData(String.valueOf(resultMap.get("areaSelected")));
//        log.info("********************[PCCServiceImpl] pccData：{}", pccData);
//
//        JSONObject areaSelectedData = pccData.getJSONObject("areaSelected");
//        int playTarget = areaSelectedData.getInt("playTarget");
//        Map<Integer, JSONArray> map = setPccDataByPlayTarget(playTarget, areaSelectedData);

        EiInfo outInfo = PCCAreaDataTrans(resultMap.get("areaSelected").toString());
        if (outInfo.getStatus() == EiConstant.STATUS_FAILURE) {
            return outInfo;
        }

        //获取全局变量属性,判断是否调用pis中央系统(TEST-测试培训系统, NOCC-正式环境)
		String property = PlatApplicationContext.getProperty("iplat4j.admin.type");

		JSONObject areaObject = (JSONObject) outInfo.get("areaObject");
        Integer carType = areaObject.getInt("carType");
        if (!carType.equals(PCCAreaTypeConstant.CAR_NO_CHOOSE)) {
            outInfo = setPCCReleaseDataNew(resultMap, areaObject, carType);
			//推送列车数据
			HashMap map = new HashMap();
			map.put("msg_body", outInfo.get("msg_body"));
			//判断是否调用pis接口
			if (StringUtils.isNotEmpty(property) && property.equals("NNNOCC")) {
				EiInfo eiInfo = EiInfoUtil.callParam("S_XF_FB_04", map).build();
			}
		}

        Integer lineType = areaObject.getInt("lineType");
        if (!lineType.equals(PCCAreaTypeConstant.LINE_NO_CHOOSE)) {
            outInfo = setPCCReleaseDataNew(resultMap, areaObject, lineType);
			//推送车站数据
			HashMap map = new HashMap();
			map.put("msg_body", outInfo.get("msg_body"));
			//判断是否调用pis接口
			if (StringUtils.isNotEmpty(property) && property.equals("NNNOCC")) {
				EiInfo eiInfo = EiInfoUtil.callParam("S_XF_FB_04", map).build();
			}
        }

        outInfo.setStatus(AuditFlag.PASS);

//        for (Integer key : map.keySet()) {
//            //调用三方接口发布
//            outInfo = dataReleased(setPccReleaseData(resultMap, map.get(key), key));
//            log.info("********************[PCCServiceImpl] outInfo：{}", outInfo);
//            //返回PCC发布状态判断待处理
//            int state = Convert.toBool(outInfo.getStatus()) ? AuditFlag.PUBLISHED : AuditFlag.APPROVED;
//            outInfo.set("UUIDs", inInfo.getAttr().get("UUIDs"));
//            outInfo.set("auditFlag", state);
//            if (AuditFlag.APPROVED == state) {
//                outInfo.set(RtConstant.rtMessageCode, RtConstant.RN_FAIL_CODE_PCC);
//            }
//        }


        outInfo.set("UUIDs", resultMap.get("UUIDs"));
        return outInfo;
    }

    public Map<Integer, JSONArray> setPccDataByPlayTarget(int playTarget, JSONObject pccData) {
        Map<Integer, JSONArray> map = new HashMap<>();
        if (playTarget == 0) {
            map.put(playTarget, pccData.getJSONArray("stationSelect"));
        } else if (playTarget == 1) {
            map.put(playTarget, pccData.getJSONArray("trainSelect"));
        } else {
            map.put(0, pccData.getJSONArray("stationSelect"));
            map.put(1, pccData.getJSONArray("trainSelect"));
        }
        return map;
    }

    private EiInfo dataReleased(EiInfo fInfo) {
        log.info("********************[PCCServiceImpl]fInfo：{}", fInfo);
        //调用三方接口发布
        fInfo.set(EiConstant.serviceId, "S_PI_FB_05");
        EiInfo outInfo = XServiceManager.call(fInfo);
        if (outInfo.getStatus() < 0) {
            log.error("*******************[PCCServiceImpl]：{}", new Gson().toJson(outInfo));
        }
        return outInfo;
    }

    private EiInfo setPccReleaseData(Map<String, Object> resultMap, JSONArray jsonArray, int playTarget) {
        Map<Integer, Integer> enumData = new HashMap<Integer, Integer>(12) {{
            put(180001, 1);
            put(180002, 0);
            put(350001, 0);
            put(350002, 1);
        }};
        EiInfo fInfo = new EiInfo();
        fInfo.set("msg_body", new HashMap<String, Object>(16) {{
            //开始时间
            put("start_time", resultMap.get("startTime"));
            //结束时间
            put("end_time", resultMap.get("endTime"));
            //模式控制
            //180001 紧急模式 1 180002 日常模式 0
            put("pcc_mode", enumData.get(Convert.toInt(resultMap.get("pccMode"))));
            //信息类型 0-实时信息；1-预录信息
            put("info_type", 0);
            //预录信息ID 0-无效；1-预录信息1；2-预录信息2；
            put("pre_record", 0);
            //播放模式控制
            // pccCmd 代表播放模式 350001是正常播放 0  350002是取消播放 1
            put("play_mode", enumData.get(Convert.toInt(resultMap.get("pccCmd"))));
            //显示模式选择：0-采用开始、结束时间，1-持续显示(不采用开始结束时间，等待取消标志后取消)
            put("display_mode", 0);
            //车站/列车选择 0－车站；1－列车
            put("play_target", playTarget);
            //发布区域
            put("lines", jsonArray);
            //全线网
            put("is_net", 1);
            //发布内容
            put("desc", JSONUtil.parseObj(resultMap.get("desc")).get("content"));
        }});
        return fInfo;
    }

	/**
	 * PCC区域转化为PCC中央程序所需数据
	 *
	 * @param jsonPCCData
	 */
	private EiInfo PCCAreaDataTrans(String jsonPCCData) {
		EiInfo outInfo = new EiInfo();
		JSONObject resultObject = new JSONObject();

		try {
			PCCAreaData pccAreaData = JSON.parseObject(jsonPCCData, PCCAreaData.class);
			//判断列车是否勾选了线网
			if (!pccAreaData.getCar().getAll()) {
				List<JSONObject> carObjectList = new ArrayList<>();
				List<String> carArrList = pccAreaData.getCar().getArr();

				//判断是否选择了列车线路
				if (carArrList.size() == 0) {
					resultObject.putOnce("carType", PCCAreaTypeConstant.CAR_NO_CHOOSE);
				} else {
					for (int i = 0; i < carArrList.size(); i++) {
						JSONObject tObject = new JSONObject();
						JSONArray tArray = new JSONArray();
						tObject.putOnce("line_id", lineMatch(carArrList.get(i)));
						tObject.putOnce("stations", tArray);
						carObjectList.add(tObject);
					}
					resultObject.putOnce("car", carObjectList);
					resultObject.putOnce("carType", PCCAreaTypeConstant.CAR_NO_ALL);
				}
			} else {
				//这里处理获取全部线路,先禁止4号线
				List<JSONObject> carObjectList = new ArrayList<>();
				for (Map.Entry<String, String> entry : lineBaseData.entrySet()) {
					String value = entry.getValue();
					if (!value.equals("0400000000")) {
						JSONObject tObject = new JSONObject();
						JSONArray tArray = new JSONArray();
						tObject.putOnce("line_id", value);
						tObject.putOnce("stations", tArray);
						carObjectList.add(tObject);
					}
				}
				resultObject.putOnce("car", carObjectList);
				resultObject.putOnce("carType", PCCAreaTypeConstant.CAR_NO_ALL);

			}

			//获取播放区域
			List<Integer> areaIds = new ArrayList<>();
			if (!pccAreaData.getStation().getPosition().getAll()) {
				List<String> positionArrList = pccAreaData.getStation().getPosition().getArr();
				for (int i = 0; i < positionArrList.size(); i++) {
					int areaId = Integer.parseInt(positionArrList.get(i));
					areaIds.add(areaId);
				}
			} else {
				areaIds = Arrays.asList(1, 2, 3, 4);
			}
			resultObject.set("areaId", areaIds);

			//判断全线网(线路下拉框旁边),是就要获取全部的车站编号
			if (!pccAreaData.getStation().getAll()) {
				List<Line> lineList = pccAreaData.getStation().getLine();
				JSONArray linesArray = new JSONArray();

				//先遍历各线路是否有选择车站的情况，判断是否选择了车站
				int lineChoose = 0;
				for (Line one : lineList) {
					if (one.getArr().size() > 0) {
						lineChoose = 1;
					}
				}

				//如果车站未选择则更改标志为未选择车站。
				if (lineChoose == 0) {
					resultObject.putOnce("lineType", PCCAreaTypeConstant.LINE_NO_CHOOSE);
				} else {
					for (int i = 0; i < lineList.size(); i++) {
						JSONObject lineObject = new JSONObject();
						Line one = lineList.get(i);

						JSONArray tArray = new JSONArray();
						//无需判断是否全线网,都要将车站id加进去
						List<String> lineArrList = one.getArr();
						if (lineArrList.size() == 0) {
							continue;
						} else {
							for (int j = 0; j < lineArrList.size(); j++) {
								JSONObject tObject = new JSONObject();
								tObject.putOnce("station_id", lineArrList.get(j));
								tObject.putOnce("area_id", areaIds);
								tArray.add(tObject);
							}
						}

						lineObject.putOnce("line_id", lineMatch(String.valueOf(i + 1)));
						lineObject.putOnce("stations", tArray);
						linesArray.add(lineObject);
					}
					resultObject.putOnce("line", linesArray);
					resultObject.putOnce("lineType", PCCAreaTypeConstant.LINE_NO_ALL);
				}
			} else {
				//获取基础数据接口车站数据,将全部车站进行封装
				EiInfo eiInfo = queryStation(new EiInfo());
				if (eiInfo.getStatus() < 0) {
					throw new PlatException("车站基础数据获取失败");
				}
				List<Map<String,Object>> result = (List<Map<String, Object>>) eiInfo.get("result");
				//根据线路编号分组
				Map<String, List<Map<String, Object>>> groupByLineNumber = result.stream()
						.collect(Collectors.groupingBy(items -> (String) items.get("line_id")));
				//遍历分组后的结果
				JSONArray linesArray = new JSONArray();
				for (Map.Entry<String, List<Map<String, Object>>> entry : groupByLineNumber.entrySet()) {
					JSONObject lineObject = new JSONObject();
					JSONArray stations= new JSONArray();
					List<Map<String, Object>> itemsList = entry.getValue();
					// 遍历每个分组里的数据项
					for (Map<String, Object> item : itemsList) {
						//判断是否启用
						if (StringUtils.isNotEmpty((String) item.get("acc_sta_id")) && item.get("enable_status").equals("true")) {
							JSONObject staObject = new JSONObject();
							staObject.putOnce("station_id", item.get("sta_id"));
							staObject.putOnce("area_id", areaIds);
							stations.put(staObject);
						}
					}
					lineObject.putOnce("line_id", entry.getKey());
					lineObject.putOnce("stations", stations);
					linesArray.put(lineObject);
				}

				resultObject.putOnce("lineType", PCCAreaTypeConstant.LINE_ALL);
				resultObject.putOnce("line", linesArray);
				resultObject.putOnce("netAreaId", areaIds);
			}

			outInfo.setStatus(EiConstant.STATUS_SUCCESS);
			outInfo.set("areaObject", resultObject);

			return outInfo;
		} catch (Exception ex) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg(ex.getMessage());
			return null;
		}
	}

    /**
     * PCC区域转化为PCC中央程序所需数据
     *
     * @param jsonPCCData
     */
//    private EiInfo PCCAreaDataTrans(String jsonPCCData) {
//        EiInfo outInfo = new EiInfo();
//        JSONObject resultObject = new JSONObject();
//
//        try {
//            PCCAreaData pccAreaData = JSON.parseObject(jsonPCCData, PCCAreaData.class);
//            //判断列车是否勾选了线网
//            if (!pccAreaData.getCar().getAll()) {
//                List<JSONObject> carObjectList = new ArrayList<>();
//                List<String> carArrList = pccAreaData.getCar().getArr();
//
//                //判断是否选择了列车线路
//                if (carArrList.size() == 0) {
//                    resultObject.putOnce("carType", PCCAreaTypeConstant.CAR_NO_CHOOSE);
//                } else {
//                    for (int i = 0; i < carArrList.size(); i++) {
//                        JSONObject tObject = new JSONObject();
//                        JSONArray tArray = new JSONArray();
////                        tArray.add(new JSONObject());
////                        int lineId = Integer.parseInt(carArrList.get(i));
//                        tObject.putOnce("line_id", lineMatch(carArrList.get(i)));
//                        tObject.putOnce("stations", tArray);
//                        carObjectList.add(tObject);
//                    }
//                    resultObject.putOnce("car", carObjectList);
//                    resultObject.putOnce("carType", PCCAreaTypeConstant.CAR_NO_ALL);
//                }
//            } else {
//                resultObject.putOnce("carType", PCCAreaTypeConstant.CAR_ALL);
//            }
//
//            //获取播放区域
//            List<Integer> areaIds = new ArrayList<>();
//            if (!pccAreaData.getStation().getPosition().getAll()) {
//                List<String> positionArrList = pccAreaData.getStation().getPosition().getArr();
//                for (int i = 0; i < positionArrList.size(); i++) {
//                    int areaId = Integer.parseInt(positionArrList.get(i));
//                    areaIds.add(areaId);
//                }
//            } else {
//                areaIds = Arrays.asList(1, 2, 3, 4);
//            }
//            resultObject.set("areaId", areaIds);
//
//            //判断是否勾选了全线网
//            if (!pccAreaData.getStation().getAll()) {
//                List<Line> lineList = pccAreaData.getStation().getLine();
//                JSONArray linesArray = new JSONArray();
//
//                //先遍历各线路是否有选择车站的情况，判断是否选择了车站
//                int lineChoose = 0;
//                for (Line one : lineList) {
//                    if (one.getArr().size() > 0) {
//                        lineChoose = 1;
//                    }
//                }
//
//                //如果车站未选择则更改标志为未选择车站。
//                if (lineChoose == 0) {
//                    resultObject.putOnce("lineType", PCCAreaTypeConstant.LINE_NO_CHOOSE);
//                } else {
//                    for (int i = 0; i < lineList.size(); i++) {
//                        JSONObject lineObject = new JSONObject();
//                        Line one = lineList.get(i);
//
//                        JSONArray tArray = new JSONArray();
//                        if (!one.getAll()) {
//                            List<String> lineArrList = one.getArr();
//                            if (lineArrList.size() == 0) {
//                                continue;
//                            } else {
//                                for (int j = 0; j < lineArrList.size(); j++) {
//                                    JSONObject tObject = new JSONObject();
//                                    tObject.putOnce("station_id", lineArrList.get(j));
//                                    tObject.putOnce("area_id", areaIds);
//                                    tArray.add(tObject);
//                                }
//                            }
//
//                        } else {
//                            int lineArrSize = ServiceXFFB02.lineStationList.get(i).size();
//
//                            for (int j = 0; j < lineArrSize; j++) {
//                                JSONObject tObject = new JSONObject();
//                                tObject.putOnce("station_id", String.valueOf(j + 1));
//                                tObject.putOnce("area_id", areaIds);
//                                tArray.add(tObject);
//                            }
//                        }
//
//                        lineObject.putOnce("line_id", lineMatch(String.valueOf(i + 1)));
//                        lineObject.putOnce("stations", tArray);
//                        linesArray.add(lineObject);
//                    }
//                    resultObject.putOnce("line", linesArray);
//                    resultObject.putOnce("lineType", PCCAreaTypeConstant.LINE_NO_ALL);
//                }
//            } else {
//                resultObject.putOnce("lineType", PCCAreaTypeConstant.LINE_ALL);
//                resultObject.putOnce("netAreaId", areaIds);
//            }
//
//            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
//            outInfo.set("areaObject", resultObject);
//
//            return outInfo;
//        } catch (Exception ex) {
//            outInfo.setStatus(EiConstant.STATUS_FAILURE);
//            outInfo.setMsg(ex.getMessage());
//            return null;
//        }
//    }

	/**
	 * 列车编号与线路的基础数据匹配
	 * @param carNo-列车编号
	 * @return lineId-线路编号
	 */
	public String lineMatch(String carNo) {
    	//如果基础数据已经有数据就不需要再重新获取一次了
    	if (lineBaseData.size() == 0) {
			EiInfo eiInfo = queryLine(new EiInfo());
			if (eiInfo.getStatus() == -1) {
				throw new PlatException("线路基础数据获取失败");
			}
			List<Map<String,String>> data = (List<Map<String,String>>) eiInfo.getBlock("result").getRows();
			for (int i = 0; i < data.size(); i++) {
				lineBaseData.put(String.valueOf(i+1),data.get(i).get("line_id"));
			}
		}
		String lineId = "";
		lineId = lineBaseData.get(carNo);
		return lineId;
	}

	/**
	 * 查询线路基础数据
	 * 可传参，可查询单条
	 * @param info 参数设置
	 * @return
	 */
	public static EiInfo queryLine(EiInfo info){
		info.set("enableStatus",true);
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_LINE_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params", info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_02",eiInfo).build();
		return outInfo;
	}

	/**
	 * 查询车站基础数据
	 * 可传参，可查询单条
	 * @param info 参数设置
	 * districtId ->行政区编号
	 * stationId ->车站编号
	 * stationCName ->车站名称
	 * lineCName ->线路名称
	 * lineId ->线路编号
	 * @return
	 */
	public EiInfo queryStation(EiInfo info){
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("shareServiceId", "D_NOCC_BASE_STATION_INFO");
		eiInfo.set("ePlatApp", "1");
		eiInfo.set("isGetFieldCname", "true");
		eiInfo.set("params",  info.getAttr());//传入的参数
		eiInfo.set("offset", "0");//分页
		eiInfo.set("limit", "9999");//限制查询条数，不填就默认10条
		EiInfo outInfo = EiInfoUtils.callParam("S_BASE_DATA_03",eiInfo).build();
		//初始化车站数据
		List<Map<String,Object>> result = outInfo.getBlock("result").getRows();
		outInfo.set("result", result);
		return outInfo;
	}

    /**
     * 解析数据满足PCC中央程序格式
     * @param resultMap
     * @param areaObject
     * @param pccAreaType
     * @return
     */
    private EiInfo setPCCReleaseDataNew(Map<String, Object> resultMap, JSONObject areaObject, int pccAreaType) {
        //设定枚举值
        Map<Integer, Integer> enumData = new HashMap<Integer, Integer>(12) {{
            put(Integer.valueOf(PCCReleaseModeConstant.GUIDE_MODE), 2);
            put(Integer.valueOf(PCCReleaseModeConstant.EMERGENCY_MODE), 1);
            put(Integer.valueOf(PCCReleaseModeConstant.NORMAL_MODE), 0);
            put(Integer.valueOf(PCCPlayTypeConstant.START_PLAY), 0);
            put(Integer.valueOf(PCCPlayTypeConstant.CANCEL_PLAY), 1);
            put(PCCAreaTypeConstant.CAR_ALL, 1);
            put(PCCAreaTypeConstant.CAR_NO_ALL, 1);
            put(PCCAreaTypeConstant.LINE_ALL, 0);
            put(PCCAreaTypeConstant.LINE_NO_ALL, 0);
        }};
        EiInfo fInfo = new EiInfo();
        HashMap<String, Object> paramMap = new HashMap<>(16);
        //开始时间
        paramMap.put("start_time", resultMap.get("startTime"));
        //结束时间
        paramMap.put("end_time", resultMap.get("endTime"));
        //预录信息 0-无效 1-预录信息id1 2-预录信息id2
        paramMap.put("pre_record", 0);
        //模式控制
        //180001 紧急模式 1 180002 日常模式 0
        paramMap.put("pcc_mode", enumData.get(Convert.toInt(resultMap.get("pccMode"))));
        //信息类型 0-实时信息；1-预录信息
        paramMap.put("info_type", 0);
        //播放模式控制
        // pccCmd 代表播放模式 350001是正常播放 0  350002是取消播放 1
        paramMap.put("play_mode", enumData.get(Convert.toInt(resultMap.get("pccCmd"))));
        //显示模式选择：0-采用开始、结束时间，1-持续显示(不采用开始结束时间，等待取消标志后取消)
        paramMap.put("display_mode", 0);
        //车站/列车选择 0－车站；1－列车
        paramMap.put("play_target", enumData.get(pccAreaType));
        //线网区域id
		paramMap.put("net_area_id", new JSONArray());
        //发布区域选择，全线网和非全线网处理 0 全线网 1非全线网
        JSONArray linesArray = new JSONArray();
        switch (pccAreaType) {
            case PCCAreaTypeConstant.CAR_ALL:
                paramMap.put("is_net", 0);
                break;
            case PCCAreaTypeConstant.CAR_NO_ALL:
                paramMap.put("is_net", 1);
                linesArray = areaObject.getJSONArray("car");
                break;
            case PCCAreaTypeConstant.LINE_ALL:
                paramMap.put("is_net", 0);
                paramMap.put("net_area_id", areaObject.get("areaId"));
            case PCCAreaTypeConstant.LINE_NO_ALL:
                paramMap.put("is_net", 1);
                linesArray = areaObject.getJSONArray("line");
				paramMap.put("net_area_id", areaObject.get("areaId"));
                break;
            default:
                break;
        }
        paramMap.put("lines", linesArray);
        //发布内容
        paramMap.put("desc", JSONUtil.parseObj(resultMap.get("desc")).get("content"));

        fInfo.set("msg_body", paramMap);
        return fInfo;
    }

}

