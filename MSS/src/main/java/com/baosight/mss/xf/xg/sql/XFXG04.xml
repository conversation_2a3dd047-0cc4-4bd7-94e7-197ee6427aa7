<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XFXG04">

    <!--群组和部门的公共查询条件，如有其他条件自行添加-->
    <sql id="group_dept_common_query">
        <isNotEmpty prepend=" AND " property="fdUuids">
            fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdNumber">
            fd_number = #fdNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdName">
            fd_name = #fdName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdParentId">
            fd_parent_id = #fdParentId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDesc">
            fd_desc = #fdDesc#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdLevel">
            fd_level = #fdLevel#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdType">
            fd_type = #fdType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdNumbers">
            fd_number in
            <iterate open="(" close=")" conjunction="," property="fdNumbers" >
                #fdNumbers[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend1">
            fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend2">
            fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend3">
            fd_extend3 = #fdExtend3#
        </isNotEmpty>
    </sql>

    <!--部门人员的公共查询条件，如有其他条件自行添加-->
    <sql id="dept_person_common_query">
        <isNotEmpty prepend=" AND " property="fdUuids">
            person.fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdNumber">
            person.fd_number = #fdNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdName">
            person.fd_name = #fdName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="likeName">
            person.fd_name like '%$likeName$%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdPhone">
            person.fd_phone = #fdPhone#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdPost">
            person.fd_post = #fdPost#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptId">
            person.fd_dept_id = #fdDeptId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptName">
            person.fd_dept_name = #fdDeptName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdNumberId">
            person.fd_number_id = #fdNumberId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdType">
            person.fd_type = #fdType#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="filterText">
            (person.fd_name like '%$filterText$%' or person.fd_post like '%$filterText$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdNumbers">
            person.fd_number in
            <iterate open="(" close=")" conjunction="," property="fdNumbers" >
                #fdNumbers[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdNameList">
            person.fd_Name in
            <iterate open="(" close=")" conjunction="," property="fdNameList" >
                #fdNameList[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptNumberArray">
            person.fd_dept_id in
            <iterate open="(" close=")" conjunction="," property="fdDeptNumberArray" >
                #fdDeptNumberArray[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptNameArray">
            dept.fd_name in
            <iterate open="(" close=")" conjunction="," property="fdDeptNameArray" >
                #fdDeptNameArray[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdPostArray">
            person.fd_post in
            <iterate open="(" close=")" conjunction="," property="fdPostArray" >
                #fdPostArray[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdNumberIdArray">
            person.fd_number_id in
            <iterate open="(" close=")" conjunction="," property="fdNumberIdArray" >
                #fdNumberIdArray[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdPhoneNumberArray">
            person.fd_phone in
            <iterate open="(" close=")" conjunction="," property="fdPhoneNumberArray" >
                #fdPhoneNumberArray[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeleteFlag">
            person.fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedBy">
            person.fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedTime">
            person.fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateBy">
            person.fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateTime">
            person.fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend1">
            person.fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend2">
            person.fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend3">
            person.fd_extend3 = #fdExtend3#
        </isNotEmpty>
    </sql>

    <!--岗位查询条件-->
    <sql id="group_post_common_query">
        <isNotEmpty prepend=" AND " property="fdUuids">
            fd_uuids = #fdUuids#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptNumber">
            fd_dept_number = #fdDeptNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptName">
            fd_dept_name = #fdDeptName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptOfficeNumber">
            fd_dept_office_number = #fdDeptOfficeNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeptOfficeName">
            fd_dept_office_name = #fdDeptOfficeName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdGroupUuid">
            fd_group_uuid = #fdGroupUuid#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdGroupName">
            fd_group_name = #fdGroupName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdPhoneFlag">
            fd_phone_flag = #fdPhoneFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdMessageFlag">
            fd_message_flag = #fdMessageFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdType">
            fd_type = #fdType#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdPost">
            fd_post = #fdPost#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdDeleteFlag">
            fd_delete_flag = #fdDeleteFlag#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedBy">
            fd_created_by = #fdCreatedBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdCreatedTime">
            fd_created_time = #fdCreatedTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateBy">
            fd_update_by = #fdUpdateBy#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdUpdateTime">
            fd_update_time = #fdUpdateTime#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend1">
            fd_extend1 = #fdExtend1#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend2">
            fd_extend2 = #fdExtend2#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdExtend3">
            fd_extend3 = #fdExtend3#
        </isNotEmpty>
        <isNotEmpty prepend="AND" property="filterText">
            (fd_dept_name like '%$filterText$%' or fd_dept_office_name like '%$filterText$%'
            or fd_post like '%$filterText$%')
        </isNotEmpty>
    </sql>

    <!--查询通讯录群组-->
    <select id="queryMssAddressGroup" resultClass="java.util.HashMap">
        select
        fd_uuids as "fdUuids", <!--uuid-->
        fd_number as "fdNumber", <!--群组编号-->
        fd_name as "fdName", <!--群组名称-->
        fd_parent_id as "fdParentId", <!--父id-->
        fd_desc as "fdDesc", <!--描述-->
        fd_level as "fdLevel", <!--层级-->
        fd_type as "fdType", <!--类别 0-群组 1-群组人员 2-部门 3-部门人员-->
        fd_delete_flag as "fdDeleteFlag", <!--删除标识-->
        fd_created_by as "fdCreatedBy", <!--创建人-->
        fd_created_time as "fdCreatedTime", <!--创建时间-->
        fd_update_by as "fdUpdateBy", <!--修改人-->
        fd_update_time as "fdUpdateTime", <!--修改时间-->
        fd_extend1 as "fdExtend1", <!--扩展字段1-->
        fd_extend2 as "fdExtend2", <!--扩展字段2-->
        fd_extend3 as "fdExtend3" <!--扩展字段3-->
        from ${mssProjectSchema}.t_mss_address_group
        where 1 = 1 and fd_delete_flag != '1'
        <include refid="group_dept_common_query"></include>
    </select>

    <!--查询通讯录群组-->
    <select id="queryAllMajor" resultClass="java.util.HashMap">
        select
        fd_uuids as "UUIDs", <!--uuid-->
        fd_number as "profession_id", <!--群组编号-->
        fd_name as "profession" <!--群组名称-->
        from ${mssProjectSchema}.t_mss_address_group
        where 1 = 1 and fd_delete_flag != '1'
        <include refid="group_dept_common_query"></include>
    </select>

    <!--查询岗位信息-->
    <select id="queryMssAddressGroupPost" resultClass="java.util.HashMap">
        select
        fd_uuids as "fdUuids", <!--uuid-->
        fd_dept_number as "fdDeptNumber", <!--部门编号-->
        fd_dept_name as "fdDeptName", <!--部门名称-->
        fd_dept_office_number as "fdDeptOfficeNumber", <!--科室编号-->
        fd_dept_office_name as "fdDeptOfficeName", <!--科室名称-->
        fd_group_uuid as "fdGroupUuid", <!--群组（专业）id-->
        fd_group_name as "fdGroupName", <!--群组（专业）名称-->
        fd_phone_flag as "fdPhoneFlag", <!--电话标识位-->
        fd_message_flag as "fdMessageFlag", <!--消息标识位-->
        fd_type as "fdType", <!--类别 0-群组（专业） 1-群组（专业）岗位 2-部门 3-部门人员-->
        fd_post as "fdPost", <!--岗位名称-->
        fd_delete_flag as "fdDeleteFlag", <!--删除标识-->
        fd_created_by as "fdCreatedBy", <!--创建人-->
        fd_created_time as "fdCreatedTime", <!--创建时间-->
        fd_update_by as "fdUpdateBy", <!--修改人-->
        fd_update_time as "fdUpdateTime", <!--修改时间-->
        fd_extend1 as "fdExtend1", <!--扩展字段1-->
        fd_extend2 as "fdExtend2", <!--扩展字段2-->
        fd_extend3 as "fdExtend3" <!--扩展字段3-->
        from ${mssProjectSchema}.t_mss_address_group_post
        where 1 = 1 and fd_delete_flag != '1'
        <include refid="group_post_common_query"></include>
    </select>

    <!--查询岗位信息-->
    <select id="queryAllPost" resultClass="java.util.HashMap">
        select
        fd_uuids as "fdUuids", <!--uuid-->
        fd_dept_number as "fdDeptNumber", <!--部门编号-->
        fd_dept_name as "fdDeptName", <!--部门名称-->
        fd_dept_office_number as "fdDeptOfficeNumber", <!--科室编号-->
        fd_dept_office_name as "fdDeptOfficeName", <!--科室名称-->
        fd_group_uuid as "fdGroupUuid", <!--群组（专业）id-->
        fd_group_name as "fdGroupName", <!--群组（专业）名称-->
        fd_type as "fdType", <!--类别 0-群组（专业） 1-群组（专业）岗位 2-部门 3-部门人员-->
        fd_post as "fdPost", <!--岗位名称-->
        fd_phone_flag as "fdPhoneFlag", <!--电话标识位-->
        fd_message_flag as "fdMessageFlag" <!--消息标识位-->
        from ${mssProjectSchema}.t_mss_address_group_post
        where 1 = 1 and fd_delete_flag != '1'
        <include refid="group_post_common_query"></include>
    </select>

    <!--查询导出的岗位信息-->
    <select id="queryExportGroupPost" resultClass="java.util.HashMap">
        select
        d1.fd_name as "department",
        d2.fd_name as "deptRoom",
        g.fd_name as "major",
        case when p.fd_phone_flag = '00' then '否' else '是' end as "cellNotice",
        case when p.fd_message_flag = '00' then '否' else '是' end as "warmNotice",
        p.fd_post as "post"
        from ${mssProjectSchema}.t_mss_address_group_post p
            left join ${mssProjectSchema}.t_mss_address_dept d1
            on p.fd_dept_number = d1.fd_number
            left join ${mssProjectSchema}.t_mss_address_dept d2
            on p.fd_dept_office_number = d2.fd_number
            left join ${mssProjectSchema}.t_mss_address_group g
            on p.fd_group_uuid = g.fd_number
        where 1 = 1 and p.fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="idList">
            p.fd_uuids in
            <iterate open="(" close=")" conjunction="," property="idList" >
                #idList[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--统计岗位数量-->
    <select id="countMssAddressGroupPost" resultClass="int">
        select count(*)
        from ${mssProjectSchema}.t_mss_address_group_post
        where 1 = 1 and fd_delete_flag != '1'
        <include refid="group_post_common_query"></include>
    </select>

    <!--查询岗位信息：对外接口-->
    <select id="queryForeignMajorInfo" resultClass="java.util.HashMap">
        select
            post.fd_uuids as "UUIDs", <!--uuid-->
            t1.fd_number as "division", <!--部门编号-->
            t2.fd_number as "department", <!--科室编号-->
            post.fd_phone_flag as "phone_notice", <!--电话标识位-->
            post.fd_message_flag as "message_notice", <!--消息标识位-->
            post.fd_post as "post", <!--岗位名称-->
            post.fd_group_uuid as "profession_id", <!--专业id-->
            major.fd_name as "profession" <!--专业名称-->
        from
            ${mssProjectSchema}.t_mss_address_group_post post
            left join ${mssProjectSchema}.t_mss_address_group major on post.fd_group_uuid = major.fd_number
            left join ${mssProjectSchema}.t_mss_address_dept t1 on post.fd_dept_name = t1.fd_name
            left join ${mssProjectSchema}.t_mss_address_dept t2 on post.fd_dept_office_name = t2.fd_name
        where 1 = 1 and post.fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="fdUuids">
            post.fd_uuids = #fdUuids#
        </isNotEmpty>
    </select>

    <!--查询通讯录部门-->
    <select id="queryMssAddressDept" resultClass="java.util.HashMap">
        select
        fd_uuids as "fdUuids",  <!--uuid-->
        fd_number as "fdNumber", <!--部门编号-->
        fd_name as "fdName", <!--部门名称-->
        fd_parent_id as "fdParentId", <!--父id-->
        fd_level as "fdLevel", <!--层级-->
        fd_type as "fdType" <!--类别 0-群组 1-群组人员 2-部门 3-部门人员-->
        from ${mssProjectSchema}.t_mss_address_dept
        where 1 = 1 and fd_delete_flag != '1'
        <include refid="group_dept_common_query"></include>
    </select>

    <!--查询通讯录部门名称-->
    <select id="queryDeptNameByNumber" resultClass="java.util.HashMap">
        select
        fd_number as "number",
        fd_name as "name"
        from ${mssProjectSchema}.t_mss_address_dept
        where 1 = 1 and fd_delete_flag != '1'
        <include refid="group_dept_common_query"></include>
    </select>

    <!--统计部门人员数量-->
    <select id="countMssAddressDeptPerson" resultClass="int">
        select count(*)
        from ${mssProjectSchema}.t_mss_address_dept_person person
        where 1 = 1 and person.fd_delete_flag != '1'
        <include refid="dept_person_common_query"></include>
    </select>

    <!--查询通讯录部门人员名称和工号-->
    <select id="queryPeopleInfo" resultClass="java.util.HashMap">
        select
        fd_name as "name",
        fd_number as "number",
        fd_phone as "phone",
        fd_number_id as "numberId",
        fd_post as "post"
        from ${mssProjectSchema}.t_mss_address_dept_person
        where 1 = 1 and fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="name">
            fd_name = #name#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="number">
            fd_number = #number#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="phone">
            fd_phone = #phone#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="numberId">
            fd_number_id = #numberId#
        </isNotEmpty>
    </select>

    <!--查询通讯录部门人员-->
    <select id="queryMssAddressDeptPerson" resultClass="java.util.HashMap">
        select
        person.fd_uuids as "fdUuids", <!--uuid-->
        person.fd_name as "fdName", <!--人员名称-->
        person.fd_number as "fdNumber", <!--工号-->
        person.fd_phone as "fdPhone", <!--手机号码-->
        person.fd_post as "fdPost", <!--职位-->
        person.fd_number_id as "fdNumberId", <!--人员钉钉id-->
        person.fd_dept_id as "fdDeptId", <!--所属部门编号-->
        dept.fd_name as "fdDeptName", <!--所属部门名-->
        person.fd_type as "fdType" <!--类别 0-群组 1-群组人员 2-部门 3-部门人员-->
        from ${mssProjectSchema}.t_mss_address_dept_person person
            left join ${mssProjectSchema}.t_mss_address_dept dept
            on person.fd_dept_id = dept.fd_number
        where 1 = 1 and person.fd_delete_flag != '1'
        <include refid="dept_person_common_query"></include>
    </select>

    <!--查询通讯录部门人员(精简)-->
    <select id="queryDeptPersons" resultClass="java.util.HashMap">
        select
        person.fd_name as "fdName", <!--人员名称-->
        person.fd_number as "fdNumber", <!--工号-->
        person.fd_phone as "fdPhone", <!--手机号码-->
        person.fd_post as "fdPost", <!--职位-->
        person.fd_number_id as "fdNumberId", <!--人员钉钉id-->
        person.fd_dept_id as "fdDeptId", <!--所属部门编号-->
        dept.fd_name as "fdDeptName", <!--所属部门名-->
        person.fd_type as "fdType" <!--类别 0-群组 1-群组人员 2-部门 3-部门人员-->
        from ${mssProjectSchema}.t_mss_address_dept_person person
        left join ${mssProjectSchema}.t_mss_address_dept dept
        on person.fd_dept_id = dept.fd_number
        where 1 = 1 and person.fd_delete_flag != '1'
        <include refid="dept_person_common_query"></include>
    </select>

    <!--根据部门编号查询通讯录部门人员-->
    <select id="queryPersonByDeptNumber" resultClass="java.util.HashMap">
        select
        person.fd_uuids as "UUIDs", <!--uuid-->
        person.fd_name as "name", <!--人员名称-->
        person.fd_number as "number", <!--工号-->
        person.fd_phone as "phone", <!--手机号码-->
        person.fd_post as "post", <!--职位-->
        person.fd_number_id as "id", <!--人员id-->
        person.fd_dept_id as "deptNumber", <!--所属部门编号-->
        dept.fd_name as "deptName" <!--所属部门名-->
        from ${mssProjectSchema}.t_mss_address_dept_person person
        left join ${mssProjectSchema}.t_mss_address_dept dept
        on person.fd_dept_id = dept.fd_number
        where 1 = 1 and person.fd_delete_flag != '1'
        <include refid="dept_person_common_query"></include>
    </select>

    <!--获取人员-->
    <select id="queryDeptPersonByName" resultClass="java.util.HashMap">
        select
        t1.fd_uuids as "uuid",
        t1.fd_number_id as "numberId",
        t1.fd_type as "type",
        t1.fd_number as "number",
        t1.fd_post as "post",
        t1.fd_phone as "phone",
        t1.fd_name as "name",
        t2.fd_number as "orgNumber",
        t2.fd_name as "orgName",
        t2.fd_parent_id as "orgParentId"
        from ${mssProjectSchema}.t_mss_address_dept_person t1
        left join ${mssProjectSchema}.t_mss_address_dept t2
        on t1.fd_dept_id = t2.fd_number
        where 1=1
        <isNotEmpty prepend=" AND " property="likeName">
            t1.fd_name like '%$likeName$%'
        </isNotEmpty>
    </select>

    <!--根据专业ID查询岗位信息-->
    <select id="queryPostByMajorNumber" resultClass="java.util.HashMap">
        select
        fd_dept_number as "deptNumber",<!--部门编号-->
        fd_dept_name as "deptName", <!--部门名称-->
        fd_dept_office_number as "office",<!--科室编号-->
        fd_dept_office_name as "officeName",<!--科室名称-->
        fd_group_uuid as "majorNumber", <!--群组（专业）编号-->
        fd_post as "post" <!--岗位名称-->
        from ${mssProjectSchema}.t_mss_address_group_post
        where 1 = 1 and fd_delete_flag != '1'
        <isNotEmpty prepend=" AND " property="fdMajorNumber">
            fd_group_uuid = #fdMajorNumber#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="fdMajorNumberArray">
            fd_group_uuid in
            <iterate open="(" close=")" conjunction="," property="fdMajorNumberArray" >
                #fdMajorNumberArray[]#
            </iterate>
        </isNotEmpty>
    </select>

    <!--获取全部专业群组编号-->
    <select id="queryMajorNumber" resultClass="java.util.HashMap">
        select
        fd_number as "majorNumber"
        from ${mssProjectSchema}.t_mss_address_group
        where 1 = 1 and fd_delete_flag != '1'
    </select>

    <!--岗位信息新增-->
    <insert id="insertMssAddressGroupPost" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_mss_address_group_post(
        fd_uuids,
        fd_dept_number,
        fd_dept_name,
        fd_dept_office_number,
        fd_dept_office_name,
        fd_group_uuid,
        fd_group_name,
        fd_phone_flag,
        fd_message_flag,
        fd_type,
        fd_post,
        fd_delete_flag,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time,
        fd_extend1,
        fd_extend2,
        fd_extend3
        )
        values(
        #fdUuids#,
        #fdDeptNumber#,
        #fdDeptName#,
        #fdDeptOfficeNumber#,
        #fdDeptOfficeName#,
        #fdGroupUuid#,
        #fdGroupName#,
        #fdPhoneFlag#,
        #fdMessageFlag#,
        #fdType#,
        #fdPost#,
        #fdDeleteFlag#,
        #fdCreatedBy#,
        #fdCreatedTime#,
        #fdUpdateBy#,
        #fdUpdateTime#,
        #fdExtend1#,
        #fdExtend2#,
        #fdExtend3#
        )
    </insert>

    <!--新增通讯录群组节点-->
    <insert id="insertMssAddressGroup" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_mss_address_group(
        fd_uuids,
        fd_number,
        fd_name,
        fd_parent_id,
        fd_desc,
        fd_level,
        fd_type,
        fd_delete_flag,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time,
        fd_extend1,
        fd_extend2,
        fd_extend3
        )
        values(
        #fdUuids#,
        #fdNumber#,
        #fdName#,
        #fdParentId#,
        #fdDesc#,
        #fdLevel#,
        #fdType#,
        #fdDeleteFlag#,
        #fdCreatedBy#,
        #fdCreatedTime#,
        #fdUpdateBy#,
        #fdUpdateTime#,
        #fdExtend1#,
        #fdExtend2#,
        #fdExtend3#
        )
    </insert>

    <!--新增部门信息-->
    <insert id="insertDepartment" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_mss_address_dept(
        fd_uuids,
        fd_number,
        fd_name,
        fd_parent_id,
        fd_type,
        fd_delete_flag,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time,
        fd_extend1,
        fd_extend2,
        fd_extend3
        )
        values(
        #fdUuids#,
        #fdNumber#,
        #fdName#,
        #fdParentId#,
        #fdType#,
        #fdDeleteFlag#,
        #fdCreatedBy#,
        #fdCreatedTime#,
        #fdUpdateBy#,
        #fdUpdateTime#,
        #fdExtend1#,
        #fdExtend2#,
        #fdExtend3#
        )
    </insert>

    <!--删除部门信息-->
    <delete id="deleteDepartment" parameterClass="java.util.HashMap">
        delete from ${mssProjectSchema}.t_mss_address_dept where fd_extend3 is null
        <isNotEmpty property="fdUuids">
            and fd_uuids = #fdUuids#
        </isNotEmpty>
    </delete>

    <!--新增部门人员-->
    <insert id="insertDepartmentPerson" parameterClass="java.util.HashMap">
        insert into ${mssProjectSchema}.t_mss_address_dept_person(
        fd_uuids,
        fd_number,
        fd_name,
        fd_dept_id,
        fd_dept_name,
        fd_phone,
        fd_post,
        fd_type,
        fd_delete_flag,
        fd_created_by,
        fd_created_time,
        fd_update_by,
        fd_update_time,
        fd_extend1,
        fd_extend2,
        fd_extend3,
        fd_number_id
        )
        values(
        #fdUuids#,
        #fdNumber#,
        #fdName#,
        #fdDeptId#,
        #fdDeptName#,
        #fdPhone#,
        #fdPost#,
        #fdType#,
        #fdDeleteFlag#,
        #fdCreatedBy#,
        #fdCreatedTime#,
        #fdUpdateBy#,
        #fdUpdateTime#,
        #fdExtend1#,
        #fdExtend2#,
        #fdExtend3#,
        #fdNumberId#
        )
    </insert>

    <!--删除部门人员信息-->
    <delete id="deleteDepartmentPerson" parameterClass="java.util.HashMap">
        <!--不支持where 1=1或者没有where,找一个字段条件为真的-->
        delete from ${mssProjectSchema}.t_mss_address_dept_person where fd_extend3 is null
        <isNotEmpty property="fdUuids">
            and fd_uuids = #fdUuids#
        </isNotEmpty>
    </delete>

    <!-- 删除通讯录群组节点-->
    <delete id="deleteMssAddressGroup" parameterClass="java.util.HashMap">
        delete from ${mssProjectSchema}.t_mss_address_group
        where
        fd_uuids = #fdUuids#
    </delete>

    <!-- 根据专业群组编号删除岗位-->
    <delete id="deletePostByGroupId" parameterClass="java.util.HashMap">
        delete from ${mssProjectSchema}.t_mss_address_group_post
        where
        fd_group_uuid = #groupId#
    </delete>

    <!--修改通讯录群组节点-->
    <update id="update" parameterClass="java.util.HashMap">
        update ${mssProjectSchema}.t_mss_address_group
        set
        fd_uuids = #fdUuids#,
        fd_number = #fdNumber#,
        fd_name = #fdName#,
        fd_parent_id = #fdParentId#,
        fd_desc = #fdDesc#,
        fd_level = #fdLevel#,
        fd_type = #fdTpe#,
        fd_delete_flag = #fdDeleteFlag#,
        fd_created_by = #fdCreatedBy#,
        fd_created_time = #fdCreatedTime#,
        fd_update_by = #fdUpdateBy#,
        fd_update_time = #fdUpdateTime#,
        fd_extend1 = #fdExtend1#,
        fd_extend2 = #fdExtend2#,
        fd_extend3 = #fdExtend3#
        where
        fd_uuids = #fdUuids#
    </update>

    <!--修改通讯录群组节点名称-->
    <update id="updateName" parameterClass="java.util.HashMap">
        update ${mssProjectSchema}.t_mss_address_group
        set
        fd_name = #fdName#,
        fd_update_by = #fdUpdateBy#,
        fd_update_time = #fdUpdateTime#
        where
        fd_uuids = #fdUuids#
    </update>

</sqlMap>