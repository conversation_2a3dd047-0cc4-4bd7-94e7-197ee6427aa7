package com.baosight.mss.xf.fb.service;

import cn.hutool.core.date.DateUtil;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 信息发布通知情况页面后台处理类
 * @author: lanyifu
 * @date: 2023/09/18 18:24:11
 */
public class ServiceXFFB0102 extends ServiceBase {

    /**
     * @description 初始化方法
     * @param inInfo
     * @return 数据集
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        EiInfo outInfo = query(inInfo);
        return outInfo;
    }

    /**
     * @description 查询信息发布消息通知的方法
     * @param inInfo
     * @return 数据集
     */
    @Override
    public EiInfo query(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            //初始化时使用该参数
            String InfoHistoryIdFromInit = (String) inInfo.get("fdUuids");
            //点击tab页切换使用该参数
            String InfoHistoryIdFromTab = (String) inInfo.get("inqu_status-0-fdInfoHistoryId");
            //类别,区分消息通知和电话通知
            String type = (String) inInfo.get("inqu_status-0-fdType");
            Map map = new HashMap();
            if (StringUtils.isNotEmpty(InfoHistoryIdFromInit)) {
                map.put("fdInfoHistoryUuid", InfoHistoryIdFromInit);
            }
            if (StringUtils.isNotEmpty(InfoHistoryIdFromTab)) {
                map.put("fdInfoHistoryUuid", InfoHistoryIdFromTab);
            }
            //区分消息通知和电话通知的数据,默认消息通知的数据
            if (StringUtils.isNotEmpty(type)) {
                map.put("fdType", type);
            } else {
                map.put("fdType", "0");
            }
            //给block设置分页,不然翻页不成功
            EiBlock block = inInfo.getBlock("result");
            int offset = 0;
            int limit = 10;
            if (block != null){
                offset = block.getInt(EiConstant.offsetStr);
                limit = block.getInt(EiConstant.limitStr);
            }
            List<Map<String,Object>> list = dao.query("XFFB01.queryMsgAndPhoneStatus", map,offset,limit);
            outInfo.setRows("result", list);
            //查询数量总数并设置count,showCount也需要设置,不然显示条数不正确
            List count = dao.query("XFFB01.countMsgAndPhoneStatus", map);
            outInfo.set("result-count", count.get(0));
            outInfo.set("result-showCount", "true");
            //重新生成序号,并赋值给属性,前端展示该属性
            for (int i = 0; i < list.size(); i++) {
                list.get(i).put("num", offset + (i+1));
            }
        } catch (Exception e) {
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg("查询失败"+e.getMessage());
            return outInfo;
        }
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setMsg("查询成功");
        return outInfo;
    }

	/**
	 * 导出方法
	 * @param inInfo
	 * @return
	 */
	public EiInfo export(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//标题
			String excelTitle = "信息发布通知情况";
			Map<String, String> publishMap = new HashMap<String, String>() {{
				put("0", "发布失败");
				put("1", "发布成功");
			}};
			Map<String, String> receiveMap = new HashMap<String, String>() {{
				put("0", "未接听");
				put("1", "已接听");
			}};
			List<Map<String, String>> result = inInfo.getBlock("result").getRows();
			//筛选指定属性
			List<Map<String, String>> filteredList = result.stream()
					.map(map -> {
						//创建一个新的Map,仅包含需要的属性
						Map<String, String> filteredMap = new HashMap<>();
						filteredMap.put("num", map.get("num"));
						filteredMap.put("post", map.get("post"));
						filteredMap.put("name", map.get("name"));
						filteredMap.put("phoneNumber", map.get("phoneNumber"));
						filteredMap.put("publishResult", publishMap.get(map.get("fdPublishResult")));
						filteredMap.put("publishTime", map.get("publishTime"));
						//判断是消息通知的记录还是电话通知的记录
						if (map.get("fdType").equals("1")) {
							filteredMap.put("receiveResult", receiveMap.get(map.get("receiveResult")));
							filteredMap.put("replyTime", map.get("replyTime"));
							filteredMap.put("type", "电话通知");
						} else {
							filteredMap.put("type", "消息通知");
						}
						return filteredMap;
					})
					.collect(Collectors.toList());
			//数据组装
			List data = new ArrayList();
			if (filteredList.size() > 0) {
				List<String> cHead = new ArrayList<>(Arrays.asList(new String[]{"序号", "岗位", "姓名", "手机号", "发布状态", "发布时间"}));
				List<String> eHead = new ArrayList<>(Arrays.asList(new String[]{"num","post","name","phoneNumber","publishResult","publishTime"}));
				String type = filteredList.get(0).get("type");
				//判断是消息通知还是电话通知,区分不同的表头
				if (type.equals("消息通知")) {
					excelTitle = "信息发布消息通知情况";
//					cHead.add("通知类型");
//					eHead.add("type");
				} else {
					excelTitle = "信息发布电话通知情况";
					cHead.add("响应状态");
					cHead.add("响应时间");
//					cHead.add("通知类型");
					eHead.add("receiveResult");
					eHead.add("replyTime");
//					eHead.add("type");
				}
				//查询主表信息发布是否关联应急事件,是就在标题后加上
				String historyUuid = result.get(0).get("fdInfoHistoryUuid");
				if (StringUtils.isNotEmpty(historyUuid)) {
					Map map = new HashMap();
					map.put("fdUuids", historyUuid);
					List<Map> query = dao.query("XFFB01.query", map);
					if (query.size() > 0) {
						String eventName = (String) query.get(0).get("eventName");
						if (StringUtils.isNotEmpty(eventName)) {
							excelTitle = excelTitle +"——"+ eventName;
						}
					}
				}
				data.add(cHead);
				//遍历数据放入集合中
				for (int i = 0; i < filteredList.size(); i++) {
					List<String> bodyData = new ArrayList<>();
					for (int j = 0; j < cHead.size(); j++) {
						bodyData.add(filteredList.get(i).get(eHead.get(j)));
					}
					data.add(bodyData);
				}
			}
			excelToFileServe("信息发布通知情况-" + DateUtils.curDateMselStr17(),"sheet", data, excelTitle);
			System.out.println(data);
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("导出失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("导出成功");
		return outInfo;
	}

	/**
	 * excel文件生成到fileserve
	 * @param fileName 文件名
	 * @param dataList 数据行
	 * @param sheetName 工作簿名称
	 * @return 0：生成失败，
	 */
	public static String excelToFileServe(String fileName, String sheetName, List<List<String>> dataList, String excelTitle) {
//		HSSFWorkbook workbook = new HSSFWorkbook(); // 创建一个新的Excel工作簿
//		Sheet sheet = workbook.createSheet(sheetName); // 创建一个新的工作表
		HSSFWorkbook workbook = new HSSFWorkbook(); // 创建一个新的Excel工作簿
		Sheet sheet = workbook.createSheet(sheetName); // 创建一个新的工作表

		// 创建行和单元格
		Row row1 = sheet.createRow(0);
		Cell cell1 = row1.createCell(0);
		// 设置单元格合并
		CellRangeAddress region = new CellRangeAddress(0, 0, 0, dataList.get(0).size()-1); // 合并第一行第一列到第二列
		sheet.addMergedRegion(region);
		// 设置居中显示
		CellStyle style = workbook.createCellStyle();
		style.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
		style.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中
		cell1.setCellStyle(style);
		cell1.setCellValue(excelTitle); // 将数据写入单元格
		int rowNum = 1; // 行号
		for (List<String> rowData : dataList) { // 遍历数据列表，每个列表代表一行
			Row row = sheet.createRow(rowNum++); // 在工作表中创建新行
			int colNum = 0; // 列号
			for (String cellData : rowData) { // 遍历行中的数据，每个数据代表一个单元格
				Cell cell = row.createCell(colNum++); // 在当前行中创建新单元格
				cell.setCellValue(cellData); // 将数据写入单元格
			}
		}

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		try {
			workbook.write(outputStream); // 将工作簿写入字节数组输出流中
			workbook.close(); // 关闭工作簿资源
		} catch (IOException e) {
			return "0";
		}
		fileName = fileName+ DateUtil.format(DateUtil.date(), "yyyyMMdd")+".xlsx";
		return writeFileToFileServe(outputStream.toByteArray(),fileName);
	}

	private static String writeFileToFileServe(byte[] base64,String fileName){
		try {
			EiInfo fileInfo = new EiInfo();
			fileInfo.set("file", base64);
			fileInfo.set("fileName", fileName);
			fileInfo.set("path", "通讯录/");
			fileInfo.set(EiConstant.serviceId, "S_RF_02");
			EiInfo outInfo = XServiceManager.call(fileInfo);
			if(outInfo.getStatus() < 0){
				return "0";
			}
		}catch (Exception e){
			return "0";
		}
		return "1";
	}

	/**
	 * 一键重播方法
	 * @param inInfo
	 * @return
	 */
	public EiInfo replay(EiInfo inInfo) {
    	EiInfo outInfo = new EiInfo();
    	try {
			String uuids = inInfo.getString("uuids");
			inInfo.set("message_uuid", uuids);
			inInfo.set(EiConstant.serviceId, "S_XF_FB_05");
			outInfo = XServiceManager.call(inInfo);
			if (outInfo.getStatus() < 0) {
				throw new PlatException(outInfo.getMsg());
			}
			//将这些记录发布时间拼接起来
			inInfo.set("messageUuid", uuids);
			inInfo.set("fdType", "1");
			inInfo.set("fdReceiveResult", "0");
			inInfo.set("publishTime", DateUtils.curDateTimeStr19());
			dao.update("XFFB01.updatePublishTime", inInfo.getAttr());
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("一键重播失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("一键重播成功");
    	return outInfo;
	}

	/**
	 * 定时重播
	 * 查询最新一条记录,遍历查看最新记录的通知人员是否全部接听电话,没有全部接听就继续重播,直到全部接听为止
	 * 注意: 测试只能选择自己人,重播几次不接后看看发布时间是否有修改,并且发布时间字段长度才1024,一直重播肯定会报错
	 * 		还有看看接听之后还会不会打过去
	 * @param inInfo
	 * @return
	 */
	public EiInfo timingReplay(EiInfo inInfo) {
		try {
			List<Map<String,String>> list = dao.query("XFFB01.queryLatestReceiveRecord", new HashMap<>());
			boolean flag = false;
			for (int i = 0; i < list.size(); i++) {
				String result = list.get(i).get("receiveResult");
				// 判断接听状态,为0时就代表还有人没有接听,直接退出并修改flag状态
				if (result.equals("0")) {
					flag = true;
					break;
				}
			}
			if (flag && list.size() > 0) {
				String publishTime = list.get(0).get("publishTime");
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				// 解析给定的时间字符串为 LocalDateTime
				LocalDateTime givenTime = LocalDateTime.parse(publishTime, formatter);
				// 获取当前时间
				LocalDateTime currentTime = LocalDateTime.now();
				// 判断给定时间是否离当前时间超过一个小时
				boolean result = isWithinOneHour(givenTime, currentTime);
				// 超过一个小时就不要重播了
				if (!result) {
					String uuids = list.get(0).get("uuid");
					inInfo.set("message_uuid", uuids);
					inInfo.set(EiConstant.serviceId, "S_XF_FB_05");
					EiInfo outInfo = XServiceManager.call(inInfo);
					if (outInfo.getStatus() < 0) {
						throw new PlatException(outInfo.getMsg());
					}
					//将这些记录发布时间拼接起来
					inInfo.set("messageUuid", uuids);
					inInfo.set("fdType", "1");
					inInfo.set("fdReceiveResult", "0");
					inInfo.set("publishTime", DateUtils.curDateTimeStr19());
					dao.update("XFFB01.updatePublishTime", inInfo.getAttr());
				}
			}
		} catch (PlatException e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("重播失败"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("重播成功");
		return inInfo;
	}

	// 判断两个时间间隔是否超过1个小时
	public static boolean isWithinOneHour(LocalDateTime givenTime, LocalDateTime currentTime) {
		// 计算两个时间之间的差值
		Duration duration = Duration.between(givenTime, currentTime);
		// 判断差值是否超过一个小时（绝对值）
		return Math.abs(duration.toHours()) >= 1;
	}


}
