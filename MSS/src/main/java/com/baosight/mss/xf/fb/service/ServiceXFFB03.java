package com.baosight.mss.xf.fb.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.mss.common.constant.xf.fb.occEscalationStatusConstant;
import com.baosight.mss.utils.CommonUtils;
import com.baosight.mss.utils.LoginUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * OCC报表上传页面后台处理类
 * @author: lanyifu
 * @date: 2023/06/16 10:12:50
 */
public class ServiceXFFB03 extends ServiceBase {

	/**
	 * @description: 页面初始化方法，给前端设置假数据
	 * @param inInfo date(当天时间)
	 * @return 数据集
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		EiInfo outInfo = query(inInfo);
		return outInfo;
	}

	/**
	 * @description: OCC查询方法
	 * @param inInfo date 日期
	 * @return 上报历史记录集
	 */
	@Override
	public EiInfo query(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			String startTime = (String) inInfo.get("inqu_status-0-startTime");
			String endTime = (String) inInfo.get("inqu_status-0-endTime");
			String faultContent = (String) inInfo.get("inqu_status-0-faultContent");
			String faultType = (String) inInfo.get("inqu_status-0-dataFaultType");
			List<Map<String, String>> starTimeAndEndTime;
			//map条件
			Map map = new HashMap();
			//判断内容是否为空
			if (StringUtils.isNotBlank(faultContent)) {
				map.put("faultContent", faultContent);
			}
			//判断日期是否为空
			if (StringUtils.isNotBlank(startTime)) {
				map.put("startTime", startTime);
			} else {
				starTimeAndEndTime = CommonUtils.getStarTimeAndEndTime(1);
				map.put("startTime", starTimeAndEndTime.get(0).get("startTime"));
			}
			if (StringUtils.isNotBlank(endTime)) {
				map.put("endTime", endTime);
			} else {
				starTimeAndEndTime = CommonUtils.getStarTimeAndEndTime(1);
				map.put("endTime", starTimeAndEndTime.get(0).get("endTime"));
			}
			//给block设置分页,不然翻页不成功
			EiBlock block = inInfo.getBlock("result");
			//设置默认值,因为初始化时获取不到result的block
			int offset = 0;
			int limit = 20;
			if (block != null){
				offset = block.getInt(EiConstant.offsetStr);
				limit = block.getInt(EiConstant.limitStr);
			}
			//查询数据库后返回的集合
			List<Map<String, String>> list = dao.query("XFFB03.query", map,offset,limit);

			if (StringUtils.isNotBlank(faultType)) {
				for(int i=list.size()-1;i>=0;i--){
					JSONObject jsonObj = JSON.parseObject(list.get(i).get("fdContent"));
					if(faultType.equals("车辆故障")){
						Map gzType1 = (Map) jsonObj.get("fault");
						Map gzType2 = (Map) gzType1.get("cl");
						if((int)gzType2.get("sum")==0){
							list.remove(i);
						}
					}

					if(faultType.equals("信号故障")){
						Map gzType1 = (Map) jsonObj.get("fault");
						Map gzType2 = (Map) gzType1.get("xh");
						if((int)gzType2.get("sum")==0){
							list.remove(i);
						}
					}

					if(faultType.equals("供电故障")){
						Map gzType1 = (Map) jsonObj.get("fault");
						Map gzType2 = (Map) gzType1.get("gd");
						if((int)gzType2.get("sum")==0){
							list.remove(i);
						}
					}

					if(faultType.equals("门梯故障")){
						Map gzType1 = (Map) jsonObj.get("fault");
						Map gzType2 = (Map) gzType1.get("mt");
						if((int)gzType2.get("sum")==0){
							list.remove(i);
						}
					}

					if(faultType.equals("其他故障")){
						Map gzType1 = (Map) jsonObj.get("fault");
						Map gzType2 = (Map) gzType1.get("qt");
						if((int)gzType2.get("sum")==0){
							list.remove(i);
						}
					}

				}
			}


			outInfo.addRows("result", list);
			//查询数量总数并设置count,showCount也需要设置,不然显示条数不正确
			List count = dao.query("XFFB03.count", map);
			outInfo.set("result-count", count.get(0));
			outInfo.set("result-showCount", "true");
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("查询失败"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("查询成功");
		return outInfo;
	}

	/**
	 * @description 保存occ报表
	 * @param inInfo data json字符串
	 * @return
	 */
	public EiInfo insertOCC(EiInfo inInfo) {
		//获取前端JSON字符串
		String data = inInfo.getString("data");
		try {
            String userName = LoginUtils.getUserName((String) inInfo.get("userName"));
            //加入到数据库
			Map map = new HashMap<>();
			map.put("fdUuids", UUID.randomUUID().toString());
			map.put("fdContent", data);
			map.put("fdStatus", occEscalationStatusConstant.ESCALATION_EDIT);
			map.put("fdReportTime", DateUtils.curDateTimeStr19());
			map.put("fdDeleteFlag", "0");
			map.put("fdCreatedBy", LoginUtils.getUserName(userName));
			map.put("fdCreatedTime", DateUtils.curDateTimeStr19());
			map.put("fdUpdateBy", LoginUtils.getUserName(userName));
			map.put("fdUpdateTime", DateUtils.curDateTimeStr19());
			dao.insert("XFFB03.insert", map);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("保存失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("保存成功！");
		return inInfo;
	}

	/**
	 * @description 修改occ报表
	 * @param inInfo data json字符串
	 * @return
	 */
	public EiInfo updateOCC(EiInfo inInfo) {
		//获取前端JSON字符串
		String data = inInfo.getString("data");
		String uuid = (String) inInfo.get("uuid");
		try {
            String userName = LoginUtils.getUserName((String) inInfo.get("userName"));
			if (StringUtils.isEmpty(uuid)) {
				throw new PlatException("主键丢失,请重试");
			}
			Map map = new HashMap<>();
			map.put("fdUuids", uuid);
			map.put("fdContent", data);
			map.put("fdUpdateBy", userName);
			map.put("fdUpdateTime", DateUtils.curDateTimeStr19());
			dao.update("XFFB03.update", map);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("修改失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("修改成功！");
		return inInfo;
	}

	/**
	 * @description 删除occ报表
	 * @param inInfo
	 * @return
	 */
	public EiInfo deleteOCC(EiInfo inInfo) {
		String uuid = (String) inInfo.get("uuid");
		try {
			if (StringUtils.isEmpty(uuid)) {
				throw new PlatException("主键丢失,请重试");
			}
			Map map = new HashMap<>();
			map.put("fdUuids", uuid);
			dao.delete("XFFB03.delete", map);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("删除失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("删除成功！");
		return inInfo;
	}

	/**
	 * @description 上报事件后端处理方法
	 * @param inInfo data json字符串
	 * @return 上报历史结果集
	 */
	public EiInfo escalation(EiInfo inInfo) {
		//获取前端JSON字符串
		String data = inInfo.getString("data");
		String uuid = (String) inInfo.get("uuid");
		try {
			if (StringUtils.isEmpty(uuid)) {
				throw new PlatException("主键丢失,请重试");
			}
			String userName = LoginUtils.getUserName((String) inInfo.get("userName"));
			//加入到数据库
			Map map = new HashMap<>();
			map.put("fdUuids", uuid);
			map.put("fdContent", data);
			map.put("fdStatus", occEscalationStatusConstant.ESCALATION_SUCCESS);
			map.put("fdReportTime", inInfo.get("date"));
			map.put("fdUpdateBy", LoginUtils.getUserName(userName));
			map.put("fdUpdateTime", DateUtils.curDateTimeStr19());
			//先将数据推送给指标系统,看数据是否成功插入,如果成功则该条数据状态上报成功,反之上报失败
			EiInfo eiInfo = escalationDataToTepSystem(map);
			if (eiInfo.getStatus() != 1) {
				map.put("fdStatus", occEscalationStatusConstant.ESCALATION_FAIL);
			}
			map.put("fdReportTime", DateUtils.curDateTimeStr19());
			dao.update("XFFB03.update", map);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("上报失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("上报成功！");
		return inInfo;
	}

	/**
	 * @description 上报时调用指标系统接口
	 * @param dataMap-数据map
	 * @return
	 */
	public EiInfo escalationDataToTepSystem(Map dataMap) {
		EiInfo inInfo = new EiInfo();
		EiInfo outInfo = new EiInfo();
		try {
			//给接口设置参数，并调用接口
			JSONObject jsonObject = AssemblyJsonData(dataMap);
			inInfo.set("data", jsonObject);
			//todo 如果后面能直接用指标的微服务标识调用(S_NOCC_TEP_04)再替换
			inInfo.set(EiConstant.serviceId, "S_XF_FB_97"); //ServiceDV04-insertDailyFromOCCReport
			//执行并返回
			outInfo = XServiceManager.call(inInfo);
			//判断返回状态
			int status = outInfo.getStatus();
			if (status != 1) {
				outInfo.setStatus(EiConstant.STATUS_FAILURE);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("上报失败！"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("上报成功！");
		return outInfo;
	}

	/**
	 * 组装给指标接口的对象
	 * @param dataMap-occ上报时的数据map
	 * @return jsonObject-JSON对象
	 * @throws Exception
	 */
	public JSONObject AssemblyJsonData(Map dataMap) throws Exception {
		JSONObject jsonObject = new JSONObject();
		JSONObject faultJsonObject = new JSONObject();
		JSONObject indexJsonObject = new JSONObject();
		//解析json字符串
		String fdContent = (String) dataMap.get("fdContent");
		JSONObject sourceData = jsonObject.parseObject(fdContent);
		//获取第一层对象
		JSONObject fault = sourceData.getJSONObject("fault");
		JSONObject index = sourceData.getJSONObject("index");
		//获取第二层对象
		JSONObject cl = fault.getJSONObject("cl");
		JSONObject xh = fault.getJSONObject("xh");
		JSONObject gd = fault.getJSONObject("gd");
		JSONObject mt = fault.getJSONObject("mt");
		JSONObject qt = fault.getJSONObject("qt");
		JSONObject jd = fault.getJSONObject("jd");
		//组装fault的json对象数据
		faultJsonObject.put("vehicleDesc", cl.get("text"));
		faultJsonObject.put("vehicle", cl.get("sum"));
		faultJsonObject.put("sigDesc", xh.get("text"));
		faultJsonObject.put("sig", xh.get("sum"));
		faultJsonObject.put("powerDesc", gd.get("text"));
		faultJsonObject.put("power", gd.get("sum"));
		faultJsonObject.put("liftDesc", mt.get("text"));
		faultJsonObject.put("lift", mt.get("sum"));
		faultJsonObject.put("electromechanicalDesc", jd.get("text"));
		faultJsonObject.put("electromechanical", jd.get("sum"));
		faultJsonObject.put("otherDesc", qt.get("text"));
		faultJsonObject.put("other", qt.get("sum"));
		//组装index的JSON对象数据
		indexJsonObject.put("pass", index.get("lctg"));
		indexJsonObject.put("outage", index.get("lcty"));
		indexJsonObject.put("offline", index.get("lcxx"));
		indexJsonObject.put("detrain", index.get("lcqk"));
		indexJsonObject.put("engineering", index.get("gccl"));
		indexJsonObject.put("debugging", index.get("tscl"));
		indexJsonObject.put("kcjk", index.get("kcjk"));
		indexJsonObject.put("jkkc", index.get("jkkc"));
		indexJsonObject.put("constructionDesc", index.get("sgwt"));
		//组装最外层对象
		jsonObject.put("lineNumber", UserSession.getLoginName().equals("admin") ? "0100000000" : UserSession.getLoginName());
		jsonObject.put("date", dataMap.get("fdReportTime"));
		jsonObject.put("uploadTime", DateUtils.curDateTimeStr19());
		jsonObject.put("fault", faultJsonObject);
		jsonObject.put("index", indexJsonObject);
		return jsonObject;
	}

	/**
	 * 根据时间查询报表上报状态(查询指标系统的接口)
	 * @param inInfo
	 * @return EiInfo
	 */
	public EiInfo queryReportStatus(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("date", DateUtils.curDateTimeStr19());
			jsonObject.put("lineNumber", UserSession.getLoginName().equals("admin") ? "0100000000" : UserSession.getLoginName());
			inInfo.set("data", jsonObject);
			//todo 如果后面能直接用指标的微服务标识调用(S_NOCC_TEP_05)再替换
			inInfo.set(EiConstant.serviceId, "S_XF_FB_98");
			outInfo = XServiceManager.call(inInfo);
			//判断返回状态
			if (outInfo.getStatus() == -1) {
				throw new PlatException("查询数据异常！");
			} else {
				List data = (List) outInfo.get("data");
				if (data.size() > 0) {
					//返回已经上报的次数
					outInfo.set("count", data.get(0));
				}
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("查询失败！"+e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("查询成功！");
		return outInfo;
	}

	//获取信息发布OCC发布的记录,如果上报次数0时,获取昨天的OCC发布记录,如果上报次数1时,获取今天的OCC发布记录
	//遍历记录,每条记录要以1.开头,分号结束
	//除了信号故障、供电故障、车辆故障和门梯故障之外都归属于其他故障
	public EiInfo getOCCHistoryData(EiInfo info) {
		try {
			Integer reportCount = (Integer) info.get("reportCount");
			Integer day = -1;
			if (reportCount == 1) {
				day = 1;
			} else if (reportCount == 2) {//上报两次后就无需获取
				return info;
			}
			List<Map<String, String>> date = CommonUtils.getStarTimeAndEndTime(day);
			if (date.size() > 0) {
				HashMap hashMap = new HashMap();
				hashMap.put("etime", date.get(0).get("endTime"));
				hashMap.put("stime", date.get(0).get("startTime"));
				hashMap.put("type", "40050002");
				info.set("records", hashMap);
				info.set(EiConstant.serviceId, "S_XF_FB_06");
				EiInfo outInfo = XServiceManager.call(info);
				if (outInfo.getStatus() != -1) {
					List<Map<String,String>> data = (List<Map<String, String>>) outInfo.get("data");
					int xhCount = 0;
					int clCount = 0;
					int gdCount = 0;
					int mtCount = 0;
					int qtCount = 0;
					StringBuffer xhStr = new StringBuffer();
					StringBuffer clStr = new StringBuffer();
					StringBuffer gdStr = new StringBuffer();
					StringBuffer mtStr = new StringBuffer();
					StringBuffer qtStr = new StringBuffer();
					for (int i = 0; i < data.size(); i++) {
						String content = data.get(i).get("content");
						String publishType = data.get(i).get("publishType");
						switch (publishType) {
							case "信号故障通知":
								xhCount++;
								xhStr.append(xhCount).append(".").append(content);
								appendStr(xhStr);
								break;
							case "车辆故障通知":
								clCount++;
								clStr.append(clCount).append(".").append(content);
								appendStr(clStr);
								break;
							case "供电故障通知":
								gdCount++;
								gdStr.append(gdCount).append(".").append(content);
								appendStr(gdStr);
								break;
							case "站台门故障通知":
								mtCount++;
								mtStr.append(mtCount).append(".").append(content);
								appendStr(mtStr);
								break;
							default:
								qtCount++;
								qtStr.append(qtCount).append(".").append(content);
								appendStr(qtStr);
						}
					}
					//组装数据
					String objStr = jsonStringData(xhStr.toString(), clStr.toString(), gdStr.toString(),
							mtStr.toString(), qtStr.toString(), xhCount, clCount, gdCount, mtCount, qtCount);
					info.set("objStr", objStr);
				}
			}
		} catch (Exception e) {
			return info;
		}
		return info;
	}

	/**
	 * 字符串替换和添加,当最后一个字符为句号时将他替换为分号,找不到就直接加分号
	 * @param str
	 */
	public void appendStr(StringBuffer str) {
		int indexOf = str.lastIndexOf("。");
		if (indexOf != -1) {
			str.replace(indexOf,indexOf+1,"；");
		} else {
			str.append("；");
		}
	}

	/**
	 * 组装JSON
	 * @param xhStr-信号故障内容
	 * @param clStr-车辆故障内容
	 * @param gdStr-供电故障内容
	 * @param mtStr-门梯故障内容
	 * @param qtStr-其他故障内容
	 * @param xhCount-信号故障数量
	 * @param clCount-车辆故障数量
	 * @param gdCount-供电故障数量
	 * @param mtCount-门梯故障数量
	 * @param qtCount-其他故障数量
	 * @return
	 */
	public String jsonStringData(String xhStr,String clStr,String gdStr,String mtStr,String qtStr,
								 int xhCount,int clCount,int gdCount,int mtCount,int qtCount) {
		JSONObject obj = new JSONObject();
		JSONObject fault = new JSONObject();
		JSONObject cl = new JSONObject();
		cl.put("sum", clCount);
		cl.put("text", clStr);
		JSONObject xh = new JSONObject();
		xh.put("sum", xhCount);
		xh.put("text", xhStr);
		JSONObject gd = new JSONObject();
		gd.put("sum", gdCount);
		gd.put("text", gdStr);
		JSONObject mt = new JSONObject();
		mt.put("sum", mtCount);
		mt.put("text", mtStr);
		JSONObject qt = new JSONObject();
		qt.put("sum", qtCount);
		qt.put("text", qtStr);

		fault.put("cl", cl);
		fault.put("xh", xh);
		fault.put("gd", gd);
		fault.put("mt", mt);
		fault.put("qt", qt);

		obj.put("fault", fault);

		JSONObject index = new JSONObject();
		index.put("lctg", "");
		index.put("lcty", "");
		index.put("lcxx", "");
		index.put("lcqk", "");
		index.put("gccl", "");
		index.put("tscl", "");
		index.put("kcjk", "");
		index.put("jkkc", "");
		index.put("sgwt", "");

		obj.put("index", index);

		return obj.toJSONString();
	}

}
