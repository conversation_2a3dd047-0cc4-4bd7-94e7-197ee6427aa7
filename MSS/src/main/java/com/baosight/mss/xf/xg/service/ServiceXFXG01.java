package com.baosight.mss.xf.xg.service;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.mss.common.constant.xf.xg.TemplateTargetConstant;
import com.baosight.mss.utils.CommonUtils;
import com.baosight.mss.utils.EiInfoUtils;
import com.baosight.mss.utils.LoginUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模板管理页面后台处理类
 * @author: lanyifu
 * @date: 2023/05/30 09:31:21
 */
public class ServiceXFXG01 extends ServiceBase {

	/**
	 * @description: 初始化方法
	 * @param inInfo
	 * @return 数据集
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		EiInfo outInfo = query(inInfo);
		//初始化专业
		queryMajor(outInfo);
		return outInfo;
	}

	/**
	 * @description PCC模块查询方法
	 * @param inInfo
	 * @return result-PCC模板信息block
	 */
	@Override
	public EiInfo query(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			Map map = new HashMap();
			map.put("fdTarget", TemplateTargetConstant.PCC);
			//获取输入框数据
			String filterText = (String) inInfo.get("filterText");
			if (StringUtils.isNotEmpty(filterText)) {
				map.put("pccLike", filterText);
			}
			//给block设置分页,不然翻页不成功
			EiBlock block = inInfo.getBlock("result");
			//设置默认值,因为初始化时获取不到result的block
			int offset = 0;
			int limit = 20;
			if (block != null){
				offset = block.getInt(EiConstant.offsetStr);
				limit = block.getInt(EiConstant.limitStr);
			}
			//查询并将查询集合设置到outInfo中
			List<Map<String,Object>> list = dao.query("XFXG01.query", map, offset, limit);
			//查询数量总数并设置count,showCount也需要设置,不然显示条数不正确
			List count = dao.query("XFXG01.count", map);
			outInfo.addRows("result", list);
			outInfo.set("result-count", count.get(0));
			outInfo.set("result-showCount", "true");
			//重新生成序号,并赋值给属性,前端展示该属性
			for (int i = 0; i < list.size(); i++) {
				list.get(i).put("num", offset + (i+1));
			}
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 应急模块查询方法
	 * @param inInfo
	 * @return yjResult-应急模板信息block
	 */
	public EiInfo queryYjTemplate(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			Map map = new HashMap();
			map.put("fdTarget", TemplateTargetConstant.EMERGENCY);
			//获取输入框数据
			String filterText = (String) inInfo.get("filterText");
			if (StringUtils.isNotEmpty(filterText)) {
				map.put("cmpLike", filterText);
			}
			//给block设置分页,不然翻页不成功
			EiBlock block = inInfo.getBlock("yjResult");
			//设置默认值,因为初始化时获取不到result的block
			int offset = 0;
			int limit = 20;
			if (block != null){
				offset = block.getInt(EiConstant.offsetStr);
				limit = block.getInt(EiConstant.limitStr);
			}
			//查询并将查询集合设置到outInfo中
			List<Map<String,Object>> list = dao.query("XFXG01.query", map, offset, limit);
			//查询数量总数并设置count,showCount也需要设置,不然显示条数不正确
			List count = dao.query("XFXG01.count", map);
			outInfo.addRows("yjResult", list);
			outInfo.set("yjResult-count", count.get(0));
			outInfo.set("yjResult-showCount", "true");
			//重新生成序号,并赋值给属性,前端展示该属性
			for (int i = 0; i < list.size(); i++) {
				list.get(i).put("num", offset + (i+1));
			}
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 新增PCC模板信息
	 * @param inInfo insert-数据块 infoContent-信息内容 fdTarget-发布目标
	 * @return
	 */
	public EiInfo insert(EiInfo inInfo) {
		//获取需要插入的数据
		List<Map> modelList = (List<Map>)inInfo.get("insert");
		String infoContent = inInfo.getString("fdContent");
		String fdTarget = inInfo.getString("fdTarget");
		String uuid = UUID.randomUUID().toString();
		try {
			for (int i = 0; i < modelList.size(); i++) {
				Map map =new HashMap();
				map.put("scene",modelList.get(i).get("fdScene"));
				map.put("fdClass",modelList.get(i).get("fdClass"));
				//根据事件情景和类别查询
				List list = dao.query("XFXG01.query", map);
				//判断事件情景和类别是否重复
				if (list.size() > 0) {
					inInfo.setStatus(EiConstant.STATUS_FAILURE);
					inInfo.setMsg("事件情景和类别不能重复");
					return inInfo;
				}
				Map hashMap = new HashMap();
				hashMap.put("fdUuids", uuid);
				hashMap.put("fdScene", modelList.get(i).get("fdScene"));
				hashMap.put("fdClass", modelList.get(i).get("fdClass"));
				hashMap.put("fdContent", infoContent);
				hashMap.put("fdTarget", fdTarget);
				hashMap.put("fdDeleteFlag", "0");
				hashMap.put("fdCreatedBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				hashMap.put("fdCreatedTime", DateUtils.curDateTimeStr19());
				hashMap.put("fdUpdateBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				hashMap.put("fdUpdateTime", DateUtils.curDateTimeStr19());
				dao.insert("XFXG01.insert",hashMap);
			}
		}catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("新增失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("新增成功！");
		return inInfo;
	}

	/**
	 * @description 新增应急模板信息
	 * @param inInfo insert-数据块 infoContent-信息内容 fdTarget-发布目标
	 * @return
	 */
	public EiInfo insertCMP(EiInfo inInfo) {
		//获取需要插入的数据
		List<Map> modelList = (List<Map>)inInfo.get("insert");
		String infoContent = inInfo.getString("fdContent");
		String fdTarget = inInfo.getString("fdTarget");
		String uuid = UUID.randomUUID().toString();
		try {
			for (int i = 0; i < modelList.size(); i++) {
				Map hashMap = new HashMap();
				hashMap.put("fdUuids", uuid);
				hashMap.put("fdType", modelList.get(i).get("fdType"));
				hashMap.put("fdScene", modelList.get(i).get("fdScene"));
				hashMap.put("fdClass", modelList.get(i).get("fdClass"));
				hashMap.put("fdMajor", modelList.get(i).get("fdMajor"));
				hashMap.put("fdContent", infoContent);
				hashMap.put("fdTarget", fdTarget);
				hashMap.put("fdDeleteFlag", "0");
				hashMap.put("fdCreatedBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				hashMap.put("fdCreatedTime", DateUtils.curDateTimeStr19());
				hashMap.put("fdUpdateBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				hashMap.put("fdUpdateTime", DateUtils.curDateTimeStr19());
				dao.insert("XFXG01.insert",hashMap);
			}
			/*=====================================*/
			//将模板类型信息发送给智能应急调度系统
			pushDataMethod(inInfo);
		}catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("新增失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("新增成功！");
		return inInfo;
	}

	/**
	 * @description 修改PCC模板信息
	 * @param inInfo update-数据块 infoContent-信息内容 contentUpdateFlag-内容修改标识
	 * @return
	 */
	public EiInfo update(EiInfo inInfo) {
		//获取需要修改的数据
		List<Map> modelList = (List<Map>)inInfo.get("update");
		String infoContent = inInfo.getString("fdContent");
		try {
			for (int i = 0; i < modelList.size(); i++){
				Map hashMap = new HashMap();
				hashMap.put("fdUuids",modelList.get(i).get("fdUuids"));
				hashMap.put("fdScene", modelList.get(i).get("fdScene"));
				hashMap.put("fdClass", modelList.get(i).get("fdClass"));
				hashMap.put("fdContent", infoContent);
				hashMap.put("fdUpdateBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				hashMap.put("fdUpdateTime", DateUtils.curDateTimeStr19());
				dao.update("XFXG01.update",hashMap);
				//修改完之后再查询一遍
				Map map = new HashMap();
				map.put("scene", modelList.get(i).get("fdScene"));
				map.put("fdClass", modelList.get(i).get("fdClass"));
				//根据事件情景和类别查询
				List<Map<String,String>> list = dao.query("XFXG01.query", map);
				//判断事件情景和类别是否重复
				if (list.size() > 1) {
					inInfo.setStatus(EiConstant.STATUS_FAILURE);
					inInfo.setMsg("事件情景和类别不能重复");
					return inInfo;
				}
			}
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("修改失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("修改成功！");
		return inInfo;
	}

	/**
	 * @description 修改应急模板信息
	 * @param inInfo update-数据块 infoContent-信息内容
	 * @return
	 */
	public EiInfo updateCMP(EiInfo inInfo) {
		//获取需要修改的数据
		List<Map> modelList = (List<Map>)inInfo.get("update");
		String infoContent = inInfo.getString("fdContent");
		try {
			for (int i = 0; i < modelList.size(); i++){
				Map hashMap = new HashMap();
				hashMap.put("fdUuids",modelList.get(i).get("fdUuids"));
				hashMap.put("fdType", modelList.get(i).get("fdType"));
				hashMap.put("fdScene", modelList.get(i).get("fdScene"));
				hashMap.put("fdClass", modelList.get(i).get("fdClass"));
				hashMap.put("fdMajor", modelList.get(i).get("fdMajor"));
				hashMap.put("fdContent", infoContent);
				hashMap.put("fdUpdateBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				hashMap.put("fdUpdateTime", DateUtils.curDateTimeStr19());
				dao.update("XFXG01.update",hashMap);
			}
			/*=====================================*/
			//将模板类型信息发送给智能应急调度系统
			pushDataMethod(inInfo);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("修改失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("修改成功！");
		return inInfo;
	}

	/**
	 * @description 删除模板信息
	 * @param inInfo delete-数据块 fdTarget-发布目标
	 * @return
	 */
	public EiInfo delete(EiInfo inInfo) {
		//获取需要删除的数据
		List<Map> modelList = (List<Map>)inInfo.get("delete");
		String fdTarget = inInfo.getString("fdTarget");
		try {
			//连接数据库删除、批量删除
			for (int i = 0; i < modelList.size(); i++){
				Map hashmap = new HashMap();
				hashmap.put("fdUuids",modelList.get(i).get("fdUuids").toString());
				dao.delete("XFXG01.delete", hashmap);
			}

			/*=====================================*/
			//将模板类型信息发送给智能应急调度系统
			//如果是应急模板删除时才推送给应急调度系统
			if(StringUtils.isNotEmpty(fdTarget) && fdTarget.equals(TemplateTargetConstant.EMERGENCY)) {
                pushDataMethod(inInfo);
			}

		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg("删除失败！"+e.getMessage());
			return inInfo;
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg("删除成功！");
		return inInfo;
	}

	/**
	 * @description 导入数据的后台方法
	 * @param inInfo
	 * @return
	 */
	public EiInfo importFile(EiInfo inInfo){
		EiInfo eiInfo1 = new EiInfo();
		try {
			//1.通过urlStr(filePath)从file server上获取文件字节流
			inInfo.set(EiConstant.serviceId, "S_RF_01");// RF01->downLoadFromUrl
			EiInfo outInfo = XServiceManager.call(inInfo);
			//注意必须对outInfo的status状态进行校验
			if (outInfo.getStatus() < 0) {
				throw new PlatException(outInfo.getMsg());
			}
			//获取导入标识,到底是pcc模板导入还是应急模板导入
			String importFileFlag = (String) inInfo.getAttr().get("importFileFlag");
			//判断是PCC模板导入还是应急模板导入
			if (StringUtils.isNotEmpty(importFileFlag) && importFileFlag.equals("pcc")) {
				outInfo.set("command", "import_pcc");//根据不同模板不同command
				outInfo.set(EiConstant.serviceId, "S_FS_01");
				eiInfo1 = XServiceManager.call(outInfo);
				//注意必须对outInfo的status状态进行校验
				if (eiInfo1.getStatus() < 0) {
					throw new PlatException(eiInfo1.getMsg());
				}
				List<Map<String,Object>> data = (List<Map<String, Object>>) eiInfo1.get("data");
				if (data.size() > 0) {
					EiInfo result = checkPccData(data,inInfo);
					List<Map<String, Object>> resultData = (List<Map<String, Object>>) result.get("data");
					int status = result.getStatus();
					//如果返回1,表示数据没有问题,可以插入数据库;如果返回-1表示数据有问题,返回错误信息
					if (status != 1) {
						eiInfo1.set("errorInfo", resultData);
						throw new PlatException("有错误数据");
					}
					resultData = (List<Map<String, Object>>) result.get("data");
					//判断insertData集合是否有数据
					if (resultData.size() > 0) {
						dao.insertBatch("XFXG01.insert", resultData);
						eiInfo1.set("importTotal", resultData.size());
					}
				}
			} else if (StringUtils.isNotEmpty(importFileFlag) && importFileFlag.equals("emergency")) {
				outInfo.set("command", "import_emergency");//根据不同模板不同command，上一方法同样
				outInfo.set(EiConstant.serviceId, "S_FS_01");
				eiInfo1 = XServiceManager.call(outInfo);
				//注意必须对outInfo的status状态进行校验
				if (eiInfo1.getStatus() < 0) {
					throw new PlatException(eiInfo1.getMsg());
				}
				List<Map<String,Object>> data = (List<Map<String, Object>>) eiInfo1.get("data");
				if (data.size() > 0) {
					//插入数据库的方法
					EiInfo result = checkExcelData(data,inInfo);
					List<Map<String, Object>> resultData = (List<Map<String, Object>>) result.get("data");
					int status = result.getStatus();
					//如果返回1,表示数据没有问题,可以插入数据库;如果返回-1表示数据有问题,返回错误信息
					if (status != 1) {
						eiInfo1.set("errorInfo", resultData);
						throw new PlatException("有错误数据");
					}
					resultData = (List<Map<String, Object>>) result.get("data");
					//判断insertData集合是否有数据
					if (resultData.size() > 0) {
						dao.insertBatch("XFXG01.insert", resultData);
						eiInfo1.set("importTotal", resultData.size());
						/*=====================================*/
						//将模板类型信息发送给智能应急调度系统
						pushDataMethod(inInfo);
					}
				}
			}
		} catch (Exception e) {
			eiInfo1.setStatus(EiConstant.STATUS_FAILURE);
			eiInfo1.setMsg("导入失败" + e.getMessage());
			return eiInfo1;
		}
		eiInfo1.setStatus(EiConstant.STATUS_SUCCESS);
		eiInfo1.setMsg("导入成功");
		return eiInfo1;
	}

	/**
	 * Excel数据导入数据库方法
	 * @param data-Excel文件解析后的数据
	 */
	public EiInfo checkExcelData(List<Map<String,Object>> data,EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		//记录错误信息
		List<Map<String, Object>> errorList = new ArrayList<>();
		//插入数据库的集合
		List<Map<String, Object>> insertData = new ArrayList<>();
		try {
			//获取发布类别和专业的枚举值,查小代码
			Map map = new HashMap();
			map.put("codeType","mss.type");
			//获取类型小代码
			List<Map<String,String>> typeList = dao.query("XFXG01.queryCodes", map);
			Map<String,String> typeMap = new HashMap<>();
			//将类别小代码放取出来放到map中,然后根据key就能获取value
			for (int i = 0; i < typeList.size(); i++) {
				typeMap.put(typeList.get(i).get("itemCname"), typeList.get(i).get("itemCode"));
			}
			map.clear();
			map.put("codeType","mss.major");
			//获取专业小代码
			EiInfo inInfo1 = new EiInfo();
			inInfo1.set(EiConstant.serviceId, "S_XF_XG_11");
			EiInfo eiInfo = XServiceManager.call(inInfo1);
			if (eiInfo.getStatus() < 0) {
				throw new PlatException("专业数据获取失败");
			}
			List<Map<String,String>> majorList = (List<Map<String, String>>) eiInfo.get("data");
			Map<String,String> majorMap = new HashMap<>();
			//将专业小代码放取出来放到map中,然后根据key就能获取value
			for (int i = 0; i < majorList.size(); i++) {
				majorMap.put(majorList.get(i).get("profession"), majorList.get(i).get("profession_id"));
			}
			//拿小代码的中文名、枚举值值和Excel导入的类型中文名进行对比,数据库存的是枚举值
			for (int i = 0; i < data.size(); i++) {
				//类别枚举值
				StringBuilder typeNumber = new StringBuilder();
				//获取类别
				String type = (String) data.get(i).get("class");
				//判断类别是否为空,不为空就先匹配是否匹配到,匹配不到记录错误信息
				if (StringUtils.isNotEmpty(type)) {
					//匹配类别,获取枚举值,根据key获取value
					typeNumber.append(typeMap.get(type));
					//获取该类别下的模板数据
					ArrayList<Map<String,String>> rows = (ArrayList) data.get(i).get("rows");
					//判断集合是否有数据
					if (rows.size() > 0) {
						//遍历集合,将集合值获取再设置到hashMap中
						for (int j = 0; j < rows.size(); j++) {
							//记录错误字符串和map
							StringBuffer errString = new StringBuffer("");
							Map<String,Object> errorMap = new HashMap();

							Map hashMap = new HashMap();
							//判断名称、发布阶段、内容、专业不能为空
							String name = rows.get(j).get("name");
							String stage = rows.get(j).get("stage");
							String content = rows.get(j).get("content");
							String major = rows.get(j).get("major");
							//名称
							if (StringUtils.isNotEmpty(name)) {
								hashMap.put("fdScene", name);
							} else {
								errString.append("第" +(i+1)+ "行: 名称不能为空; ");
							}
							//发布阶段
							if (StringUtils.isNotEmpty(stage)) {
								hashMap.put("fdClass", stage);
							} else {
								errString.append("第" +(i+1)+ "行: 发布阶段不能为空; ");
							}
							//内容
							if (StringUtils.isNotEmpty(content)) {
								hashMap.put("fdContent", content);
							} else {
								errString.append("第" +(i+1)+ "行: 内容不能为空; ");
							}
							//判断类别枚举值是否存在
							if (StringUtils.isNotEmpty(typeNumber.toString())) {
								hashMap.put("fdType", typeNumber.toString());
							} else {
								errString.append("第" +(i+1)+ "行: " +type+ "类别不存在; ");
							}
                            /*专业特殊处理:先判断是否为空,为空记录错误信息;不为空匹配专业,获取枚举值,
                              如果匹配不到专业,记录错误信息
                            */
							if (StringUtils.isNotEmpty(major)) {
								StringBuilder majorNumber = new StringBuilder();
								//判断是否存在、号,存在就先拆分,拆分后匹配获取专业枚举值,最后拼接
								if (major.indexOf("，") != -1) {
									//根据符号拆分，然后加入到集合中
									String[] splitMajor = major.split("，");
									for (int k = 0; k < splitMajor.length; k++) {
										//判断是否匹配
										if (StringUtils.isEmpty(majorMap.get(splitMajor[k]))) {
											errString.append("第" +(i+1)+ "行: " +major+ "专业不存在; ");
										} else {
											majorNumber.append(majorMap.get(splitMajor[k]));
											majorNumber.append(",");
										}
									}
								} else {
									//判断是否匹配
									if (StringUtils.isEmpty(majorMap.get(major))) {
										errString.append("第" +(i+1)+ "行: " +major+ "专业不存在; ");
									} else {
										majorNumber.append(majorMap.get(major));
										majorNumber.append(",");
									}
								}
								//将拼接好的专业编号设置到hashMap里
								hashMap.put("fdMajor", majorNumber.deleteCharAt(majorNumber.lastIndexOf(",")).toString());
							} else {
								errString.append("第" +(i+1)+ "行: 专业不能为空; ");
							}
							hashMap.put("fdUuids", UUID.randomUUID().toString());
							hashMap.put("fdTarget", TemplateTargetConstant.EMERGENCY);
							hashMap.put("fdDeleteFlag", "0");
							hashMap.put("fdCreatedBy", LoginUtils.getUserName((String) inInfo.get("userName")));
							hashMap.put("fdCreatedTime", DateUtils.curDateTimeStr19());
							hashMap.put("fdUpdateBy", LoginUtils.getUserName((String) inInfo.get("userName")));
							hashMap.put("fdUpdateTime", DateUtils.curDateTimeStr19());

							//当有错误的数据时,记录下来
							if(errString.length() > 3){
								errorMap.put("message", errString.toString());
								errorList.add(errorMap);
							} else {
								//将hashMap添加到集合中
								insertData.add(hashMap);
							}

						}
					}
				}
			}

			//如果errorList有数据,表示有不符合规定的数据,抛出异常走异常方法
			if (errorList.size() > 0) {
				throw new Exception("有不符合规定的数据");
			}

		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据解析失败：" + e.getMessage());
			outInfo.set("data", errorList);
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据解析成功！");
		outInfo.set("data", insertData);
		return outInfo;
	}

	/**
	 * PCC模板导入校验
	 * @param data-Excel文件解析后的数据
	 */
	public EiInfo checkPccData(List<Map<String,Object>> data,EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		//记录错误信息
		List<Map<String, Object>> errorList = new ArrayList<>();
		//插入数据库的集合
		List<Map<String, Object>> insertData = new ArrayList<>();
		try {

			//先将解析的数据进行重复过滤
			List<Map<String, Object>> distinctData = data.stream().distinct().collect(Collectors.toList());
			//遍历全部数据
			for (int i = 0; i < distinctData.size(); i++) {
				//记录错误字符串和map
				StringBuffer errString = new StringBuffer("");
				Map<String,Object> errorMap = new HashMap();
				String scene = distinctData.get(i).get("scence").toString();
				String fdClass = distinctData.get(i).get("class").toString();
				String content = distinctData.get(i).get("content").toString();
				//判断情景、类别、内容是否为空
				if (StringUtils.isEmpty(scene)) {
					errString.append("第" +(i+1)+ "行: 事件情景不能为空; ");
				}
				if (StringUtils.isEmpty(fdClass)) {
					errString.append("第" +(i+1)+ "行: 类别不能为空; ");
				}
				if (StringUtils.isEmpty(content)) {
					errString.append("第" +(i+1)+ "行: 内容不能为空; ");
				}
				Map map = new HashMap();
				map.put("fdUuids", UUID.randomUUID().toString());
				map.put("fdScene", distinctData.get(i).get("scence"));
				map.put("fdClass", distinctData.get(i).get("class"));
				map.put("fdContent", distinctData.get(i).get("content"));
				map.put("fdTarget", TemplateTargetConstant.PCC);
				map.put("fdDeleteFlag", "0");
				map.put("fdCreatedBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				map.put("fdCreatedTime", DateUtils.curDateTimeStr19());
				map.put("fdUpdateBy", LoginUtils.getUserName((String) inInfo.get("userName")));
				map.put("fdUpdateTime", DateUtils.curDateTimeStr19());

				//当有错误的数据时,记录下来
				if(errString.length() > 3){
					errorMap.put("message", errString.toString());
					errorList.add(errorMap);
				} else {
					//将map添加到集合中
					insertData.add(map);
				}
			}

			//如果errorList有数据,表示有不符合规定的数据,抛出异常走异常方法
			if (errorList.size() > 0) {
				throw new Exception("有不符合规定的数据");
			}

		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据解析失败：" + e.getMessage());
			outInfo.set("data", errorList);
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据解析成功！");
		outInfo.set("data", insertData);
		return outInfo;
	}

	/**
	 * pcc模板导出
	 * @param inInfo
	 * @return
	 */
	public EiInfo exportPccTemplate(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			ArrayList idList = (ArrayList) inInfo.get("pccIdList");
			String search = (String) inInfo.get("search");
			//1.获取数据
			Map map = new HashMap();
			if (idList.size() > 0) {
				map.put("idList", idList);
			}
			if (StringUtils.isNotEmpty(search)) {
				map.put("pccLike", search);
			}
			map.put("target", TemplateTargetConstant.PCC);
			List<Map<String, Object>> data = dao.query("XFXG01.queryPccTemplate", map);

			//2.将数据传给解析接口拿到文件流
			EiInfo outInfo1 = new EiInfo();
			inInfo.set("command", "export_pcc");
			inInfo.set("data", data);
			inInfo.set(EiConstant.serviceId, "S_FS_02");
			outInfo1 =  XServiceManager.call(inInfo);
			if (outInfo1.getStatus() == -1) {
				throw new PlatException("导出解析失败" + outInfo1.getMsg());
			}
			byte[] fileData = outInfo1.toJSON().getBytes("pccByte");

			//3.上传至fileServer
			EiInfo eiInfo = new EiInfo();
			//导出文件名称加时间
			StringBuilder exportFileName = new StringBuilder();
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
			exportFileName.append("PCC模板-");
			exportFileName.append(dateFormat.format(new Date()));
			exportFileName.append(".xlsx");
			eiInfo.set("fileName", exportFileName.toString());
			eiInfo.set("file", fileData);
			eiInfo.set("path", "PCC发布/");
			eiInfo.set(EiConstant.serviceId,"S_RF_02");
			outInfo = XServiceManager.call(eiInfo);
			if (outInfo.getStatus() == -1) {
				throw new PlatException("导出到fileServer失败" + outInfo.getMsg());
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据导出失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据导出成功！");
		return outInfo;
	}

	/**
	 * 应急模板导出
	 * @param inInfo
	 * @return
	 */
	public EiInfo exportCmpTemplate(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			ArrayList idList = (ArrayList) inInfo.get("cmpIdList");
			String search = (String) inInfo.get("search");
			//1.获取数据
			Map map = new HashMap();
			if (idList.size() > 0) {
				map.put("idList", idList);
			}
			if (StringUtils.isNotEmpty(search)) {
				map.put("cmpLike", search);
			}
			map.put("target", TemplateTargetConstant.EMERGENCY);
			List<Map<String, Object>> data = dao.query("XFXG01.queryCmpTemplate", map);
			//获取专业小代码
			Map map1 = new HashMap();
			map.put("codeType","mss.major");
			List<Map<String,String>> majorList = dao.query("XFXG01.queryCodes", map1);
			//重新组装数据
			List<Map<String, Object>> newData = new ArrayList<>();
			for (int i = 0; i < data.size(); i++) {
				Map hashMap = new HashMap<>();
				hashMap.put("name", data.get(i).get("name"));
				hashMap.put("stage", data.get(i).get("stage"));
				hashMap.put("content", data.get(i).get("content"));
				hashMap.put("class", data.get(i).get("class"));
				String majorNumber = (String) data.get(i).get("major");
				//专业编号处理
				String majorName = getMajorNameByNumber(majorList, majorNumber);
				hashMap.put("major", majorName);
				newData.add(hashMap);
			}
			//2.将数据传给解析接口拿到文件流
			EiInfo outInfo1 = new EiInfo();
			inInfo.set("command", "export_emergency");
			inInfo.set("data", newData);
			inInfo.set(EiConstant.serviceId, "S_FS_02");
			outInfo1 =  XServiceManager.call(inInfo);
			if (outInfo1.getStatus() == -1) {
				throw new PlatException("导出解析失败" + outInfo1.getMsg());
			}
			byte[] fileData = outInfo1.toJSON().getBytes("emergencyByte");

			//3.上传至fileServer
			EiInfo eiInfo = new EiInfo();
			//导出文件名称加时间
			StringBuilder exportFileName = new StringBuilder();
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
			exportFileName.append("应急指挥模板-");
			exportFileName.append(dateFormat.format(new Date()));
			exportFileName.append(".xlsx");
			eiInfo.set("fileName", exportFileName.toString());
			eiInfo.set("file", fileData);
			eiInfo.set("path", "模板管理/");
			eiInfo.set(EiConstant.serviceId,"S_RF_02");
			outInfo = XServiceManager.call(eiInfo);
			if (outInfo.getStatus() == -1) {
				throw new PlatException("导出到fileServer失败" + outInfo.getMsg());
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据导出失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据导出成功！");
		return outInfo;
	}

	/**
	 * 根据专业编号获取专业名称
	 * @param majorList-专业小代码list
	 * @param majorNumber-专业编号字符串
	 * @return
	 */
	public String getMajorNameByNumber(List<Map<String,String>> majorList, String majorNumber) {
		StringBuilder majorName = new StringBuilder();
		//将小代码放入Map中,后面用到直接根据key获取value(根据编号获取名称)
		Map<String,String> majorMap = new HashMap();
		majorList.stream().forEach(items -> {
			majorMap.put(items.get("itemCode"), items.get("itemCname"));
		});
		if (majorNumber.indexOf(",") != -1) {
			//根据符号拆分，然后加入到集合中
			String[] majorArray = majorNumber.split(",");
			for (int j = 0; j < majorArray.length; j++) {
				majorName.append(majorMap.get(majorArray[j])).append(",");
			}
			//去掉最后的,号
			majorName.deleteCharAt(majorName.lastIndexOf(","));
		} else {
			majorName.append(majorMap.get(majorNumber));
		}
		return majorName.toString();
	}

	/**
	 * @description 查询PCC模板,提供给信息发布模板进行绑定
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryPCCTemplate(EiInfo inInfo){
		EiInfo outInfo = new EiInfo();
		try {
			//给block设置分页,不然翻页不成功
			EiBlock block = inInfo.getBlock("result2");
			int offset = 0;
			int limit = 20;
			if (block != null){
				offset = block.getInt(EiConstant.offsetStr);
				limit = block.getInt(EiConstant.limitStr);
			}
			inInfo.set("fdTarget", TemplateTargetConstant.PCC);
			// inInfo.set("fdClass", "事发车站"); // 信息发布只能绑定类别为“事发车站”的PCC模板
			List<Map<String,Object>> list = dao.query("XFXG01.query", inInfo.getAttr(),offset, limit);
			List count = dao.query("XFXG01.count", inInfo.getAttr());
			outInfo.addRows("result2",list);
			outInfo.set("result2-count", count.get(0));
			outInfo.set("result2-showCount", "true");
			//重新生成序号,并赋值给属性,前端展示该属性
			for (int i = 0; i < list.size(); i++) {
				list.get(i).put("num", offset + (i+1));
			}
		}catch (Exception e){
			return CommonUtils.setMessage(outInfo,EiConstant.STATUS_FAILURE,"数据查询失败：" + e.getMessage());
		}
		return CommonUtils.setMessage(outInfo,EiConstant.STATUS_SUCCESS,"数据查询成功");
	}

	/**
	 * @description 关联PCC模板
	 * @param inInfo
	 * @return
	 */
	public EiInfo updateTemplateRelation(EiInfo inInfo){
		try {
			//获取PCC模板id和信息发布模板id
			String pccId = (String) inInfo.get("pccId");
			String pccName = (String) inInfo.get("pccName");
			String yjId = (String) inInfo.get("yjId");
			if (StringUtils.isEmpty(pccId) || StringUtils.isEmpty(yjId) || StringUtils.isEmpty(pccName)) {
				throw new PlatException("信息发布模板主键丢失或PCC模板主键丢失，请重新刷新页面");
			}
			Map map = new HashMap();
			map.put("fdUuids", yjId);
			map.put("pccId", pccId);
			map.put("pccName", pccName);
			dao.update("XFXG01.update", map);
		}catch (Exception e){
			return CommonUtils.setMessage(inInfo,EiConstant.STATUS_FAILURE,"关联模板失败：" + e.getMessage());
		}
		return CommonUtils.setMessage(inInfo,EiConstant.STATUS_SUCCESS,"关联模板成功");
	}

	/**
	 * 获取专业群组数据
	 * @param outInfo
	 */
	public void queryMajor(EiInfo outInfo) {
		EiInfo inInfo = new EiInfo();
		inInfo.set(EiConstant.serviceId, "S_XF_XG_11");
		EiInfo eiInfo = XServiceManager.call(inInfo);
		if (eiInfo.getStatus() < 0) {
			throw new PlatException("专业数据获取失败");
		}
		outInfo.addRows("majorResult", (List<Map<String,String>>)eiInfo.get("data"));
	}

	/**
	 * @description 信息服务 =====>>>>> 智能应急调度系统
	 *              信息服务系统将模板类型和全部模板数据推送给智能应急调度系统
	 */
	public void pushDataMethod(EiInfo inInfo) {
		/*=====================================*/
		//将模板类型信息发送给智能应急调度系统
		EiInfo templateTypeInfo = queryTemplateTypeForeign(inInfo);
		Map typeMap = new HashMap();
		typeMap.put("data", templateTypeInfo.get("data"));
		EiInfo outInfo = EiInfoUtils.callParam("S_XF_XG_95", typeMap).build();

		//将全部模板信息发送给智能应急调度系统(只发应急模板的)
		EiInfo templateInfo = queryAllTemplateInfo(inInfo);
		Map allTemplateMap = new HashMap();
		allTemplateMap.put("data", templateInfo.get("data"));
		EiInfo outInfo1 = EiInfoUtils.callParam("S_XF_XG_96", allTemplateMap).build();
	}

	/**
	 * @description 模板分类查询：推送给智能应急调度系统
	 * @param inInfo
	 * @return data-模板分类数据块
	 * @servceId S_XF_XG_95
	 */
	public EiInfo queryTemplateTypeForeign(EiInfo inInfo){
		EiInfo outInfo = new EiInfo();
		try {
			Map hashMap = new HashMap();
			hashMap.put("fdTarget", TemplateTargetConstant.EMERGENCY);
			List list = dao.query("XFXG01.queryTemplateTypeForeign", hashMap);
			outInfo.set("data",list);
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 预案查询
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryScenarios(EiInfo inInfo){
		try {
			EiInfo outInfo = EiInfoUtils.callParam("S_YJYG_03",inInfo).build();
			if (outInfo.getStatus() < 0) {
				throw new PlatException("获取预案数据失败");
			}

			//初始化
			EiBlock block = inInfo.getBlock("result3");
			int offset = 0;
			int limit = 10;
			if (block == null){
				block = new EiBlock("result3");
			} else {
				offset = block.getInt(EiConstant.offsetStr);
				limit = block.getInt(EiConstant.limitStr);
			}
			String type = (String) inInfo.get("type");
			if (StringUtils.isNotEmpty(type) && type.equals("sz")) {
				List szList = (List) outInfo.get("szList");
				addBlockData(inInfo,block,szList,limit,offset);
			} else if (StringUtils.isNotEmpty(type) && type.equals("zx")){
				List zxList = (List) outInfo.get("zxList");
				addBlockData(inInfo,block,zxList,limit,offset);
			}

			//搜索
			String openWinFlag = (String) inInfo.get("openWinFlag");
			String planName = (String) inInfo.get("planName");
			if (StringUtils.isNotEmpty(openWinFlag) && openWinFlag.equals("sz")) {
				if(StringUtils.isEmpty(planName)) {
					List szList = (List) outInfo.get("szList");
					addBlockData(inInfo,block,szList,limit,offset);
				} else {
					List<Map<String,String>> planList =  (List<Map<String,String>>) outInfo.get("szList");
					List<Map> result = planList.stream().filter(plan -> plan.get("planName").contains(planName)).collect(Collectors.toList());
					addBlockData(inInfo,block,result,limit,offset);
				}
			} else if (StringUtils.isNotEmpty(openWinFlag) && openWinFlag.equals("zx")){
				if(StringUtils.isEmpty(planName)) {
					List zxList = (List) outInfo.get("zxList");
					addBlockData(inInfo,block,zxList,limit,offset);
				} else {
					List<Map<String,String>> planList =  (List<Map<String,String>>) outInfo.get("zxList");
					List<Map> result = planList.stream().filter(plan -> plan.get("planName").contains(planName)).collect(Collectors.toList());
					addBlockData(inInfo,block,result,limit,offset);
				}
			}

		} catch (Exception e){
			return CommonUtils.setMessage(inInfo,EiConstant.STATUS_FAILURE,"数据查询失败：" + e.getMessage());
		}
		return CommonUtils.setMessage(inInfo,EiConstant.STATUS_SUCCESS,"数据查询成功");
	}

	/**
	 * 给block添加数据以及分页查询
	 * @param inInfo
	 * @param block
	 * @param list
	 * @param limit
	 * @param offset
	 */
	public void addBlockData(EiInfo inInfo, EiBlock block, List list, int limit, int offset) {
		block.addRows(gridPage(list,limit,offset));
		block.set(EiConstant.countStr, list.size());
		block.set(EiConstant.isCountFlag, "true");
		inInfo.addBlock(block);
	}

	/**
	 * 序号生成
	 * @param list
	 * @param limit
	 * @param offset
	 * @return
	 */
	public List gridPage(List<Map> list, int limit, int offset) {
		int count = list.size();//总条数
		int toIndex = 0;
		int lastIndex = offset + limit;
		toIndex = lastIndex >= count ? count : offset + limit;
		//重新生成序号,并赋值给属性,前端展示该属性
		for (int i = 0; i < list.size(); i++) {
			list.get(i).put("num", (i+1));
		}
		return list.subList(offset, toIndex);
	}

	/**
	 * 关联预案
	 * @param inInfo
	 * @return
	 */
	public EiInfo updateScenarios(EiInfo inInfo){
		try {
			//获取PCC模板id和信息发布模板id
			String openWinFlag = (String) inInfo.get("openWinFlag");
			String planIdList = (String) inInfo.get("planIdList");
			String planNameList = (String) inInfo.get("planNameList");
			String yjId = (String) inInfo.get("yjId");
			if (StringUtils.isEmpty(yjId) || StringUtils.isEmpty(planIdList) || StringUtils.isEmpty(planNameList)) {
				throw new PlatException("信息发布模板主键丢失或预案主键丢失，请重新刷新页面");
			}
			if (StringUtils.isNotEmpty(openWinFlag) && openWinFlag.equals("sz")) {
				Map map = new HashMap();
				map.put("fdUuids", yjId);
				map.put("digitPlanId", planIdList);
				map.put("digitPlanName", planNameList);
				dao.update("XFXG01.update", map);
			} else if (StringUtils.isNotEmpty(openWinFlag) && openWinFlag.equals("zx")) {
				Map map = new HashMap();
				map.put("fdUuids", yjId);
				map.put("specialPlanId", planIdList);
				map.put("specialPlanName", planNameList);
				dao.update("XFXG01.update", map);
			}
		}catch (Exception e){
			return CommonUtils.setMessage(inInfo,EiConstant.STATUS_FAILURE,"关联预案失败：" + e.getMessage());
		}
		return CommonUtils.setMessage(inInfo,EiConstant.STATUS_SUCCESS,"关联预案成功");
	}

	/**
	 * @description 模板分类查询
	 * @param inInfo fdTarget-发布目标(pcc,钉钉,应急)
	 * @return data-模板分类数据块
	 * @servceId S_XF_XG_01
	 */
	public EiInfo queryTemplateType(EiInfo inInfo){
		EiInfo outInfo = new EiInfo();
		try {
			String fdTarget = (String) inInfo.get("fdTarget");
			Map hashMap = new HashMap();
			if (StringUtils.isNotEmpty(fdTarget)) {
				hashMap.put("fdTarget", fdTarget);
			}
			List list = dao.query("XFXG01.queryTemplateType", hashMap);
			outInfo.set("data",list);
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 根据根据模板分类查询事件名称（事件情景）
	 * @param inInfo fdType-模板分类 fdTarget-发布目标(pcc,钉钉,应急)
	 * @return data-类别数据块
	 * @servceId S_XF_XG_02
	 */
	public EiInfo queryTemplateScene(EiInfo inInfo){
		EiInfo outInfo = new EiInfo();
		try {
			Map hashMap = new HashMap();
			String fdType = (String) inInfo.get("fdType");
			String fdTarget = (String) inInfo.get("fdTarget");
			if (StringUtils.isNotEmpty(fdType)) {
				//获取事件情景，查询结果为list
				hashMap.put("fdType", fdType);
			}
			if (StringUtils.isNotEmpty(fdTarget)) {
				//发布目标
				hashMap.put("fdTarget", fdTarget);
			}
			List list = dao.query("XFXG01.queryTemplateName", hashMap);
			outInfo.set("data",list);
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 根据模板分类和事件情景查询发布阶段和文本内容
	 * @param inInfo fdType-类别 fdScene-事件情景 fdClass-发布阶段
	 *               fdTarget-发布目标(pcc,钉钉,应急)
	 * @return data-模板数据
	 * @servceId S_XF_XG_03
	 */
	public EiInfo queryTemplateContentAndPhase(EiInfo inInfo){
		EiInfo outInfo = new EiInfo();
		try {
			String fdScene = (String) inInfo.get("fdScene");
			String fdType = (String) inInfo.get("fdType");
			String fdClass = (String) inInfo.get("fdClass");
			String fdTarget = (String) inInfo.get("fdTarget");
			Map hashMap = new HashMap();
			if (StringUtils.isNotEmpty(fdScene)) {
				hashMap.put("fdScene", inInfo.get("fdScene"));
			}
			if (StringUtils.isNotEmpty(fdType)) {
				hashMap.put("fdType", inInfo.get("fdType"));
			}
			if (StringUtils.isNotEmpty(fdClass)) {
				hashMap.put("fdClass", inInfo.get("fdClass"));
			}
			if (StringUtils.isNotEmpty(fdTarget)) {
				//发布目标
				hashMap.put("fdTarget", fdTarget);
			}
			//根据事件情景和类别获取信息内容
			List<Map<String, String>> list = dao.query("XFXG01.query", hashMap);
			//定制排序
			Comparator<Map<String, String>> mapComparator = Comparator.comparing(m -> m.get("fdClass"));
			List<Map<String, String>> data = list.stream()
					.sorted(mapComparator)
					.collect(Collectors.toList());
			outInfo.set("data", data);
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 查询应急模板数据
	 * @param inInfo publishingTarget-发布目标（230006-PCC模板,230008-应急模板）
	 * @return data-应急模板数据集
	 * @servceId S_XF_XG_04
	 */
	public EiInfo queryTemplateInfo(EiInfo inInfo){
		EiInfo outInfo = new EiInfo();
		String fdTarget = (String) inInfo.get("publishingTarget");
		List<Map<String,String>> cmpTemplateList = new ArrayList<>();
		try {
			Map map = new HashMap();
			//判断发布目标是什么模板
			if (StringUtils.isNotEmpty(fdTarget)) {
				map.put("fdTarget", fdTarget);
			}
			cmpTemplateList= dao.query("XFXG01.queryForeignCmpTemplate", map);
			outInfo.set("data", cmpTemplateList);
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 查询全部应急模板数据
	 * @param inInfo
	 * @return data-全部模板数据集
	 * @servceId S_XF_XG_10
	 */
	public EiInfo queryAllTemplateInfo(EiInfo inInfo){
		EiInfo outInfo = new EiInfo();
		//只获取类型和类型id
		List<Map<String,String>> typeAndIdList = new ArrayList<>();
		//最后返回的对象
		List<JSONObject> templateObjectList = new ArrayList<>();
		try {
			Map map = new HashMap();
			//获取专业小代码
//			map.put("codeType","mss.major");
//			List<Map<String,String>> majorList = dao.query("XFXG01.queryCodes", map);
			//获取专业小代码
			EiInfo inInfo1 = new EiInfo();
			inInfo1.set(EiConstant.serviceId, "S_XF_XG_11");
			EiInfo eiInfo = XServiceManager.call(inInfo1);
			if (eiInfo.getStatus() < 0) {
				throw new PlatException("专业数据获取失败");
			}
			List<Map<String,String>> majorList = (List<Map<String, String>>) eiInfo.get("data");
			//查询应急模板信息
			Map map1 = new HashMap();
			map1.put("fdTarget",TemplateTargetConstant.EMERGENCY);
			List<Map<String,String>> templateList = dao.query("XFXG01.queryAllTemplateInfo", map1);
			//判断是否存在数据
			if (templateList.size() > 0) {
				//使用流遍历并将专业和id放进majorAndIdList集合中
				templateList.stream().forEach(items -> {
					Map hashMap = new HashMap();
					hashMap.put("template_class_id", items.get("template_class_id"));
					hashMap.put("template_class_name", items.get("template_class_name"));
					typeAndIdList.add(hashMap);
				});
			}
			//过滤重复类型和类型id
			List<Map<String, String>> distinctTypeList = typeAndIdList.stream().distinct().collect(Collectors.toList());
			if (distinctTypeList.size() > 0) {
				distinctTypeList.stream().forEach(items -> {
					//获取模板的类别id和name
					String id = items.get("template_class_id");
					String name = items.get("template_class_name");
					//创建对象
					JSONObject jsonObject = new JSONObject();
					//将类型和id设置到对象中
					jsonObject.put("template_class_id", id);
					jsonObject.put("template_class_name", name);
					List<Map<String,String>> list = new ArrayList<>();
					//遍历全部模板数据
					for (int i = 0; i < templateList.size(); i++) {
						//获取模板类型id
						String templateClassId = templateList.get(i).get("template_class_id");
						//比较模板类型编号，根据模板类型编号来确认数据放在那个类型下
						if (id.equals(templateClassId)) {
							Map hashMap = new HashMap();
							hashMap.put("uuids",templateList.get(i).get("uuids"));
							hashMap.put("template_name",templateList.get(i).get("template_name"));
							hashMap.put("release_phase",templateList.get(i).get("release_phase"));
							hashMap.put("template_content",templateList.get(i).get("template_content"));
							hashMap.put("professional_id",templateList.get(i).get("professional_id"));
							//特殊处理，如果professional_id有,分割符，先拆分，后进行匹配，再转字符串
							String professionalId = templateList.get(i).get("professional_id");
							if (professionalId.indexOf(",") != -1) {
								//根据符号拆分，然后加入到集合中
								String[] professionalIdArray = professionalId.split(",");
								//判断数组里的id在major集合中是否存在，存在就从major集合拿名称并拼接
								StringBuilder professional = new StringBuilder();
								//循环专业编号数组
								for (int j = 0; j < professionalIdArray.length; j++) {

									//遍历专业小代码集合
									for (int k = 0; k < majorList.size(); k++) {
										//判断专业编号数组的数据在集合中是否存在，存在就从集合获取中文名并拼接
										if (professionalIdArray[j].equals(majorList.get(k).get("profession_id"))) {
											professional.append(majorList.get(k).get("profession"));
											professional.append(",");
										}
									}

								}
								//去掉最后的,号
								professional.deleteCharAt(professional.lastIndexOf(","));
								hashMap.put("professional", professional.toString());
							}
							list.add(hashMap);
							//将集合设置到对象中
							jsonObject.put("template",list);
						}
					}
					//将专业对象数据设置到对象数组中
					templateObjectList.add(jsonObject);
				});
			}
			outInfo.set("data", templateObjectList);
		}catch (Exception e){
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("数据查询失败：" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("数据查询成功！");
		return outInfo;
	}

	/**
	 * @description 提供给PCC发布模板回填,通过模板类型、名称和发布阶段查询
	 * @param inInfo
	 * @return
	 * @servceId S_XF_XG_15
	 */
	public EiInfo getPCCTemplate(EiInfo inInfo){
		try {
			List data = dao.query("XFXG01.getPCCTemplate", inInfo.getAttr());
			inInfo.set("data", data);
		}catch (Exception e){
			return CommonUtils.setMessage(inInfo,EiConstant.STATUS_FAILURE,"查询失败"+e.getMessage());
		}
		return CommonUtils.setMessage(inInfo,EiConstant.STATUS_SUCCESS,"数据查询成功！");
	}

}
