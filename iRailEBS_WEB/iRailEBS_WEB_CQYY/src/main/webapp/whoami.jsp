<%@page contentType="text/html;charset=UTF-8" %>
<%
    String ip = request.getLocalAddr();
    StringBuilder showIpBuilder = new StringBuilder();
    if (ip.contains(":")) {
        String[] ipSplit = ip.split(":");
        for (int i = 0; i < ipSplit.length; i++) {
            if (i == (ipSplit.length - 1)) {
                showIpBuilder.append(ipSplit[i]);
            } else {
                showIpBuilder.append("XX:");
            }
        }
    } else {
        String[] ipSplit = ip.split("\\.");
        for (int i = 0; i < ipSplit.length; i++) {
            if (i == (ipSplit.length - 1)) {
                showIpBuilder.append(ipSplit[i]);
            } else {
                showIpBuilder.append("XX.");
            }
        }
    }
    String showIp = showIpBuilder.toString();
%>
Server IP:<%=showIp%> PORT:<%=request.getLocalPort()%>
