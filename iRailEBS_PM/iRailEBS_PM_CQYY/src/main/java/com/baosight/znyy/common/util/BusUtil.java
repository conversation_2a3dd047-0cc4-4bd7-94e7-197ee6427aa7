package com.baosight.znyy.common.util;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;

public class BusUtil {

    private BusUtil() {
    }


    /**
     * 通用数据解析方法，使用泛型处理不同类型的返回结果
     *
     * @param inInfo 输入信息对象
     * @param parser 解析函数，接收EiInfo并返回T类型结果
     * @param <T>    返回数据类型
     * @return 解析后的数据
     * @throws PlatException 当解析失败或数据为空时抛出异常
     */
    private static <T> T parseData(EiInfo inInfo, Function<EiInfo, T> parser, Predicate<T> isEmpty) {
        // 尝试直接解析
        T result = parser.apply(inInfo);
        if (!isEmpty.test(result)) {
            return result;
        }

        // 尝试从messageBody解析
        String messageBody = inInfo.getString("messageBody");
        if (messageBody != null && !messageBody.trim().isEmpty()) {
            try {
                EiInfo messageInfo = EiInfo.parseJSONString(messageBody);
                result = parser.apply(messageInfo);
                if (!isEmpty.test(result)) {
                    return result;
                }
            } catch (Exception e) {
                // 解析JSON失败，继续抛出最终异常
                throw new PlatException("解析messageBody为EiInfo失败", e);
            }
        }
        // 如果所有解析方式都失败，抛出异常
        throw new PlatException("解析消费数据失败，数据为空！");
    }

    /**
     * 解析接收数据为List<Map>
     *
     * @param inInfo 输入信息对象
     * @return 解析后的List<Map>数据
     * @throws PlatException 当解析失败或数据为空时抛出异常
     */
    public static List<Map<String, Object>> parse(EiInfo inInfo) {
        return parseData(
                inInfo,
                ConvertUtil::parseAttr,
                list -> list == null || list.isEmpty()
        );
    }

    /**
     * 解析接收数据为Map
     *
     * @param inInfo 输入信息对象
     * @return 解析后的Map数据
     * @throws PlatException 当解析失败或数据为空时抛出异常
     */
    public static Map<String, Object> parse2Map(EiInfo inInfo) {
        return parseData(
                inInfo,
                ConvertUtil::parseAttr2Map,
                map -> map == null || map.isEmpty()
        );
    }

}
