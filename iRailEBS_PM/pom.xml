<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.iplat4j</groupId>
        <artifactId>iplat4j-boot-starter</artifactId>
        <version>7.1.0</version>
        <relativePath/>
    </parent>


    <groupId>com.baosight.irailebs</groupId>
    <artifactId>pm-cqyy-parent</artifactId>
    <version>${cqyy.version}</version>
    <packaging>pom</packaging>
    <description>重庆智能运营</description>

    <properties>
        <cqyy.version>1.0.0-7.1.0-SNAPSHOT</cqyy.version>
        <irailmms.version>1.6.0-7.1.0-SNAPSHOT</irailmms.version>
        <lowcode.version>7.2.0</lowcode.version>
    </properties>

    <modules>
        <module>iRailEBS_PM_CQYY</module>
    </modules>

    <dependencies>

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-data-redis</artifactId>-->
<!--        </dependency>-->

        <!-- hutool插件-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.26</version>
        </dependency>
        <!-- lombok插件-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
        </dependency>
        <!-- 流程管理 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-bpm</artifactId>
            <version>7.1.0</version>
        </dependency>
        <!-- 任务管理 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-job</artifactId>
            <version>7.1.0</version>
        </dependency>
        <!-- 组织机构、岗位、分级授权等 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>org-all-plugin</artifactId>
            <version>7.1.0</version>
        </dependency>
        <!--redis适配插件-->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>redis-plugin</artifactId>
            <version>7.1.0</version>
        </dependency>


        <dependency>
            <groupId>com.baosight.irailmms</groupId>
            <artifactId>irailmms-rt-mq</artifactId>
            <version>${irailmms.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baosight.irailmms</groupId>
            <artifactId>irailmms-rt-ext</artifactId>
            <version>${irailmms.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baosight.irailmms</groupId>
            <artifactId>irailmms-rt-ff</artifactId>
            <version>${irailmms.version}</version>
        </dependency>
        <!--设计器相关公共方法-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-management</artifactId>
            <version>${lowcode.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-bpm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--表单设计器相关的前台和后台-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-form</artifactId>
            <version>${lowcode.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-bpm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>code-display</artifactId>
            <version>${lowcode.version}</version>
        </dependency>
        <!--流程设计器相关的前台和后台-->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>lowcode-workflow</artifactId>
            <version>${lowcode.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>delivery-dashboard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-job</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.iplat4j</groupId>
                    <artifactId>xservices-message</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-standard</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-authority</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.baosight.xin3plat</groupId>
                    <artifactId>biz-user</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>code-display-workflow</artifactId>
            <version>${lowcode.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>12.2.0.1</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/webapp</directory>
                <!--注意此次必须要放在此目录下才能被访问到-->
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <plugins>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.6.0</version>
                <configuration>
                    <embedBuildProfileDependencies>true</embedBuildProfileDependencies>
                    <updatePomFile>true</updatePomFile>
                    <pomElements>
                        <parent>resolve</parent>
                        <properties>keep</properties>
                        <distributionManagement>remove</distributionManagement>
                        <name>remove</name>
                        <description>remove</description>
                        <scm>remove</scm>
                        <url>remove</url>
                        <developers>remove</developers>

                    </pomElements>
                    <flattenMode>bom</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>

                    <archive>
                        <addMavenDescriptor>true</addMavenDescriptor>

                        <manifestEntries>
                            <build_number>${env.BUILD_NUMBER}</build_number>
                            <build-node>${env.NODE_NAME}</build-node>
                            <build-svnrev>${env.SVN_REVISION}</build-svnrev>
                            <build-svnurl>${env.SVN_URL}</build-svnurl>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </repository>
        <repository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </repository>

        <repository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </repository>
        <repository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </repository>
        <repository>
            <id>guidao-maven</id>
            <name>guidao maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </pluginRepository>
        <pluginRepository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </pluginRepository>
        <pluginRepository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>guidao-maven</id>
            <name>guidao maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>



    <distributionManagement>

        <snapshotRepository>
            <id>rt-snapshots</id>
            <name>rt-snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>