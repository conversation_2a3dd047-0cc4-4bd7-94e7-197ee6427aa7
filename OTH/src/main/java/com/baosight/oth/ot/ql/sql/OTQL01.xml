<?xml version="1.0" encoding="UTF-8"?>
<!DOCTY<PERSON><PERSON> sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
	<!--      table information
		Generate time : 2023-03-29 10:44:57
   		Version :  1.0
		tableName :wlplat.tmmmq01 
		 REC_ID  VARCHAR   NOT NULL   primarykey, 
		 QUOTA_ID  VARCHAR   NOT NULL   primarykey, 
		 QUOTA_NAME  VARCHAR, 
		 QUOTA_TYPE  VARCHAR, 
		 QUOTA_VERSION  VARCHAR, 
		 APPLY_PERSION_ID  VARCHAR, 
		 APPLY_PERSION_NAME  VARCHAR, 
		 APPLY_DEPT_ID  VARCHAR, 
		 APPLY_DEPT_NAME  VARCHAR, 
		 PLAN_YEAR  DECIMAL, 
		 EFFECT_DATE_FROM  VARCHAR, 
		 EFFECT_DATE_TO  VARCHAR, 
		 LINE_ID  VARCHAR, 
		 LINE_STAGE_ID  VARCHAR, 
		 MAT_CLASS_CODE  VARCHAR, 
		 MAT_CLASS_NAME  VARCHAR, 
		 USAGE_CODE  VARCHAR, 
		 USAGE_NAME  VARCHAR, 
		 VEHICLE_NO  VARCHAR, 
		 WORK_FLOW_INST_ID  VARCHAR, 
		 WORK_FLOW_INST_STATUS  VARCHAR, 
		 MANAGEMENT_CODE  VARCHAR, 
		 MANAGEMENT_NAME  VARCHAR, 
		 MANAGEMENT_PERSON_CODE  VARCHAR, 
		 MANAGEMENT_PERSON_NAME  VARCHAR, 
		 REC_STATUS  VARCHAR, 
		 REMARK  VARCHAR, 
		 REJECT_REASON  VARCHAR, 
		 ORG_UNIT_CODE  VARCHAR, 
		 COMPANY_CODE  VARCHAR, 
		 REC_CREATOR  VARCHAR, 
		 REC_CREATE_TIME  VARCHAR, 
		 REC_REVISOR  VARCHAR, 
		 REC_REVISE_TIME  VARCHAR, 
		 REC_DELETOR  VARCHAR, 
		 REC_DELETE_TIME  VARCHAR, 
		 DELETE_FLAG  VARCHAR, 
		 ARCHIVE_FLAG  VARCHAR, 
		 EXT1  VARCHAR, 
		 EXT2  VARCHAR, 
		 EXT3  VARCHAR, 
		 EXT4  VARCHAR, 
		 EXT5  VARCHAR
	-->
<sqlMap namespace="OTQL01">

    <select id="queryUrl" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
        SELECT warnningurl as "warnningUrl",
        bridgeid as "bridgeId"
        FROM ${othProjectSchema}.bridge_info t
        WHERE t.status='1'
    </select>

    <select id="queryQL" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
        SELECT
        t.bridgeid as "bridgeId",
        t.MeaPtNum as "MeaPtNum",
        t.tagObj as "tagObj",
        d.ITEM_CODE as "itemCode",
        d.ITEM_CNAME as "itemCname",
        d.ITEM_ENAME as "itemEname"
        FROM ${othProjectSchema}.meaptnum_info t
        left join ${platSchema}.tedcm01 d on d.CODESET_CODE='ql.gzLevel'
        WHERE t.status='1' and t.bridgeid=#bridgeId#
    </select>

    <select id="queryMsg" resultClass="java.util.HashMap" parameterClass="java.util.HashMap">
        SELECT
        t.bridgeid as "bridgeId",
        t.MeaPtNum as "MeaPtNum",
        t.tagObj as "tagObj",
        b.bridgename as "bridgename",
        b.lineid as "lineid",
        d.ITEM_CNAME as "gzType",
        m.ITEM_CNAME as "meaptType"
        FROM ${othProjectSchema}.meaptnum_info t
        left join ${othProjectSchema}.bridge_info b on b.bridgeid=t.bridgeid
        left join ${platSchema}.tedcm01 d on d.CODESET_CODE='ql.gzType' and d.item_code=#value#
        left join ${platSchema}.tedcm01 m on m.CODESET_CODE='ql.meaptType' and t.meapttype=m.item_code
        WHERE t.status='1' and t.tagobj=#tagobj#
    </select>
</sqlMap>