package com.baosight.oth.ot.jc.domain;

import com.baosight.iplat4j.core.data.DaoEPBase;
import com.baosight.iplat4j.core.ei.EiColumn;

public class HistoryWarn extends DaoEPBase {
    private String occurtime;
    private String status;
    private String desc;
    private String level;
    private String confirmTime;
    private String recoveryTime;
    private String confirmPerson;

    /**
     * initialize the metadata
     */
    public void initMetaData() {
        EiColumn eiColumn;

        eiColumn = new EiColumn("occurtime");
        eiColumn.setPrimaryKey(true);
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("产生时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("status");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("确认状态");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("desc");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("报警点描述");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("level");
        eiColumn.setFieldLength(125);
        eiColumn.setDescName("报警等级");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("confirmTime");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("确认时间");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("confirmPerson");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("确认人");
        eiMetadata.addMeta(eiColumn);

        eiColumn = new EiColumn("recoveryTime");
        eiColumn.setFieldLength(255);
        eiColumn.setDescName("恢复时间");
        eiMetadata.addMeta(eiColumn);
    }
    /**
     * the constructor
     */
    public HistoryWarn() {
        initMetaData();
    }
}
