package com.baosight.oth.ot;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JSONSerializer;
import org.apache.commons.beanutils.BeanUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

public class JSONUtil {

	public static <T> List<T> getJavaCollection(T clazz, String jsons) {
		List<T> objs = null;
		JSONArray jsonArray = (JSONArray) JSONSerializer.toJSON(jsons);
		if (jsonArray != null) {
			objs = new ArrayList<T>();
			List list = (List) JSONSerializer.toJava(jsonArray);
			for (Object o : list) {
				JSONObject jsonObject = JSONObject.fromObject(o);
				T obj = (T) JSONObject.toBean(jsonObject, clazz.getClass());
				objs.add(obj);
			}
		}
		return objs;
	}

	public static <T> T toObject(String json, T clazz) {
		JSONObject obj = JSONObject.fromObject(json);
		return (T) JSONObject.toBean(obj, clazz.getClass());
	}
	
	public static <T> T toObject(String json, Class clazz) {
		JSONObject obj = JSONObject.fromObject(json);
		return (T) JSONObject.toBean(obj, clazz);
	}

	public static <T> void transMapBean(Map<String, Object> map, Object obj)
			throws IllegalAccessException, InvocationTargetException {
		if (map == null || obj == null) {
			return;
		}
		BeanUtils.populate(obj, map);
	}

	public static Map<String, Object> parseJSON2Map(String jsonStr) {
		Map<String, Object> map = new HashMap<String, Object>();
		// 最外层解析
		JSONObject json = JSONObject.fromObject(jsonStr);
		for (Object k : json.keySet()) {
			Object v = json.get(k);
			// 如果内层还是数组的话，继续解析
			if (v instanceof JSONArray) {
				List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
				Iterator<JSONObject> it = ((JSONArray) v).iterator();
				while (it.hasNext()) {
					JSONObject json2 = it.next();
					list.add(parseJSON2Map(json2.toString()));
				}
				map.put(k.toString(), list);
			} else {
				map.put(k.toString(), v);
			}
		}
		return map;
	}
	/**
	 * 解析单层map
	 * @param jsonStr
	 * @return
	 */
	public static Map<String, Object> parseJSON2Map1(String jsonStr) {
		Map<String, Object> map = new HashMap<String, Object>();
		// 最外层解析
		JSONObject json = JSONObject.fromObject(jsonStr);
		for (Object k : json.keySet()) {
			Object v = json.get(k);
			// 如果内层还是数组的话，继续解析
			map.put(k.toString(), v);
		}
		return map;
	}

	public static List<Map<String, Object>> parseJSON2List(String jsonStr) {
		JSONArray jsonArr = JSONArray.fromObject(jsonStr);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Iterator<JSONObject> it = jsonArr.iterator();
		while (it.hasNext()) {
			JSONObject json2 = it.next();
			list.add(parseJSON2Map(json2.toString()));
		}
		return list;
	}

	public static String toJSONArray(List list) {
		JSONArray array = JSONArray.fromObject(list);
		return array.toString();
	}

	public static String toJSON(Object obj) {
		JSONObject object = JSONObject.fromObject(obj);
		return object.toString();
	}
	
	public static List<Map<String, Object>> parseJSON2ListCase(String jsonStr) {
		JSONArray jsonArr = JSONArray.fromObject(jsonStr);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Iterator<JSONObject> it = jsonArr.iterator();
		while (it.hasNext()) {
			JSONObject json2 = it.next();
			list.add(parseJSON2MapCase(json2.toString()));
		}
		return list;
	}
	
	public static Map<String, Object> parseJSON2MapCase(String jsonStr) {
		Map<String, Object> map = new HashMap<String, Object>();
		// 最外层解析
		JSONObject json = JSONObject.fromObject(jsonStr);
		for (Object k : json.keySet()) {
			Object v = json.get(k);
			// 如果内层还是数组的话，继续解析
			if (v instanceof JSONArray) {
				List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
				Iterator<JSONObject> it = ((JSONArray) v).iterator();
				while (it.hasNext()) {
					JSONObject json2 = it.next();
					list.add(parseJSON2Map(json2.toString()));
				}
				map.put(convertString(k.toString()), list);
			} else {
				map.put(convertString(k.toString()), v);
			}
		}
		return map;
	}
	
	private static String convertString(String s) {
		String ret = "";

		Boolean isUpper = false;
		for (int i = 0; i < s.length(); ++i) {
			if (s.charAt(i) == '_') {
				isUpper = true;
			} else {
				if (isUpper) {
					ret += s.charAt(i);
					isUpper = false;
				} else {
					ret = ret + Character.toLowerCase(s.charAt(i));
				}
			}

		}
		return ret;
	}
	
	
}
