
$(function () {

    $(window).on("load", function () {
        requestDataAndLoadChart("0000000000");
        let eiInfo = new EiInfo();
        eiInfo.set("referenceLine","0000000000");
        EiCommunicator.send("TFTJ02","getPeakDate", eiInfo, {
            onSuccess: function(response){
                console.log("线网：");
                console.log(response);
                $("#peakDate")[0].innerHTML = "线网历史峰值日：" + response.extAttr.peakDate;
            }
        });
    });

    IPLATUI.EFSelect = {
        "referenceLine": {
            change: function (e) {
                let referenceLine = IPLAT.EFSelect.value($("#referenceLine"));
                let text = IPLAT.EFSelect.text($("#referenceLine"));

                requestDataAndLoadChart(referenceLine);

                let eiInfo = new EiInfo();
                eiInfo.set("referenceLine",referenceLine);
                EiCommunicator.send("TFTJ02","getPeakDate", eiInfo, {
                    onSuccess: function(response){
                        $("#peakDate")[0].innerHTML = text + "历史峰值日：" + response.extAttr.peakDate;
                    }
                });
            }
        },
    };


    /**
     * 环形图各线路颜色
     * @type {[{x: number, globalCheckbox: boolean, y: number, y2: number, x2: number, global: boolean, colorStops: [{offset: number, color: string},{offset: number, color: string}], type: string},{x: number, globalCheckbox: boolean, y: number, y2: number, x2: number, global: boolean, colorStops: [{offset: number, color: string},{offset: number, color: string}], type: string},{x: number, globalCheckbox: boolean, y: number, y2: number, x2: number, global: boolean, colorStops: [{offset: number, color: string},{offset: number, color: string}], type: string},{x: number, globalCheckbox: boolean, y: number, y2: number, x2: number, global: boolean, colorStops: [{offset: number, color: string},{offset: number, color: string}], type: string},{x: number, globalCheckbox: boolean, y: number, y2: number, x2: number, global: boolean, colorStops: [{offset: number, color: string},{offset: number, color: string}], type: string}]}
     */
    let MaxFlowChartColors = [
        {
            "type": "linear",
            "x": 0,
            "y": 0,
            "x2": 1,
            "y2": 1,
            "colorStops": [
                {
                    "offset": 1,
                    "color": "rgba(59, 162, 52, 1)"
                },
                {
                    "offset": 0,
                    "color": "rgba(59, 162, 52, 0.56)"
                }
            ],
            "global": false,
            "globalCheckbox": true
        },
        {
            "type": "linear",
            "x": 0,
            "y": 0,
            "x2": 1,
            "y2": 1,
            "colorStops": [
                {
                    "offset": 1,
                    "color": "rgba(203, 51, 59, 1)"
                },
                {
                    "offset": 0,
                    "color": "rgba(203, 51, 59, 0.55)"
                }
            ],
            "global": false,
            "globalCheckbox": true
        },
        {
            "type": "linear",
            "x": 0,
            "y": 0,
            "x2": 1,
            "y2": 1,
            "colorStops": [
                {
                    "offset": 1,
                    "color": "rgba(173, 16, 172, 0.61)"
                },
                {
                    "offset": 0,
                    "color": "rgba(173, 16, 172, 1)"
                }
            ],
            "global": false,
            "globalCheckbox": true
        },
        {
            "type": "linear",
            "x": 0,
            "y": 0,
            "x2": 1,
            "y2": 1,
            "colorStops": [
                {
                    "offset": 1,
                    "color": "rgba(206, 220, 0, 0.81)"
                },
                {
                    "offset": 0,
                    "color": "rgba(206, 220, 0, 1)"
                }
            ],
            "global": false,
            "globalCheckbox": true
        },
        {
            "type": "linear",
            "x": 0,
            "y": 0,
            "x2": 1,
            "y2": 1,
            "colorStops": [
                {
                    "offset": 1,
                    "color": "rgba(58, 93, 174, 0.56)"
                },
                {
                    "offset": 0,
                    "color": "rgba(58, 93, 174, 1)"
                }
            ],
            "global": false,
            "globalCheckbox": true
        }

    ]

    /**
     * 最大客流环形图
     */
    function loadMaxFlowChart(names, data){
        let chartDom = document.getElementById("maxFlowChart");
        let chart = echarts.init(chartDom);

        let chartData = [];
        for (let i=0; i<data.length; i++){
            let oneData =  {
                "value": data[i],
                "name": names[i],
                "itemStyle": {
                    "color": MaxFlowChartColors[i]
                },
            };
            chartData.push(oneData);
        }
        let option = {
            "toolbox": {
                "show": true,
                "top": 0,
                "right": 0,
                "itemSize": 16,
                "iconStyle": {
                    "color": "#2C5399",
                    "borderWidth": "0"
                },
                "showTitle": false,
                "feature": {
                    "myFull": {
                        "show": false,
                        "title": "放大",
                        "icon": "path://M426.496 225.450667l-312.951467-83.848534 83.857067 312.942934 81.262933-81.262934 160.256 160.264534 60.3392-60.3392-160.256-160.264534zM438.920533 568.2688l-160.256 160.256-81.262933-81.262933-83.857067 312.951466 312.951467-83.857066-87.492267-87.492267 160.256-160.256zM600.5504 533.546667l160.264533-160.264534 81.262934 81.262934 83.848533-312.942934-312.942933 83.848534 87.492266 87.492266-160.264533 160.264534zM760.814933 728.5248l-160.264533-160.256-60.3392 60.3392 160.264533 160.256-87.492266 87.492267 312.942933 83.857066-83.848533-312.951466z",
                        "myStyle": {
                            "width": 960,
                            "height": 540,
                            "background": "rgba(172, 198, 235, 1)"
                        }
                    }
                }
            },
            "tooltip": {
                "show": true,
                "trigger": "item",
                "templateType": "default",
                "formatter":function(param){
                    return `${param.name} ${param.value} (${param.percent.toFixed(2)}%)`
                },
                "left": 0,
                "top": 0,
                "backgroundColor": "rgba(50, 50, 50, 0.7)",
                "borderColor": "#333333",
                "borderWidth": 0,
                "textStyle": {
                    "color": "white" //设置文字颜色
                },
            },
            "legend": {
                "pageTurning": false,
                "type": "plain",
                "customlegend": "{b|{b}}",
                "Legendformat": "",
                "textStyle": {
                    "fontFamily": "Microsoft YaHei",
                    "lineHeight": 15,
                    "color": "auto",
                    "rich": {
                        "b": {
                            "fontSize": 12,
                            "padding": [
                                0,
                                0,
                                0,
                                1
                            ]
                        },
                        "c": {
                            "fontSize": 12,
                            "padding": [
                                0,
                                0,
                                0,
                                10
                            ]
                        },
                        "d": {
                            "fontSize": 12,
                            "padding": [
                                0,
                                0,
                                0,
                                10
                            ]
                        }
                    },
                    "formatterValue": "0"
                },
                "show": false,
                "orient": "vertical",
                "itemWidth": 10,
                "itemHeight": 10,
                "itemGap": 10,
                "left": "left",
                "top": "top",
                "align": "left",
                "icon": "circle",
                "data": [
                    "1号线",
                    "2号线",
                    "3号线",
                    "4号线",
                    "5号线"
                ]
            },
            "title": {
                "text": "",
                "left": "50%",
                "top": "0%",
                "padding": [
                    24,
                    0
                ],
                "textStyle": {
                    "color": "#333333",
                    "fontSize": 16,
                    "align": "center",
                    "fontFamily": "Microsoft YaHei"
                }
            },
            "series": [
                {
                    "itemStyle": {
                        "borderWidth": 0,
                        "borderColor": "#ffffff",
                        "emphasis": {
                            "shadowBlur": 10,
                            "shadowOffsetX": 0,
                            "shadowColor": "rgba(0, 0, 0, 0.5)"
                        }
                    },
                    "type": "pie",
                    "radius": [
                        "62%",
                        "73%"
                    ],
                    "avoidLabelOverlap": true,
                    "label": {
                        "normal": {
                            "formatter": "{b|{b}}\n{hr|}\n{c|{c}}\n{d|{d}%}",
                            "customFormat": "",
                            "labelForm": "{b|{b}}\n{hr|}\n{c|{c}}\n{d|{d}%}",
                            "unit": "",
                            "unitConversion": false,
                            "conversionType": "flow",
                            "flowFormat": "B",
                            "weightFormat": "mg",
                            "roundNum": 0,
                            "rich": {
                                "c": {
                                    "color": "#ffc72b",
                                    "fontSize": 20,
                                    "padding": [
                                        5,
                                        4
                                    ],
                                    "align": "center",
                                    "fontFamily": "Microsoft YaHei"
                                },
                                "b": {
                                    "color": "#333333",
                                    "align": "center",
                                    "fontSize": 14,
                                    "padding": [
                                        12,
                                        0
                                    ],
                                    "fontFamily": "Microsoft YaHei"
                                },
                                "d": {
                                    "color": "#49dff0",
                                    "fontSize": 16,
                                    "align": "center",
                                    "fontFamily": "Microsoft YaHei",
                                    "padding": [
                                        0,
                                        0
                                    ]
                                },
                                "hr": {
                                    "borderColor": "#0b5263",
                                    "width": 70,
                                    "borderWidth": 1,
                                    "height": 0
                                }
                            },
                            "show": false,
                            "numberFormat": "auto",
                            "autoZeroize": true,
                            "showRoundNum": false,
                            "formatterValue": "2"
                        },
                        "emphasis": {
                            "show": false
                        }
                    },
                    "labelLine": {
                        "normal": {
                            "show": false,
                            "lineStyle": {
                                "color": "#333333"
                            },
                            "length": 30,
                            "length2": 0
                        },
                        "emphasis": {
                            "show": false
                        }
                    },
                    "data": chartData,
                    "roseType": false,
                    "left": 0,
                    "right": 0,
                    "top": 0,
                    "bottom": 0
                }
            ],
            "animation": false,
            "initialAnimation": 3000,
            "pieUpdateAnimation": 1000,
            "carousel": false,
            "carouselTime": 2000,
        }

        chart.setOption(option, true);
    }

    let lineChartColor1 = [
        {
            "x": 0,
            "y": 0,
            "x2": 0,
            "y2": 1,
            "type": "linear",
            "global": false,
            "colorStops": [
                {
                    "offset": 0,
                    "color": "#3BA234"
                },
                {
                    "offset": 1,
                    "color": "rgba(0, 255, 194, 0)"
                }
            ]
        },
        {
            "x": 0,
            "y": 0,
            "x2": 0,
            "y2": 1,
            "type": "linear",
            "global": false,
            "colorStops": [
                {
                    "offset": 0,
                    "color": "#CB333B"
                },
                {
                    "offset": 1,
                    "color": "rgba(255, 0, 77, 0.46)"
                }
            ]
        },
        {
            "x": 0,
            "y": 0,
            "x2": 0,
            "y2": 1,
            "type": "linear",
            "global": false,
            "colorStops": [
                {
                    "offset": 0,
                    "color": "#AD1AAC"
                },
                {
                    "offset": 1,
                    "color": "rgba(189, 0, 255, 0.64)"
                }
            ]
        },
        {
            "x": 0,
            "y": 0,
            "x2": 0,
            "y2": 1,
            "type": "linear",
            "global": false,
            "colorStops": [
                {
                    "offset": 0,
                    "color": "#CEDC00"
                },
                {
                    "offset": 1,
                    "color": "rgba(255, 245, 0, 0.46)"
                }
            ]
        },
        {
            "x": 0,
            "y": 0,
            "x2": 0,
            "y2": 1,
            "type": "linear",
            "global": false,
            "colorStops": [
                {
                    "offset": 0,
                    "color": "#3A5DAE"
                },
                {
                    "offset": 1,
                    "color": "#00B2FF"
                }
            ]
        }];

    let lineChartPictorialBarColors = ["#3BA234", "#CB333B", "#AD1AAC", "#CEDC00", "#3A5DAE"];
    /**
     * 线路客运排名图
     */
    function loadLineChart(names, data){
        let chartDom = document.getElementById("lineChart");
        let chart = echarts.init(chartDom);

        //该图表有三个柱子叠合：大柱子、内柱子、顶部条
        let series1Data = [];
        let series2Data = [];
        let series3Data = [];
        for (let i=0; i<data.length; i++){
            let oneData1 = {
                "value": data[i],
                "symbolPosition": "end",
                "itemStyle": {
                    "color": lineChartColor1[i],
                    "opacity": 0.4
                }
            }
            let oneData2 = {
                "value": data[i],
                "symbolPosition": "end",
                "itemStyle": {
                    "color": lineChartColor1[i],
                    "opacity": 1
                }
            }
            let oneData3 = {
                "value": data[i],
                "symbolPosition": "end",
                "itemStyle": {
                    "color": lineChartPictorialBarColors[i]
                }
            };
            series1Data.push(oneData1)
            series2Data.push(oneData2)
            series3Data.push(oneData3)
        }


        let option = {
            "backgroundColor": "rgba(255,255,255,0.0)",
            "grid": {
                "left": 30,
                "right": 30,
                "top": 40,
                "bottom": 30,
                "containLabel": true
            },
            "xAxis": [
                {
                    "type": "category",
                    "axisLabel": {
                        "interval": 0,
                        "color": "#99DAF5",
                        "fontSize": 16,
                        "padding": [
                            0,
                            0,
                            0,
                            0
                        ]
                    },
                    "axisLine": {
                        "show": true,
                        "lineStyle": {
                            "type": "solid",
                            "color": "#99DAF5",
                            "width": 2
                        }
                    },
                    "axisTick": {
                        "show": false
                    },
                    "data": names
                },
                {
                    "type": "category",
                    "data": names,
                    "axisLabel": {
                        "show": false
                    },
                    "axisLine": {
                        "show": false
                    },
                    "axisTick": {
                        "show": false
                    }
                },
                {
                    "type": "category",
                    "data": names,
                    "axisLabel": {
                        "show": false
                    },
                    "axisLine": {
                        "show": false
                    },
                    "axisTick": {
                        "show": false
                    }
                }
            ],
            "yAxis": [
                {
                    "type": "value",
                    "name": "(人次)",
                    "nameTextStyle": {
                        "color": "#99DAF5",
                        "fontSize": 12,
                        "padding": [
                            0,
                            60,
                            0,
                            0
                        ]
                    },
                    "axisTick": {
                        "show": false
                    },
                    "axisLine": {
                        "show": false
                    },
                    "splitLine": {
                        "show": true,
                        "lineStyle": {
                            "type": "solid",
                            "color": "#99DAF5",
                            "width": 1
                        }
                    },
                    "axisLabel": {
                        "show": true,
                        "fontSize": 16,
                        "color": "#99DAF5"
                    }
                }
            ],
            "series": [
                {
                    "type": "bar",
                    "xAxisIndex": 2,
                    "barWidth": 60,
                    "itemStyle": {
                        "color": {
                            "x": 0,
                            "y": 0,
                            "x2": 0,
                            "y2": 1,
                            "type": "linear",
                            "global": false,
                            "colorStops": [
                                {
                                    "offset": 0,
                                    "color": "rgba(17, 60, 95, 0.61)"
                                },
                                {
                                    "offset": 1,
                                    "color": "#114163"
                                }
                            ]
                        },
                        "borderWidth": 0.5,
                        "borderColor": "rgba(1, 216, 237, 0.42)"
                    },
                    "barMinHeight": 100000000,
                    "data": [
                        0,
                        0,
                        0,
                        0,
                        0
                    ],
                    "z": 2
                },
                {
                    "type": "bar",
                    "xAxisIndex": 1,
                    "barWidth": 45,
                    "itemStyle": {
                        "color": {
                            "x": 0,
                            "y": 0,
                            "x2": 0,
                            "y2": 1,
                            "type": "linear",
                            "global": false,
                            "colorStops": [
                                {
                                    "offset": 0,
                                    "color": "#00A8FF"
                                },
                                {
                                    "offset": 1,
                                    "color": "rgba(255, 122, 255, 1)"
                                }
                            ]
                        }
                    },
                    "data": series1Data,
                    "z": 3
                },
                {
                    "type": "bar",
                    "xAxisIndex": 0,
                    "barWidth": 15,
                    "label": {
                        "show": true,
                        "position": "top",
                        "color": "#ddd"
                    },
                    "data": series2Data,
                    "z": 5
                },
                {
                    "type": "pictorialBar",
                    "xAxisIndex": 0,
                    "symbol": "react",
                    "symbolSize": [
                        54,
                        2
                    ],
                    "symbolOffset": [
                        0,
                        -1
                    ],
                    "data": series3Data,
                    "z": 7
                }
            ]
        };
        chart.setOption(option, true);
    }

    /**
     * 车站Top10客流排名图
     */
    function loadStationChart(names, data){
        let chartDom = document.getElementById("stationChart");
        let chart = echarts.init(chartDom);
        let seriesData = [];
        for (let i=0; i<data.length; i++){
            let oneData =  {
                "value": data[i],
                "itemStyle": {
                    "color": "#1AD6CE"
                }
            };
            seriesData.push(oneData);
        }
        let option = {
            "grid": {
                "width": "830",
                "height": "740",
                "top": "5",
                "left": "105",
                "right": "120"
            },
            "xAxis": {
                "show": false,
                "type": "value"
            },
            "yAxis": [
                {
                    "axisLabel": {
                        "color": "#fff",
                        "fontSize": 16
                    },
                    "axisLine": {
                        "show": false
                    },
                    "axisTick": {
                        "show": false
                    },
                    "inverse": true,
                    "type": "category",
                    "data": names
                },
                {
                    "axisLabel": {
                        "show": false
                    },
                    "axisLine": {
                        "show": false
                    },
                    "axisTick": {
                        "show": false
                    },
                    "inverse": true,
                    "type": "category",
                    "data": names
                }
            ],
            "series": [
                {
                    "type": "bar",
                    "data": seriesData,
                    "yAxisIndex": 0,
                    "label": {
                        "show": true,
                        "position": [
                            835,
                            5
                        ],
                        "fontSize": 20,
                        "color": "white"
                    },
                    "colorBy": "data",
                    "barWidth": 25,
                    "showBackground": true,
                    "backgroundStyle": {
                        "color": "rgba(9, 113, 184, 0.4)"
                    },
                    "z": 7
                },
                {
                    "type": "bar",
                    "silent": true,
                    "data": [
                        1,
                        1,
                        4,
                        5,
                        1,
                        4,
                        1,
                        9,
                        1,
                        9
                    ],
                    "yAxisIndex": 1,
                    "color": "rgba(12, 80, 125, 0.7)",
                    "barWidth": 60,
                    "barMinHeight": 100000000,
                    "z": 0,
                    "barCategoryGap": "50%"
                }
            ]
        };
        chart.setOption(option, true);
    }

    /**
     * 请求数据并加载图表
     * @param referenceLine 线路号
     */
    function requestDataAndLoadChart(referenceLine){
        let eiInfo = new EiInfo();
        eiInfo.set("referenceLine",referenceLine);
        EiCommunicator.send("TFTJ02","queryNetPeakTraffic", eiInfo, {
            onSuccess: function(response){
                let maxFlowValueDom = document.getElementsByClassName("maxFlowValue");
                maxFlowValueDom[0].innerText = response.extAttr.maxFlow;
            }
        });

        EiCommunicator.send("TFTJ02","queryLinePeakTraffic", eiInfo, {
            onSuccess: function(response){
                let attr = response.extAttr
                loadLineChart(attr.names, attr.data);
                loadMaxFlowChart(attr.names, attr.data);
            }
        });

        EiCommunicator.send("TFTJ02","queryStationPeakTraffic", eiInfo, {
            onSuccess: function(response){
                let attr = response.extAttr
                loadStationChart(attr.names, attr.data);
            }
        });

    }
});
