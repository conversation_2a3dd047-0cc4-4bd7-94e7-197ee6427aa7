;(function (define) {

    class TreeView {
        options = {
            elem: "",
            serviceName: "",
            methodName: "",
            filter: "",
            tree: {
                "core": {
                    'data': []
                },
                "types": {
                    "default": {
                        "icon": "fa fa-file-o"
                    }
                },
                "defaults": {
                    "core": {
                        "themes": {
                            "dots": false
                        },
                    }
                },
                "checkbox": {
                    "keep_selected_style": false,//是否默认选中
                    "three_state": false,//父子级别级联选择
                    "tie_selection": false
                },
                "plugins": ["search", "types", "checkbox"]
            }
        };

        get elem() {
            return $(this.options.elem);
        }

        constructor(options) {
            this.render(options);
        }

        render(options) {
            this.init(options);
            this.listen();
        }

        init(options) {
            let that = this;
            this.options = $.extend(this.options, options);
            this.createTree();
            this.options.serviceName && this.options.methodName ? this.ajaxInit() : "";
        }

        createTree() {
            this.elem.data('jstree', false).empty();
        }

        // ajaxInit() {
        //     let ei = this.query(), that = this, datas = [];
        //     IPLAT.sendService(this.options.serviceName, this.options.methodName,
        //         ei, function (response) {
        //             let block = response.getBlock("root"), rows = block.getRows();
        //             rows.forEach((item, index) => {
        //                 let parent = block.getCell(index, "parentEname");
        //                 if (parent === "root") {
        //                     parent = "#";
        //                 }
        //                 datas.push({
        //                     "id": block.getCell(index, "ename"),
        //                     "parent": parent,
        //                     "text": block.getCell(index, "cname"),
        //                     "icon": block.getCell(index, "icon"),
        //                     "state": {
        //                         "opened": Boolean(parseInt(block.getCell(index, "spread"))),
        //                         "disabled": !Boolean(parseInt(block.getCell(index, "leaf")))
        //                     }
        //                 })
        //             });
        //
        //             that.elem.jstree($.extend({}, that.options.tree, {
        //                 'core': {
        //                     'data': datas
        //                 }
        //             }));
        //         });
        // }

        ajaxInit() {
            let ei = this.query(), that = this, datas = [];
            IPLAT.EiCommunicator.send(this.options.serviceName, this.options.methodName, ei, {
                onSuccess: (response) => {
                    if (response.getStatus() === -1) {
                        const msg = response.detailMsg || response.msg;
                        return IPLAT.alert({
                            message: '<b>' + msg + '</b>',
                            title: '提示'
                        });
                    }

                    let block = response.getBlock("root"), rows = block.getRows();
                    rows.forEach((item, index) => {
                        let parent = block.getCell(index, "parentEname");
                        if (parent === "root") {
                            parent = "#";
                        }
                        datas.push({
                            "id": block.getCell(index, "ename"),
                            "parent": parent,
                            "text": block.getCell(index, "cname"),
                            "icon": block.getCell(index, "icon"),
                            "state": {
                                "opened": Boolean(parseInt(block.getCell(index, "spread"))),
                                "disabled": !Boolean(parseInt(block.getCell(index, "leaf")))
                            }
                        })
                    });

                    that.elem.jstree($.extend({}, that.options.tree, {
                        'core': {
                            'data': datas
                        }
                    }));
                }
            });
        }



        query() {
            let eiInfo = new EiInfo();
            eiInfo.set("inqu_status-0-node", "root");
            this.options.query && typeof this.options.query === 'function' && this.options.query(eiInfo);
            return eiInfo;
        }

        listen() {
            let that = this,
                to = false, $filter = $(that.options.filter);
            $filter.keyup(function () {
                if (to) {
                    clearTimeout(to);
                }
                to = setTimeout(function () {
                    let v = $filter.val();
                    that.elem.jstree(true).search(v);
                }, 250);
            });
        }
    }

    define(['jquery'], function ($) {
        return {
            render(options) {
                return new TreeView(options);
            }
        };
    });
}(typeof define === 'function' && define.amd ? define : function (deps, factory) {
    const MOD_NAME = 'treeView';
    if (typeof module !== 'undefined' && module.exports) { //Node
        module.exports = factory(require('jquery'));
    } else if (window.IPLAT && IPLAT.define) {
        IPLAT.define('jquery', function (exports) {
            exports(MOD_NAME, factory(IPLAT.jquery));
        });
    } else {
        window[MOD_NAME] = factory(window['jQuery']);
    }
}));
