/*! layDate v5.3.1 | 日期与时间组件 | MIT Licensed */
 ;!function(e){"use strict";var t=e.document,n={modules:{},status:{},timeout:10,event:{}},a=function(){this.v="2.6.7"},r=e.LAYUI_GLOBAL||{},i=function(){var e=t.currentScript?t.currentScript.src:function(){for(var e,n=t.scripts,a=n.length-1,r=a;r>0;r--)if("interactive"===n[r].readyState){e=n[r].src;break}return e||n[a].src}();return n.dir=r.dir||e.substring(0,e.lastIndexOf("/")+1)}(),o=function(t,n){n=n||"log",e.console&&console[n]&&console[n]("layui error hint: "+t)},l="undefined"!=typeof opera&&"[object Opera]"===opera.toString(),s=n.builtin={lay:"lay",layer:"layer",laydate:"laydate",laypage:"laypage",laytpl:"laytpl",layedit:"layedit",form:"form",upload:"upload",dropdown:"dropdown",transfer:"transfer",tree:"tree",table:"table",element:"element",rate:"rate",colorpicker:"colorpicker",slider:"slider",carousel:"carousel",flow:"flow",util:"util",code:"code",jquery:"jquery",all:"all","layui.all":"layui.all"};a.prototype.cache=n,a.prototype.define=function(e,t){var a=this,r="function"==typeof e,i=function(){var e=function(e,t){u[e]=t,n.status[e]=!0};return"function"==typeof t&&t(function(a,r){e(a,r),n.callback[a]=function(){t(e)}}),this};return r&&(t=e,e=[]),a.use(e,i,null,"define"),a},a.prototype.use=function(a,r,c,y){function d(e,t){var a="PLaySTATION 3"===navigator.platform?/^complete$/:/^(complete|loaded)$/;("load"===e.type||a.test((e.currentTarget||e.srcElement).readyState))&&(n.modules[g]=t,p.removeChild(w),function r(){return++v>1e3*n.timeout/4?o(g+" is not a valid module","error"):void(n.status[g]?m():setTimeout(r,4))}())}function m(){c.push(u[g]),a.length>1?f.use(a.slice(1),r,c,y):"function"==typeof r&&function(){return u.jquery&&"function"==typeof u.jquery&&"define"!==y?u.jquery(function(){r.apply(u,c)}):void r.apply(u,c)}()}var f=this,h=n.dir=n.dir?n.dir:i,p=t.getElementsByTagName("head")[0];a=function(){return"string"==typeof a?[a]:"function"==typeof a?(r=a,["all"]):a}(),e.jQuery&&jQuery.fn.on&&(f.each(a,function(e,t){"jquery"===t&&a.splice(e,1)}),u.jquery=u.$=jQuery);var g=a[0],v=0;if(c=c||[],n.host=n.host||(h.match(/\/\/([\s\S]+?)\//)||["//"+location.host+"/"])[0],0===a.length||u["layui.all"]&&s[g])return m(),f;var T=(s[g]?h+"modules/":/^\{\/\}/.test(f.modules[g])?"":n.base||"")+(f.modules[g]||g)+".js";if(T=T.replace(/^\{\/\}/,""),!n.modules[g]&&u[g]&&(n.modules[g]=T),n.modules[g])!function D(){return++v>1e3*n.timeout/4?o(g+" is not a valid module","error"):void("string"==typeof n.modules[g]&&n.status[g]?m():setTimeout(D,4))}();else{var w=t.createElement("script");w.async=!0,w.charset="utf-8",w.src=T+function(){var e=n.version===!0?n.v||(new Date).getTime():n.version||"";return e?"?v="+e:""}(),p.appendChild(w),!w.attachEvent||w.attachEvent.toString&&w.attachEvent.toString().indexOf("[native code")<0||l?w.addEventListener("load",function(e){d(e,T)},!1):w.attachEvent("onreadystatechange",function(e){d(e,T)}),n.modules[g]=T}return f},a.prototype.getStyle=function(t,n){var a=t.currentStyle?t.currentStyle:e.getComputedStyle(t,null);return a[a.getPropertyValue?"getPropertyValue":"getAttribute"](n)},a.prototype.link=function(e,a,r){var i=this,l=t.getElementsByTagName("head")[0],s=t.createElement("link");"string"==typeof a&&(r=a);var c=(r||e).replace(/\.|\//g,""),u=s.id="layuicss-"+c,y="creating",d=0;return s.rel="stylesheet",s.href=e+(n.debug?"?v="+(new Date).getTime():""),s.media="all",t.getElementById(u)||l.appendChild(s),"function"!=typeof a?i:(function m(r){var l=100,s=t.getElementById(u);return++d>1e3*n.timeout/l?o(e+" timeout"):void(1989===parseInt(i.getStyle(s,"width"))?(r===y&&s.removeAttribute("lay-status"),s.getAttribute("lay-status")===y?setTimeout(m,l):a()):(s.setAttribute("lay-status",y),setTimeout(function(){m(y)},l)))}(),i)},a.prototype.addcss=function(e,t,a){return u.link(n.dir+"css/"+e,t,a)},n.callback={},a.prototype.factory=function(e){if(u[e])return"function"==typeof n.callback[e]?n.callback[e]:null},a.prototype.img=function(e,t,n){var a=new Image;return a.src=e,a.complete?t(a):(a.onload=function(){a.onload=null,"function"==typeof t&&t(a)},void(a.onerror=function(e){a.onerror=null,"function"==typeof n&&n(e)}))},a.prototype.config=function(e){e=e||{};for(var t in e)n[t]=e[t];return this},a.prototype.modules=function(){var e={};for(var t in s)e[t]=s[t];return e}(),a.prototype.extend=function(e){var t=this;e=e||{};for(var n in e)t[n]||t.modules[n]?o(n+" Module already exists","error"):t.modules[n]=e[n];return t},a.prototype.router=function(e){var t=this,e=e||location.hash,n={path:[],search:{},hash:(e.match(/[^#](#.*$)/)||[])[1]||""};return/^#\//.test(e)?(e=e.replace(/^#\//,""),n.href="/"+e,e=e.replace(/([^#])(#.*$)/,"$1").split("/")||[],t.each(e,function(e,t){/^\w+=/.test(t)?function(){t=t.split("="),n.search[t[0]]=t[1]}():n.path.push(t)}),n):n},a.prototype.url=function(e){var t=this,n={pathname:function(){var t=e?function(){var t=(e.match(/\.[^.]+?\/.+/)||[])[0]||"";return t.replace(/^[^\/]+/,"").replace(/\?.+/,"")}():location.pathname;return t.replace(/^\//,"").split("/")}(),search:function(){var n={},a=(e?function(){var t=(e.match(/\?.+/)||[])[0]||"";return t.replace(/\#.+/,"")}():location.search).replace(/^\?+/,"").split("&");return t.each(a,function(e,t){var a=t.indexOf("="),r=function(){return a<0?t.substr(0,t.length):0!==a&&t.substr(0,a)}();r&&(n[r]=a>0?t.substr(a+1):null)}),n}(),hash:t.router(function(){return e?(e.match(/#.+/)||[])[0]||"/":location.hash}())};return n},a.prototype.data=function(t,n,a){if(t=t||"layui",a=a||localStorage,e.JSON&&e.JSON.parse){if(null===n)return delete a[t];n="object"==typeof n?n:{key:n};try{var r=JSON.parse(a[t])}catch(i){var r={}}return"value"in n&&(r[n.key]=n.value),n.remove&&delete r[n.key],a[t]=JSON.stringify(r),n.key?r[n.key]:r}},a.prototype.sessionData=function(e,t){return this.data(e,t,sessionStorage)},a.prototype.device=function(t){var n=navigator.userAgent.toLowerCase(),a=function(e){var t=new RegExp(e+"/([^\\s\\_\\-]+)");return e=(n.match(t)||[])[1],e||!1},r={os:function(){return/windows/.test(n)?"windows":/linux/.test(n)?"linux":/iphone|ipod|ipad|ios/.test(n)?"ios":/mac/.test(n)?"mac":void 0}(),ie:function(){return!!(e.ActiveXObject||"ActiveXObject"in e)&&((n.match(/msie\s(\d+)/)||[])[1]||"11")}(),weixin:a("micromessenger")};return t&&!r[t]&&(r[t]=a(t)),r.android=/android/.test(n),r.ios="ios"===r.os,r.mobile=!(!r.android&&!r.ios),r},a.prototype.hint=function(){return{error:o}},a.prototype._typeof=function(e){return null===e?String(e):"object"==typeof e||"function"==typeof e?function(){var t=Object.prototype.toString.call(e).match(/\s(.+)\]$/)||[],n="Function|Array|Date|RegExp|Object|Error|Symbol";return t=t[1]||"Object",new RegExp("\\b("+n+")\\b").test(t)?t.toLowerCase():"object"}():typeof e},a.prototype._isArray=function(t){var n,a=this,r=a._typeof(t);return!(!t||"object"!=typeof t||t===e)&&(n="length"in t&&t.length,"array"===r||0===n||"number"==typeof n&&n>0&&n-1 in t)},a.prototype.each=function(e,t){var n,a=this,r=function(e,n){return t.call(n[e],e,n[e])};if("function"!=typeof t)return a;if(e=e||[],a._isArray(e))for(n=0;n<e.length&&!r(n,e);n++);else for(n in e)if(r(n,e))break;return a},a.prototype.sort=function(e,t,n){var a=JSON.parse(JSON.stringify(e||[]));return t?(a.sort(function(e,n){var a=/^-?\d+$/,r=e[t],i=n[t];return a.test(r)&&(r=parseFloat(r)),a.test(i)&&(i=parseFloat(i)),r-i}),n&&a.reverse(),a):a},a.prototype.stope=function(t){t=t||e.event;try{t.stopPropagation()}catch(n){t.cancelBubble=!0}};var c="LAYUI-EVENT-REMOVE";a.prototype.onevent=function(e,t,n){return"string"!=typeof e||"function"!=typeof n?this:a.event(e,t,null,n)},a.prototype.event=a.event=function(e,t,a,r){var i=this,o=null,l=(t||"").match(/\((.*)\)$/)||[],s=(e+"."+t).replace(l[0],""),y=l[1]||"",d=function(e,t){var n=t&&t.call(i,a);n===!1&&null===o&&(o=!1)};return a===c?(delete(i.cache.event[s]||{})[y],i):r?(n.event[s]=n.event[s]||{},n.event[s][y]=[r],this):(u.each(n.event[s],function(e,t){return"{*}"===y?void u.each(t,d):(""===e&&u.each(t,d),void(y&&e===y&&u.each(t,d)))}),o)},a.prototype.on=function(e,t,n){var a=this;return a.onevent.call(a,t,e,n)},a.prototype.off=function(e,t){var n=this;return n.event.call(n,t,e,c)};var u=new a,y="lay",d=window.document,m=function(e){return new f(e)},f=function(e){for(var t=0,n="object"==typeof e?[e]:(this.selector=e,d.querySelectorAll(e||null));t<n.length;t++)this.push(n[t])};f.prototype=[],f.prototype.constructor=f,m.extend=function(){var e=1,t=arguments,n=function(e,t){e=e||(t.constructor===Array?[]:{});for(var a in t)e[a]=t[a]&&t[a].constructor===Object?n(e[a],t[a]):t[a];return e};for(t[0]="object"==typeof t[0]?t[0]:{};e<t.length;e++)"object"==typeof t[e]&&n(t[0],t[e]);return t[0]},m.v="1.0.7",m.ie=function(){var e=navigator.userAgent.toLowerCase();return!!(window.ActiveXObject||"ActiveXObject"in window)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")}(),m.layui=u,m.getPath=u.cache.dir,m.stope=u.stope,m.each=function(){return u.each.apply(u,arguments),this},m.digit=function(e,t,n){var a="";e=String(e),t=t||2;for(var r=e.length;r<t;r++)a+="0";return e<Math.pow(10,t)?a+(0|e):e},m.elem=function(e,t){var n=d.createElement(e);return m.each(t||{},function(e,t){n.setAttribute(e,t)}),n},m.hasScrollbar=function(){return d.body.scrollHeight>(window.innerHeight||d.documentElement.clientHeight)},m.position=function(e,t,n){if(t){n=n||{},e!==d&&e!==m("body")[0]||(n.clickType="right");var a="right"===n.clickType?function(){var e=n.e||window.event||{};return{left:e.clientX,top:e.clientY,right:e.clientX,bottom:e.clientY}}():e.getBoundingClientRect(),r=t.offsetWidth,i=t.offsetHeight,o=function(e){return e=e?"scrollLeft":"scrollTop",d.body[e]|d.documentElement[e]},l=function(e){return d.documentElement[e?"clientWidth":"clientHeight"]},s=5,c=a.left,u=a.bottom;c+r+s>l("width")&&(c=l("width")-r-s),u+i+s>l()&&(a.top>i+s?u=a.top-i-2*s:"right"===n.clickType&&(u=l()-i-2*s,u<0&&(u=0)));var y=n.position;if(y&&(t.style.position=y),t.style.left=c+("fixed"===y?0:o(1))+"px",t.style.top=u+("fixed"===y?0:o())+"px",!m.hasScrollbar()){var f=t.getBoundingClientRect();!n.SYSTEM_RELOAD&&f.bottom+s>l()&&(n.SYSTEM_RELOAD=!0,setTimeout(function(){m.position(e,t,n)},50))}}},m.options=function(e,t){var n=m(e),a=t||"lay-options";try{return new Function("return "+(n.attr(a)||"{}"))()}catch(r){return hint.error("parseerror\uff1a"+r,"error"),{}}},m.isTopElem=function(e){var t=[d,m("body")[0]],n=!1;return m.each(t,function(t,a){if(a===e)return n=!0}),n},f.addStr=function(e,t){return e=e.replace(/\s+/," "),t=t.replace(/\s+/," ").split(" "),m.each(t,function(t,n){new RegExp("\\b"+n+"\\b").test(e)||(e=e+" "+n)}),e.replace(/^\s|\s$/,"")},f.removeStr=function(e,t){return e=e.replace(/\s+/," "),t=t.replace(/\s+/," ").split(" "),m.each(t,function(t,n){var a=new RegExp("\\b"+n+"\\b");a.test(e)&&(e=e.replace(a,""))}),e.replace(/\s+/," ").replace(/^\s|\s$/,"")},f.prototype.find=function(e){var t=this,n=0,a=[],r="object"==typeof e;return this.each(function(i,o){for(var l=r?o.contains(e):o.querySelectorAll(e||null);n<l.length;n++)a.push(l[n]);t.shift()}),r||(t.selector=(t.selector?t.selector+" ":"")+e),m.each(a,function(e,n){t.push(n)}),t},f.prototype.each=function(e){return m.each.call(this,this,e)},f.prototype.addClass=function(e,t){return this.each(function(n,a){a.className=f[t?"removeStr":"addStr"](a.className,e)})},f.prototype.removeClass=function(e){return this.addClass(e,!0)},f.prototype.hasClass=function(e){var t=!1;return this.each(function(n,a){new RegExp("\\b"+e+"\\b").test(a.className)&&(t=!0)}),t},f.prototype.css=function(e,t){var n=this,a=function(e){return isNaN(e)?e:e+"px"};return"string"==typeof e&&void 0===t?function(){if(n.length>0)return n[0].style[e]}():n.each(function(n,r){"object"==typeof e?m.each(e,function(e,t){r.style[e]=a(t)}):r.style[e]=a(t)})},f.prototype.width=function(e){var t=this;return void 0===e?function(){if(t.length>0)return t[0].offsetWidth}():t.each(function(n,a){t.css("width",e)})},f.prototype.height=function(e){var t=this;return void 0===e?function(){if(t.length>0)return t[0].offsetHeight}():t.each(function(n,a){t.css("height",e)})},f.prototype.attr=function(e,t){var n=this;return void 0===t?function(){if(n.length>0)return n[0].getAttribute(e)}():n.each(function(n,a){a.setAttribute(e,t)})},f.prototype.removeAttr=function(e){return this.each(function(t,n){n.removeAttribute(e)})},f.prototype.html=function(e){var t=this;return void 0===e?function(){if(t.length>0)return t[0].innerHTML}():this.each(function(t,n){n.innerHTML=e})},f.prototype.val=function(e){var t=this;return void 0===e?function(){if(t.length>0)return t[0].value}():this.each(function(t,n){n.value=e})},f.prototype.append=function(e){return this.each(function(t,n){"object"==typeof e?n.appendChild(e):n.innerHTML=n.innerHTML+e})},f.prototype.remove=function(e){return this.each(function(t,n){e?n.removeChild(e):n.parentNode.removeChild(n)})},f.prototype.on=function(e,t){return this.each(function(n,a){a.attachEvent?a.attachEvent("on"+e,function(e){e.target=e.srcElement,t.call(a,e)}):a.addEventListener(e,t,!1)})},f.prototype.off=function(e,t){return this.each(function(n,a){a.detachEvent?a.detachEvent("on"+e,t):a.removeEventListener(e,t,!1)})},window.lay=m,window.layui&&u.define&&u.define(function(e){e(y,m)})}(window,window.document),!function(e,t){"use strict";var n=e.layui&&layui.define,a={getPath:e.lay&&lay.getPath?lay.getPath:"",link:function(t,n,a){i.path&&e.lay&&lay.layui&&lay.layui.link(i.path+t,n,a)}},r=e.LAYUI_GLOBAL||{},i={v:"5.3.1",config:{},index:e.laydate&&e.laydate.v?1e5:0,path:r.laydate_dir||a.getPath,set:function(e){var t=this;return t.config=lay.extend({},t.config,e),t},ready:function(e){var t="laydate",r="",o=(n?"modules/laydate/":"theme/")+"default/laydate.css?v="+i.v+r;return n?layui.addcss(o,e,t):a.link(o,e,t),this}},o=function(){var e=this,t=e.config,n=t.id;return o.that[n]=e,{hint:function(t){e.hint.call(e,t)},config:e.config}},l="laydate",s=".layui-laydate",c="layui-this",u="laydate-disabled",y=[100,2e5],d="layui-laydate-static",m="layui-laydate-list",f="layui-laydate-hint",h="layui-laydate-footer",p=".laydate-btns-confirm",g="laydate-time-text",v="laydate-btns-time",T="layui-laydate-preview",w=function(e){var t=this;t.index=++i.index,t.config=lay.extend({},t.config,i.config,e),e=t.config,e.id="id"in e?e.id:t.index,i.ready(function(){t.init()})},D="yyyy|y|MM|M|dd|d|HH|H|mm|m|ss|s";o.formatArr=function(e){return(e||"").match(new RegExp(D+"|.","g"))||[]},w.isLeapYear=function(e){return e%4===0&&e%100!==0||e%400===0},w.prototype.config={type:"date",range:!1,format:"yyyy-MM-dd",value:null,isInitValue:!0,min:"1900-1-1",max:"2099-12-31",trigger:"click",show:!1,showBottom:!0,isPreview:!0,btns:["clear","now","confirm"],lang:"cn",theme:"default",position:null,calendar:!1,mark:{},zIndex:null,done:null,change:null},w.prototype.lang=function(){var e=this,t=e.config,n={cn:{weeks:["\u65e5","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d"],time:["\u65f6","\u5206","\u79d2"],timeTips:"\u9009\u62e9\u65f6\u95f4",startTime:"\u5f00\u59cb\u65f6\u95f4",endTime:"\u7ed3\u675f\u65f6\u95f4",dateTips:"\u8fd4\u56de\u65e5\u671f",month:["\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d","\u4e03","\u516b","\u4e5d","\u5341","\u5341\u4e00","\u5341\u4e8c"],tools:{confirm:"\u786e\u5b9a",clear:"\u6e05\u7a7a",now:"\u73b0\u5728"},timeout:"\u7ed3\u675f\u65f6\u95f4\u4e0d\u80fd\u65e9\u4e8e\u5f00\u59cb\u65f6\u95f4<br>\u8bf7\u91cd\u65b0\u9009\u62e9",invalidDate:"\u4e0d\u5728\u6709\u6548\u65e5\u671f\u6216\u65f6\u95f4\u8303\u56f4\u5185",formatError:["\u65e5\u671f\u683c\u5f0f\u4e0d\u5408\u6cd5<br>\u5fc5\u987b\u9075\u5faa\u4e0b\u8ff0\u683c\u5f0f\uff1a<br>","<br>\u5df2\u4e3a\u4f60\u91cd\u7f6e"],preview:"\u5f53\u524d\u9009\u4e2d\u7684\u7ed3\u679c"},en:{weeks:["Su","Mo","Tu","We","Th","Fr","Sa"],time:["Hours","Minutes","Seconds"],timeTips:"Select Time",startTime:"Start Time",endTime:"End Time",dateTips:"Select Date",month:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],tools:{confirm:"Confirm",clear:"Clear",now:"Now"},timeout:"End time cannot be less than start Time<br>Please re-select",invalidDate:"Invalid date",formatError:["The date format error<br>Must be followed\uff1a<br>","<br>It has been reset"],preview:"The selected result"}};return n[t.lang]||n.cn},w.prototype.init=function(){var t=this,n=t.config,a="static"===n.position,r={year:"yyyy",month:"yyyy-MM",date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss"};n.elem=lay(n.elem),n.eventElem=lay(n.eventElem),n.elem[0]&&(t.rangeStr=n.range?"string"==typeof n.range?n.range:"-":"",n.range&&n.range.constructor===Array&&(t.rangeElem=[lay(n.range[0]),lay(n.range[1])]),r[n.type]||(e.console&&console.error&&console.error("laydate type error:'"+n.type+"' is not supported"),n.type="date"),n.format===r.date&&(n.format=r[n.type]||r.date),t.format=o.formatArr(n.format),t.EXP_IF="",t.EXP_SPLIT="",lay.each(t.format,function(e,n){var a=new RegExp(D).test(n)?"\\d{"+function(){return new RegExp(D).test(t.format[0===e?e+1:e-1]||"")?/^yyyy|y$/.test(n)?4:n.length:/^yyyy$/.test(n)?"1,4":/^y$/.test(n)?"1,308":"1,2"}()+"}":"\\"+n;t.EXP_IF=t.EXP_IF+a,t.EXP_SPLIT=t.EXP_SPLIT+"("+a+")"}),t.EXP_IF_ONE=new RegExp("^"+t.EXP_IF+"$"),t.EXP_IF=new RegExp("^"+(n.range?t.EXP_IF+"\\s\\"+t.rangeStr+"\\s"+t.EXP_IF:t.EXP_IF)+"$"),t.EXP_SPLIT=new RegExp("^"+t.EXP_SPLIT+"$",""),t.isInput(n.elem[0])||"focus"===n.trigger&&(n.trigger="click"),n.elem.attr("lay-key")||(n.elem.attr("lay-key",t.index),n.eventElem.attr("lay-key",t.index)),n.mark=lay.extend({},n.calendar&&"cn"===n.lang?{"0-1-1":"\u5143\u65e6","0-2-14":"\u60c5\u4eba","0-3-8":"\u5987\u5973","0-3-12":"\u690d\u6811","0-4-1":"\u611a\u4eba","0-5-1":"\u52b3\u52a8","0-5-4":"\u9752\u5e74","0-6-1":"\u513f\u7ae5","0-9-10":"\u6559\u5e08","0-9-18":"\u56fd\u803b","0-10-1":"\u56fd\u5e86","0-12-25":"\u5723\u8bde"}:{},n.mark),lay.each(["min","max"],function(e,t){var a=[],r=[];if("number"==typeof n[t]){var i=n[t],o=(new Date).getTime(),l=864e5,s=new Date(i?i<l?o+i*l:i:o);a=[s.getFullYear(),s.getMonth()+1,s.getDate()],i<l||(r=[s.getHours(),s.getMinutes(),s.getSeconds()])}else a=(n[t].match(/\d+-\d+-\d+/)||[""])[0].split("-"),r=(n[t].match(/\d+:\d+:\d+/)||[""])[0].split(":");n[t]={year:0|a[0]||(new Date).getFullYear(),month:a[1]?(0|a[1])-1:(new Date).getMonth(),date:0|a[2]||(new Date).getDate(),hours:0|r[0],minutes:0|r[1],seconds:0|r[2]}}),t.elemID="layui-laydate"+n.elem.attr("lay-key"),(n.show||a)&&t.render(),a||t.events(),n.value&&n.isInitValue&&(n.value.constructor===Date?t.setValue(t.parse(0,t.systemDate(n.value))):t.setValue(n.value)))},w.prototype.render=function(){var e=this,n=e.config,a=e.lang(),r="static"===n.position,o=e.elem=lay.elem("div",{id:e.elemID,"class":["layui-laydate",n.range?" layui-laydate-range":"",r?" "+d:"",n.theme&&"default"!==n.theme&&!/^#/.test(n.theme)?" laydate-theme-"+n.theme:""].join("")}),l=e.elemMain=[],s=e.elemHeader=[],c=e.elemCont=[],u=e.table=[],y=e.footer=lay.elem("div",{"class":h});if(n.zIndex&&(o.style.zIndex=n.zIndex),lay.each(new Array(2),function(e){if(!n.range&&e>0)return!0;var t=lay.elem("div",{"class":"layui-laydate-header"}),r=[function(){var e=lay.elem("i",{"class":"layui-icon laydate-icon laydate-prev-y"});return e.innerHTML="&#xe65a;",e}(),function(){var e=lay.elem("i",{"class":"layui-icon laydate-icon laydate-prev-m"});return e.innerHTML="&#xe603;",e}(),function(){var e=lay.elem("div",{"class":"laydate-set-ym"}),t=lay.elem("span"),n=lay.elem("span");return e.appendChild(t),e.appendChild(n),e}(),function(){var e=lay.elem("i",{"class":"layui-icon laydate-icon laydate-next-m"});return e.innerHTML="&#xe602;",e}(),function(){var e=lay.elem("i",{"class":"layui-icon laydate-icon laydate-next-y"});return e.innerHTML="&#xe65b;",e}()],i=lay.elem("div",{"class":"layui-laydate-content"}),o=lay.elem("table"),y=lay.elem("thead"),d=lay.elem("tr");lay.each(r,function(e,n){t.appendChild(n)}),y.appendChild(d),lay.each(new Array(6),function(e){var t=o.insertRow(0);lay.each(new Array(7),function(n){if(0===e){var r=lay.elem("th");r.innerHTML=a.weeks[n],d.appendChild(r)}t.insertCell(n)})}),o.insertBefore(y,o.children[0]),i.appendChild(o),l[e]=lay.elem("div",{"class":"layui-laydate-main laydate-main-list-"+e}),l[e].appendChild(t),l[e].appendChild(i),s.push(r),c.push(i),u.push(o)}),lay(y).html(function(){var e=[],t=[];return"datetime"===n.type&&e.push('<span lay-type="datetime" class="'+v+'">'+a.timeTips+"</span>"),(n.range||"datetime"!==n.type)&&e.push('<span class="'+T+'" title="'+a.preview+'"></span>'),lay.each(n.btns,function(e,i){var o=a.tools[i]||"btn";n.range&&"now"===i||(r&&"clear"===i&&(o="cn"===n.lang?"\u91cd\u7f6e":"Reset"),t.push('<span lay-type="'+i+'" class="laydate-btns-'+i+'">'+o+"</span>"))}),e.push('<div class="laydate-footer-btns">'+t.join("")+"</div>"),e.join("")}()),lay.each(l,function(e,t){o.appendChild(t)}),n.showBottom&&o.appendChild(y),/^#/.test(n.theme)){var m=lay.elem("style"),f=["#{{id}} .layui-laydate-header{background-color:{{theme}};}","#{{id}} .layui-this{background-color:{{theme}} !important;}"].join("").replace(/{{id}}/g,e.elemID).replace(/{{theme}}/g,n.theme);"styleSheet"in m?(m.setAttribute("type","text/css"),m.styleSheet.cssText=f):m.innerHTML=f,lay(o).addClass("laydate-theme-molv"),o.appendChild(m)}i.thisId=n.id,e.remove(w.thisElemDate),r?n.elem.append(o):(t.body.appendChild(o),e.position()),e.checkDate().calendar(null,0,"init"),e.changeEvent(),w.thisElemDate=e.elemID,"function"==typeof n.ready&&n.ready(lay.extend({},n.dateTime,{month:n.dateTime.month+1})),e.preview()},w.prototype.remove=function(e){var t=this,n=(t.config,lay("#"+(e||t.elemID)));return n[0]?(n.hasClass(d)||t.checkDate(function(){n.remove()}),t):t},w.prototype.position=function(){var e=this,t=e.config;return lay.position(e.bindElem||t.elem[0],e.elem,{position:t.position}),e},w.prototype.hint=function(e){var t=this,n=(t.config,lay.elem("div",{"class":f}));t.elem&&(n.innerHTML=e||"",lay(t.elem).find("."+f).remove(),t.elem.appendChild(n),clearTimeout(t.hinTimer),t.hinTimer=setTimeout(function(){lay(t.elem).find("."+f).remove()},3e3))},w.prototype.getAsYM=function(e,t,n){return n?t--:t++,t<0&&(t=11,e--),t>11&&(t=0,e++),[e,t]},w.prototype.systemDate=function(e){var t=e||new Date;return{year:t.getFullYear(),month:t.getMonth(),date:t.getDate(),hours:e?e.getHours():0,minutes:e?e.getMinutes():0,seconds:e?e.getSeconds():0}},w.prototype.checkDate=function(e){var t,n,a=this,r=(new Date,a.config),o=a.lang(),l=r.dateTime=r.dateTime||a.systemDate(),s=a.bindElem||r.elem[0],c=(a.isInput(s)?"val":"html",function(){if(a.rangeElem){var e=[a.rangeElem[0].val(),a.rangeElem[1].val()];if(e[0]&&e[1])return e.join(" "+a.rangeStr+" ")}return a.isInput(s)?s.value:"static"===r.position?"":lay(s).attr("lay-date")}()),u=function(e){e.year>y[1]&&(e.year=y[1],n=!0),e.month>11&&(e.month=11,n=!0),e.hours>23&&(e.hours=0,n=!0),e.minutes>59&&(e.minutes=0,e.hours++,n=!0),e.seconds>59&&(e.seconds=0,e.minutes++,n=!0),t=i.getEndDate(e.month+1,e.year),e.date>t&&(e.date=t,n=!0)},d=function(e,t,i){var o=["startTime","endTime"];t=(t.match(a.EXP_SPLIT)||[]).slice(1),i=i||0,r.range&&(a[o[i]]=a[o[i]]||{}),lay.each(a.format,function(l,s){var c=parseFloat(t[l]);t[l].length<s.length&&(n=!0),/yyyy|y/.test(s)?(c<y[0]&&(c=y[0],n=!0),e.year=c):/MM|M/.test(s)?(c<1&&(c=1,n=!0),e.month=c-1):/dd|d/.test(s)?(c<1&&(c=1,n=!0),e.date=c):/HH|H/.test(s)?(c<1&&(c=0,n=!0),e.hours=c,r.range&&(a[o[i]].hours=c)):/mm|m/.test(s)?(c<1&&(c=0,n=!0),e.minutes=c,r.range&&(a[o[i]].minutes=c)):/ss|s/.test(s)&&(c<1&&(c=0,n=!0),e.seconds=c,r.range&&(a[o[i]].seconds=c))}),u(e)};if("limit"===e)return u(l),a;c=c||r.value,"string"==typeof c&&(c=c.replace(/\s+/g," ").replace(/^\s|\s$/g,""));var m=function(){r.range&&(a.endDate=a.endDate||lay.extend({},r.dateTime,function(){var e={},t=r.dateTime,n=a.getAsYM(t.year,t.month);return"year"===r.type?e.year=t.year+1:"time"!==r.type&&(e.year=n[0],e.month=n[1]),"datetime"!==r.type&&"time"!==r.type||(e.hours=23,e.minutes=e.seconds=59),e}()))};m(),"string"==typeof c&&c?a.EXP_IF.test(c)?r.range?(c=c.split(" "+a.rangeStr+" "),lay.each([r.dateTime,a.endDate],function(e,t){d(t,c[e],e)})):d(l,c):(a.hint(o.formatError[0]+(r.range?r.format+" "+a.rangeStr+" "+r.format:r.format)+o.formatError[1]),n=!0):c&&c.constructor===Date?r.dateTime=a.systemDate(c):(r.dateTime=a.systemDate(),delete a.startTime,delete a.endDate,m(),delete a.endTime),function(){if(a.rangeElem){var e=[a.rangeElem[0].val(),a.rangeElem[1].val()],t=[r.dateTime,a.endDate];lay.each(e,function(e,n){a.EXP_IF_ONE.test(n)&&d(t[e],n,e)})}}(),u(l),r.range&&u(a.endDate),n&&c&&a.setValue(r.range?a.endDate?a.parse():"":a.parse());var f=function(e){return a.newDate(e).getTime()};return(f(l)>f(r.max)||f(l)<f(r.min))&&(l=r.dateTime=lay.extend({},r.min)),r.range&&(f(a.endDate)<f(r.min)||f(a.endDate)>f(r.max))&&(a.endDate=lay.extend({},r.max)),e&&e(),a},w.prototype.mark=function(e,t){var n,a=this,r=a.config;return lay.each(r.mark,function(e,a){var r=e.split("-");r[0]!=t[0]&&0!=r[0]||r[1]!=t[1]&&0!=r[1]||r[2]!=t[2]||(n=a||t[2])}),n&&e.html('<span class="laydate-day-mark">'+n+"</span>"),a},w.prototype.limit=function(e,t,n,a){var r,i=this,o=i.config,l={},s=o[n>41?"endDate":"dateTime"],c=lay.extend({},s,t||{});return lay.each({now:c,min:o.min,max:o.max},function(e,t){l[e]=i.newDate(lay.extend({year:t.year,month:t.month,date:t.date},function(){var e={};return lay.each(a,function(n,a){e[a]=t[a]}),e}())).getTime()}),r=l.now<l.min||l.now>l.max,e&&e[r?"addClass":"removeClass"](u),r},w.prototype.thisDateTime=function(e){var t=this,n=t.config;return e?t.endDate:n.dateTime},w.prototype.calendar=function(e,t,n){var a,r,o,l=this,s=l.config,t=t?1:0,u=e||l.thisDateTime(t),d=new Date,m=l.lang(),f="date"!==s.type&&"datetime"!==s.type,h=lay(l.table[t]).find("td"),g=lay(l.elemHeader[t][2]).find("span");return u.year<y[0]&&(u.year=y[0],l.hint(m.invalidDate)),u.year>y[1]&&(u.year=y[1],l.hint(m.invalidDate)),l.firstDate||(l.firstDate=lay.extend({},u)),d.setFullYear(u.year,u.month,1),a=d.getDay(),r=i.getEndDate(u.month||12,u.year),o=i.getEndDate(u.month+1,u.year),lay.each(h,function(e,t){var n=[u.year,u.month],i=0;t=lay(t),t.removeAttr("class"),e<a?(i=r-a+e,t.addClass("laydate-day-prev"),n=l.getAsYM(u.year,u.month,"sub")):e>=a&&e<o+a?(i=e-a,i+1===u.date&&t.addClass(c)):(i=e-o-a,t.addClass("laydate-day-next"),n=l.getAsYM(u.year,u.month)),n[1]++,n[2]=i+1,t.attr("lay-ymd",n.join("-")).html(n[2]),l.mark(t,n).limit(t,{year:n[0],month:n[1]-1,date:n[2]},e)}),lay(g[0]).attr("lay-ym",u.year+"-"+(u.month+1)),lay(g[1]).attr("lay-ym",u.year+"-"+(u.month+1)),"cn"===s.lang?(lay(g[0]).attr("lay-type","year").html(u.year+" \u5e74"),lay(g[1]).attr("lay-type","month").html(u.month+1+" \u6708")):(lay(g[0]).attr("lay-type","month").html(m.month[u.month]),lay(g[1]).attr("lay-type","year").html(u.year)),f&&(s.range?e&&(l.listYM=[[s.dateTime.year,s.dateTime.month+1],[l.endDate.year,l.endDate.month+1]],l.list(s.type,0).list(s.type,1),"time"===s.type?l.setBtnStatus("\u65f6\u95f4",lay.extend({},l.systemDate(),l.startTime),lay.extend({},l.systemDate(),l.endTime)):l.setBtnStatus(!0)):(l.listYM=[[u.year,u.month+1]],l.list(s.type,0))),s.range&&"init"===n&&!e&&l.calendar(l.endDate,1),s.range||l.limit(lay(l.footer).find(p),null,0,["hours","minutes","seconds"]),l.setBtnStatus(),l},w.prototype.list=function(e,t){var n=this,a=n.config,r=a.dateTime,i=n.lang(),o=a.range&&"date"!==a.type&&"datetime"!==a.type,l=lay.elem("ul",{"class":m+" "+{year:"laydate-year-list",month:"laydate-month-list",time:"laydate-time-list"}[e]}),s=n.elemHeader[t],y=lay(s[2]).find("span"),d=n.elemCont[t||0],f=lay(d).find("."+m)[0],h="cn"===a.lang,T=h?"\u5e74":"",w=n.listYM[t]||{},D=["hours","minutes","seconds"],b=["startTime","endTime"][t];if(w[0]<1&&(w[0]=1),"year"===e){var E,x=E=w[0]-7;x<1&&(x=E=1),lay.each(new Array(15),function(e){var r=lay.elem("li",{"lay-ym":E}),i={year:E};E==w[0]&&lay(r).addClass(c),r.innerHTML=E+T,l.appendChild(r),E<n.firstDate.year?(i.month=a.min.month,i.date=a.min.date):E>=n.firstDate.year&&(i.month=a.max.month,i.date=a.max.date),n.limit(lay(r),i,t),E++}),lay(y[h?0:1]).attr("lay-ym",E-8+"-"+w[1]).html(x+T+" - "+(E-1+T))}else if("month"===e)lay.each(new Array(12),function(e){var r=lay.elem("li",{"lay-ym":e}),o={year:w[0],month:e};e+1==w[1]&&lay(r).addClass(c),r.innerHTML=i.month[e]+(h?"\u6708":""),l.appendChild(r),w[0]<n.firstDate.year?o.date=a.min.date:w[0]>=n.firstDate.year&&(o.date=a.max.date),n.limit(lay(r),o,t)}),lay(y[h?0:1]).attr("lay-ym",w[0]+"-"+w[1]).html(w[0]+T);else if("time"===e){var C=function(){lay(l).find("ol").each(function(e,a){lay(a).find("li").each(function(a,r){n.limit(lay(r),[{hours:a},{hours:n[b].hours,minutes:a},{hours:n[b].hours,minutes:n[b].minutes,seconds:a}][e],t,[["hours"],["hours","minutes"],["hours","minutes","seconds"]][e])})}),a.range||n.limit(lay(n.footer).find(p),n[b],0,["hours","minutes","seconds"])};a.range?n[b]||(n[b]="startTime"===b?r:n.endDate):n[b]=r,lay.each([24,60,60],function(e,t){var a=lay.elem("li"),r=["<p>"+i.time[e]+"</p><ol>"];lay.each(new Array(t),function(t){r.push("<li"+(n[b][D[e]]===t?' class="'+c+'"':"")+">"+lay.digit(t,2)+"</li>")}),a.innerHTML=r.join("")+"</ol>",l.appendChild(a)}),C()}if(f&&d.removeChild(f),d.appendChild(l),"year"===e||"month"===e)lay(n.elemMain[t]).addClass("laydate-ym-show"),lay(l).find("li").on("click",function(){var i=0|lay(this).attr("lay-ym");if(!lay(this).hasClass(u)){0===t?(r[e]=i,n.limit(lay(n.footer).find(p),null,0)):n.endDate[e]=i;var s="year"===a.type||"month"===a.type;s?(lay(l).find("."+c).removeClass(c),lay(this).addClass(c),"month"===a.type&&"year"===e&&(n.listYM[t][0]=i,o&&((t?n.endDate:r).year=i),n.list("month",t))):(n.checkDate("limit").calendar(null,t),n.closeList()),n.setBtnStatus(),a.range||("month"===a.type&&"month"===e||"year"===a.type&&"year"===e)&&n.setValue(n.parse()).remove().done(),n.done(null,"change"),lay(n.footer).find("."+v).removeClass(u)}});else{var M=lay.elem("span",{"class":g}),S=function(){lay(l).find("ol").each(function(e){var t=this,a=lay(t).find("li");t.scrollTop=30*(n[b][D[e]]-2),t.scrollTop<=0&&a.each(function(e,n){if(!lay(this).hasClass(u))return t.scrollTop=30*(e-2),!0})})},k=lay(s[2]).find("."+g);S(),M.innerHTML=a.range?[i.startTime,i.endTime][t]:i.timeTips,lay(n.elemMain[t]).addClass("laydate-time-show"),k[0]&&k.remove(),s[2].appendChild(M),lay(l).find("ol").each(function(e){var t=this;lay(t).find("li").on("click",function(){var i=0|this.innerHTML;lay(this).hasClass(u)||(a.range?n[b][D[e]]=i:r[D[e]]=i,lay(t).find("."+c).removeClass(c),lay(this).addClass(c),C(),S(),(n.endDate||"time"===a.type)&&n.done(null,"change"),n.setBtnStatus())})})}return n},w.prototype.listYM=[],w.prototype.closeList=function(){var e=this;e.config;lay.each(e.elemCont,function(t,n){lay(this).find("."+m).remove(),lay(e.elemMain[t]).removeClass("laydate-ym-show laydate-time-show")}),lay(e.elem).find("."+g).remove()},w.prototype.setBtnStatus=function(e,t,n){var a,r=this,i=r.config,o=r.lang(),l=lay(r.footer).find(p);i.range&&"time"!==i.type&&(t=t||i.dateTime,n=n||r.endDate,a=r.newDate(t).getTime()>r.newDate(n).getTime(),r.limit(null,t)||r.limit(null,n)?l.addClass(u):l[a?"addClass":"removeClass"](u),e&&a&&r.hint("string"==typeof e?o.timeout.replace(/\u65e5\u671f/g,e):o.timeout))},w.prototype.parse=function(e,t){var n=this,a=n.config,r=t||("end"==e?lay.extend({},n.endDate,n.endTime):a.range?lay.extend({},a.dateTime,n.startTime):a.dateTime),o=i.parse(r,n.format,1);return a.range&&void 0===e?o+" "+n.rangeStr+" "+n.parse("end"):o},w.prototype.newDate=function(e){return e=e||{},new Date(e.year||1,e.month||0,e.date||1,e.hours||0,e.minutes||0,e.seconds||0)},w.prototype.setValue=function(e){var t=this,n=t.config,a=t.bindElem||n.elem[0];return"static"===n.position?t:(e=e||"",t.isInput(a)?lay(a).val(e):t.rangeElem?(t.rangeElem[0].val(e?t.parse("start"):""),t.rangeElem[1].val(e?t.parse("end"):"")):(0===lay(a).find("*").length&&lay(a).html(e),lay(a).attr("lay-date",e)),t)},w.prototype.preview=function(){var e=this,t=e.config;if(t.isPreview){var n=lay(e.elem).find("."+T),a=t.range?e.endDate?e.parse():"":e.parse();n.html(a).css({color:"#5FB878","font-size":"14px;"}),setTimeout(function(){
n.css({color:"#666","font-size":"12px;"})},300)}},w.prototype.done=function(e,t){var n=this,a=n.config,r=lay.extend({},lay.extend(a.dateTime,n.startTime)),i=lay.extend({},lay.extend(n.endDate,n.endTime));return lay.each([r,i],function(e,t){"month"in t&&lay.extend(t,{month:t.month+1})}),n.preview(),e=e||[n.parse(),r,i],"function"==typeof a[t||"done"]&&a[t||"done"].apply(a,e),n},w.prototype.choose=function(e,t){var n=this,a=n.config,r=n.thisDateTime(t),i=(lay(n.elem).find("td"),e.attr("lay-ymd").split("-"));i={year:0|i[0],month:(0|i[1])-1,date:0|i[2]},e.hasClass(u)||(lay.extend(r,i),a.range?(lay.each(["startTime","endTime"],function(e,t){n[t]=n[t]||{hours:0,minutes:0,seconds:0}}),n.calendar(null,t).done(null,"change")):"static"===a.position?n.calendar().done().done(null,"change"):"date"===a.type?n.setValue(n.parse()).remove().done():"datetime"===a.type&&n.calendar().done(null,"change"))},w.prototype.tool=function(e,t){var n=this,a=n.config,r=n.lang(),i=a.dateTime,o="static"===a.position,l={datetime:function(){lay(e).hasClass(u)||(n.list("time",0),a.range&&n.list("time",1),lay(e).attr("lay-type","date").html(n.lang().dateTips))},date:function(){n.closeList(),lay(e).attr("lay-type","datetime").html(n.lang().timeTips)},clear:function(){o&&(lay.extend(i,n.firstDate),n.calendar()),a.range&&(delete a.dateTime,delete n.endDate,delete n.startTime,delete n.endTime),n.setValue("").remove(),n.done(["",{},{}])},now:function(){var e=new Date;lay.extend(i,n.systemDate(),{hours:e.getHours(),minutes:e.getMinutes(),seconds:e.getSeconds()}),n.setValue(n.parse()).remove(),o&&n.calendar(),n.done()},confirm:function(){if(a.range){if(lay(e).hasClass(u))return n.hint("time"===a.type?r.timeout.replace(/\u65e5\u671f/g,"\u65f6\u95f4"):r.timeout)}else if(lay(e).hasClass(u))return n.hint(r.invalidDate);n.done(),n.setValue(n.parse()).remove()}};l[t]&&l[t]()},w.prototype.change=function(e){var t=this,n=t.config,a=t.thisDateTime(e),r=n.range&&("year"===n.type||"month"===n.type),i=t.elemCont[e||0],o=t.listYM[e],l=function(l){var s=lay(i).find(".laydate-year-list")[0],c=lay(i).find(".laydate-month-list")[0];return s&&(o[0]=l?o[0]-15:o[0]+15,t.list("year",e)),c&&(l?o[0]--:o[0]++,t.list("month",e)),(s||c)&&(lay.extend(a,{year:o[0]}),r&&(a.year=o[0]),n.range||t.done(null,"change"),n.range||t.limit(lay(t.footer).find(p),{year:o[0]})),t.setBtnStatus(),s||c};return{prevYear:function(){l("sub")||(a.year--,t.checkDate("limit").calendar(null,e),t.done(null,"change"))},prevMonth:function(){var n=t.getAsYM(a.year,a.month,"sub");lay.extend(a,{year:n[0],month:n[1]}),t.checkDate("limit").calendar(null,e),t.done(null,"change")},nextMonth:function(){var n=t.getAsYM(a.year,a.month);lay.extend(a,{year:n[0],month:n[1]}),t.checkDate("limit").calendar(null,e),t.done(null,"change")},nextYear:function(){l()||(a.year++,t.checkDate("limit").calendar(null,e),t.done(null,"change"))}}},w.prototype.changeEvent=function(){var e=this;e.config;lay(e.elem).on("click",function(e){lay.stope(e)}).on("mousedown",function(e){lay.stope(e)}),lay.each(e.elemHeader,function(t,n){lay(n[0]).on("click",function(n){e.change(t).prevYear()}),lay(n[1]).on("click",function(n){e.change(t).prevMonth()}),lay(n[2]).find("span").on("click",function(n){var a=lay(this),r=a.attr("lay-ym"),i=a.attr("lay-type");r&&(r=r.split("-"),e.listYM[t]=[0|r[0],0|r[1]],e.list(i,t),lay(e.footer).find("."+v).addClass(u))}),lay(n[3]).on("click",function(n){e.change(t).nextMonth()}),lay(n[4]).on("click",function(n){e.change(t).nextYear()})}),lay.each(e.table,function(t,n){var a=lay(n).find("td");a.on("click",function(){e.choose(lay(this),t)})}),lay(e.footer).find("span").on("click",function(){var t=lay(this).attr("lay-type");e.tool(this,t)})},w.prototype.isInput=function(e){return/input|textarea/.test(e.tagName.toLocaleLowerCase())},w.prototype.events=function(){var e=this,t=e.config,n=function(n,a){n.on(t.trigger,function(){a&&(e.bindElem=this),e.render()})};t.elem[0]&&!t.elem[0].eventHandler&&(n(t.elem,"bind"),n(t.eventElem),t.elem[0].eventHandler=!0)},o.that={},o.getThis=function(e){var t=o.that[e];return t||hint.error(e?l+" instance with ID '"+e+"' not found":"ID argument required"),t},a.run=function(n){n(t).on("mousedown",function(e){if(i.thisId){var t=o.getThis(i.thisId);if(t){var a=t.config;e.target!==a.elem[0]&&e.target!==a.eventElem[0]&&e.target!==n(a.closeStop)[0]&&t.remove()}}}).on("keydown",function(e){if(i.thisId){var t=o.getThis(i.thisId);t&&13===e.keyCode&&n("#"+t.elemID)[0]&&t.elemID===w.thisElemDate&&(e.preventDefault(),n(t.footer).find(p)[0].click())}}),n(e).on("resize",function(){if(i.thisId){var e=o.getThis(i.thisId);if(e)return!(!e.elem||!n(s)[0])&&void e.position()}})},i.render=function(e){var t=new w(e);return o.call(t)},i.parse=function(e,t,n){return e=e||{},"string"==typeof t&&(t=o.formatArr(t)),t=(t||[]).concat(),lay.each(t,function(a,r){/yyyy|y/.test(r)?t[a]=lay.digit(e.year,r.length):/MM|M/.test(r)?t[a]=lay.digit(e.month+(n||0),r.length):/dd|d/.test(r)?t[a]=lay.digit(e.date,r.length):/HH|H/.test(r)?t[a]=lay.digit(e.hours,r.length):/mm|m/.test(r)?t[a]=lay.digit(e.minutes,r.length):/ss|s/.test(r)&&(t[a]=lay.digit(e.seconds,r.length))}),t.join("")},i.getEndDate=function(e,t){var n=new Date;return n.setFullYear(t||n.getFullYear(),e||n.getMonth()+1,1),new Date(n.getTime()-864e5).getDate()},n?(i.ready(),layui.define("lay",function(e){i.path=layui.cache.dir,a.run(lay),e(l,i)})):"function"==typeof define&&define.amd?define(function(){return a.run(lay),i}):function(){i.ready(),a.run(e.lay),e.laydate=i}()}(window,window.document);