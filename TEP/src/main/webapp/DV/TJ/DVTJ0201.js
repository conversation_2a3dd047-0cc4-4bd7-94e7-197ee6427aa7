var startDate;
var endDate;

$(function(){

    $(window).on('load', function(){
        var startDateStr = IPLAT.getParameterByName("startDate") || "";
        var endDateStr   = IPLAT.getParameterByName("endDate")   || "";
        var countListStr = IPLAT.getParameterByName("countList");

        function fmt(dStr) {
            if (!dStr || dStr === "") return "";
            var d = new Date(dStr);
            if (isNaN(d)) return "";
            var y = d.getFullYear();
            var m = String(d.getMonth() + 1).padStart(2, '0');
            var dd = String(d.getDate()).padStart(2, '0');
            return y + '/' + m + '/' + dd;
        }
        var fs = fmt(startDateStr), fe = fmt(endDateStr);
        var titleText = "服务热线统计表";
        if (fs !== "" || fe !== "") {
            titleText += "  " + fs + "-" + fe;
        }
        $('#timeRange').text(titleText);

        // 填充数据
        var dataRow = [];
        if (countListStr) {
            try {
                var list = JSON.parse(decodeURIComponent(countListStr));
                if (Array.isArray(list) && list.length > 0 && Array.isArray(list[0])) {
                    dataRow = list[0];
                }
            } catch (err) {
                console.warn("解析 countList 失败，使用空数组代替", err);
            }
        }

        var COLS = 10;
        var $tbody = $('.fault-table tbody').empty();

        // 构造 HTML
        var html = '<tr><td>数量</td>';
        for (var i = 0; i < COLS; i++) {
            var v = dataRow[i];
            // 只要是 null/undefined/"" 都当 0 处理；否则保留原值
            if (v === null || v === undefined || v === "") {
                v = 0;
            }
            html += '<td>' + v + '</td>';
        }
        html += '</tr>';
        $tbody.html(html);

        $("#export").on("click", function () {
            window.parent.$("#export").trigger("click");
        });
    });

});