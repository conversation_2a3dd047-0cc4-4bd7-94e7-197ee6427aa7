<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<div class="page-background">
    <EF:EFPage title="设备故障统计" prefix="nocc">
        <style>
            body, #main-container {
                overflow: hidden !important;
            }

            .sbbody{
                padding: 0 63px;
            }

            .titleBg{
                width: 100%;
                height: 60px;
                background: url(${pageContext.request.contextPath}/iplatui/css/images/selectBox.png);
                background-size: 100% 100%;
                border-width: 0;
                padding: 0;
            }

            .tdt{
                display: flex;
                align-items: flex-start;
            }

            .tdc{
                display: flex;
                align-items: center;
            }

            .input-text{
                width: 60px;
            }

            .ck-one {
                border: 2px solid #00c2ff !important;
                border-radius: 10px 10px 10px 10px !important;
                background: linear-gradient(180deg, rgba(15, 123, 178, .4) 0, rgba(6, 57, 96, .2) 100%);
                background-size: 100% 100%;
                border-block-width: 2px;
                margin-bottom: 8px;
                box-shadow: none;
            }

            .ck-one .ck-one-header {
                display: flex;
                background: linear-gradient(180deg, rgba(0, 178, 255, .49) 0, rgba(0, 178, 255, .49) 81.77%);
                box-shadow: inset 0 0 16px rgba(0, 178, 255, .5);
                border-radius: 8px 10px 0 0;
                background-size: 100% 100%;
                font-weight: 400;
                overflow: visible;
                box-sizing: border-box;
                border-bottom: 2px solid #00c2ff;
                height: 34px;

                font-size: 18px;
                padding: 8px 12px 4px 16px;
                line-height: 28px;
                color: #4ccde5;
                letter-spacing: 0;
            }

            .ck-block{
                transition: opacity .2s ease-out;
            }

            .ck-block:after, .ck-block:before {
                content: " ";
                display: table;
            }

            .ck-title {
                display: block;
                position: relative;
                width: fit-content;
                color: #fff;
                z-index: 0;
                line-height: 18px;
            }

            .ck-one .ck-one-header .ck-title::before {
                content: ' ';
                width: calc(100% + 40px);
                height: 36px;
                left: -18px;
                top: -10px;
                position: absolute;
                border-radius: 10px 0 20px 0;
                background: linear-gradient(180deg, rgba(64, 186, 255, 0) 5.73%, rgba(0, 163, 255, .49) 78.13%);
                border: 2px solid #88d3ec;
                box-shadow: inset 0 -3px 12px 0 rgba(0, 255, 240, .48);
                z-index: -1;
            }

            .ck-one .ck-one-header .ck-title::after {
                content: ' ';
                width: calc(100% + 55px);
                height: 36px;
                left: -18px;
                top: -10px;
                position: absolute;
                border-radius: 10px 0 20px 0;
                background: linear-gradient(180deg, rgba(64, 186, 255, 0) 5.73%, rgba(64, 186, 255, .49) 78.13%);
                border: 2px solid rgba(0, 255, 240, .48);
                box-shadow: inset 0 -3px 12px 0 rgba(0, 255, 240, .48);
                z-index: -2;
            }

            .ck-one .ck-content {
                padding-left: 15px;
                padding-right: 15px;
                padding-top: 8px;
                padding-bottom: 0;
                overflow-x: auto;
                transition: opacity .2s ease-out;
                max-width: 100%;
            }

            .countShow-img{
                cursor: pointer;
                background: url(${pageContext.request.contextPath}/iplatui/css/images/icon-countShow.svg) no-repeat center center;
                background-size: 100% 100%;
            }

            .export-img{
                cursor: pointer;
                background: url(${pageContext.request.contextPath}/iplatui/css/images/icon-export.png) no-repeat center center;
                background-size: 100% 100%;
            }
        </style>
        <div class="row">
            <div class="row">
                <div class="page-title">设备故障统计</div>
            </div>
            <div class="sbbody">
                <div class="titleBg tdt">
                    <div class="col-md-12 tdc" style="height: 50px;">
                        <div class="col-md-4">
                            <EF:EFDatePicker ename="startDate" cname="开始日期" ratio="4:8"  format="yyyy-MM-dd" colWidth="6"/>
                            <EF:EFDatePicker ename="endDate" cname="结束日期" ratio="4:8"  format="yyyy-MM-dd" colWidth="6"/>
                        </div>
                        <div class="tdc" style="width: 190px;">
                            <div class="input-text">线别&ensp;</div>
                            <EF:EFSelect textField="lineName"  optionLabel="请选择"  valueField="lineNumber"
                                         serviceName="DVTJ01" methodName="lineInit"
                                         resultId="line"
                                    ename="line" inline="true" ratio="0:12" style="width:110px;">
                            </EF:EFSelect>
                        </div>
                        <div class="tdc" style="width: 190px;">
                            <div class="input-text">类别&ensp;</div>
                            <EF:EFSelect textField="typeName"  optionLabel="请选择"  valueField="typeNumber"
                                         serviceName="DVTJ01" methodName="typeInit"
                                         resultId="type"
                                         ename="type" inline="true" ratio="0:12" style="width:110px;">
                            </EF:EFSelect>
                        </div>
                        <div style="margin-left: 590px;">
                            <EF:EFButton  ename="QUERY2" cname="查询" style="margin-right: 16px;"></EF:EFButton>
                        </div>
                    </div>
                </div>

                <div id="guzhagn" data-title="故障详情" class="ck-one block nav-region">
                    <div class="ck-block ck-one-header">
                        <span class="ck-title">故障详情</span>
                    </div>

                    <div class="ck-content ck-horizontal">
                        <div  style="width: 100%;display: flex;justify-content: flex-end;">
                            <div id="countShow" class="countShow-img" style="width: 30px;height: 30px;margin: 0 7.5px;"></div>
                            <div id="export" class="export-img" style="width: 30px;height: 30px;margin: 0 7.5px;"></div>
                        </div>
                        <EF:EFRegion head = "hidden">
                            <EF:EFGrid blockId="result" autoDraw="no" height="620"
                                       sort="setted" pagerPosition="bottom"  enable="hidden"
                                       toolbarConfig="{hidden:'all'}">
                                <EF:EFColumn ename="index" cname="序号" width="30" align="center" enable="false"/>
                                <EF:EFColumn ename="lineName" cname="线别" width="60" align="center" enable="false"/>
                                <EF:EFColumn ename="typeName" cname="类别" width="60" align="center" enable="false"/>
                                <EF:EFColumn ename="date" cname="日期" width="60" align="center" enable="false"/>
                                <EF:EFColumn ename="desc" cname="概况" width="150" align="center" enable="false"  style="white-space: normal;"/>

                            </EF:EFGrid>
                        </EF:EFRegion>
                    </div>
                    <div style="height: 10px;"></div>
                </div>
            </div>
            <EF:EFWindow id="DVTJ0101" url="${ctx}/web/DVTJ0101" refresh="true" lazyload="true" height="40%" width="70%"
                         title="设备故障统计表"/>
        </div>
    </EF:EFPage>
</div>