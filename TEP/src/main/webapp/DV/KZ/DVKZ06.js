$(function (){
    let videoSrcArr = ["http://10.124.87.201:80/ossrest/api/object/DPBJ/20240730确认版本/客流指标1/枫叶下落2.mov?tenant=1",
    "http://10.124.87.201:80/ossrest/api/object/DPBJ/20240730确认版本/客流指标1/种子.mp4?tenant=1",
    "http://10.124.87.201:80/ossrest/api/object/DPBJ/20240730确认版本/客流指标1/flower.webm?tenant=1"];
    let videoBoxArr = [];
    let selectedItemIndex = 0;
    let selectedModel = 1001;

    window.onload = function (){
        console.log("load");

        //生成各个模式的dom元素
        $("#model1001Div").html(kendo.template($("#model1001Videos").html()));
        $("#model1002Div2").html(kendo.template($("#model1002Videos").html()));
        $("#model1003Div2").html(kendo.template($("#model1003Videos").html()));
        $("#model1004Div2").html(kendo.template($("#model1004Videos").html()));
        $("#model1005Div2").html(kendo.template($("#model1005Videos").html()));
        //初始化video_box框, 并选中第一个video为空的box(红边框)
        initVideoBoxArr();

        //绑定点击选中事件：选中变红框,其余边框颜色初始化
        $(".video_item").on("click", function (event){
            initVideoBoxBorderColor();
            //选中后边框变红
            let eventTarget = event.target;
            eventTarget.parentElement.style.borderColor = '#821524';
            //记录选中index
            console.log(eventTarget.id.charAt( eventTarget.id.length-1));
            selectedItemIndex = eventTarget.id.charAt( eventTarget.id.length-1);
        })

    };

    /**
     *  初始化video_box框, 并选中第一个video为空的box(红边框)
     */
    function initVideoBoxArr(){
        let allVideoBoxArr = document.getElementsByClassName("video_box");
        videoBoxArr = [];
        for (let i=0; i<allVideoBoxArr.length; i++){
            if (Number(allVideoBoxArr[i].id.substr(0, 4)) === selectedModel){
                videoBoxArr.push(allVideoBoxArr[i]);
            }
        }
        for (let i=0; i<videoBoxArr.length; i++){
            if (videoBoxArr[i].children[0].currentSrc===""){
                selectedItemIndex = i;
                videoBoxArr[i].style.borderColor = '#821524';
                break;
            }
        }
    }

    /**
     * 将所有的video_box边框颜色初始化
     */
    function initVideoBoxBorderColor(){
        for (let i=0; i<videoBoxArr.length; i++){
            videoBoxArr[i].style.borderColor = '#0481b2';
        }
    }

    let videoSrcIndex = 0;
    /**
     * 插入监控链接，并选中下一个未有cctv监控链接的box
     */
    $("#changeVideoBox").on("click", function (event){
        if (videoBoxArr.length <= 0){
            return;
        }
        //插入连接
        let videoItem = document.getElementById(selectedModel + "video"+selectedItemIndex);
        videoSrcIndex++;
        if (videoSrcIndex >= videoSrcArr.length){
            videoSrcIndex = 0;
        }
        videoItem.src = videoSrcArr[videoSrcIndex];


        //下一个未有cctv监控链接的box边框变红,其余初始化
        initVideoBoxBorderColor();
        selectedItemIndex++;
        if (selectedItemIndex >= videoBoxArr.length){
            selectedItemIndex = 0;
        }else {
            for (let i=selectedItemIndex ;i<videoBoxArr.length; i++){
                if (videoBoxArr[i].children[0].currentSrc===""){
                    selectedItemIndex = i;
                    break;
                }
            }
        }
        videoBoxArr[selectedItemIndex].style.borderColor = '#821524';
    });



    IPLATUI.EFCascadeSelect = {
        "model": {
            change: function (e){
                //先隐藏之前的模式
                let originPage = document.getElementById("model"+ selectedModel + "Div");
                if (originPage!==undefined || true){
                    originPage.style.display = 'none';
                }
                //展示新的模式框,并将所有框初始化，随后红框标记
                selectedModel = Number(e.sender._old);
                let nowPage = document.getElementById("model"+ selectedModel + "Div");
                nowPage.style.display = 'block';
                initVideoBoxBorderColor();
                initVideoBoxArr();
                console.log(e);

            }
        },
    };

})