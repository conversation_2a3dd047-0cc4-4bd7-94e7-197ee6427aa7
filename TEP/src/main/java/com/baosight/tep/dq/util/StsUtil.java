/**
 * @authoer:<PERSON><PERSON><PERSON><PERSON>
 * @createDate:2023/2/9 18:38
 */
package com.baosight.tep.dq.util;

import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.Base64;

/**
 * sts工具
 *
 * <AUTHOR>
 * @date 2023/03/24
 */
public class StsUtil {
    private static final String KEY_ERRCODE = "errcode";
    private static final String KEY_ERRINFO = "errinfo";
    private static final String KEY_RECORDS = "records";

    /**
     * sts查询
     *
     * @param inInfo 在信息
     * @return {@link EiInfo}
     */
    public static EiInfo stsQuery(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost("http://" + inInfo.getString("serverIP") + "/stsrest/api/query");
            httpPost.addHeader("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString((inInfo.getString("userName") + ":" + inInfo.getString("passWord")).getBytes()));
            httpPost.addHeader("Content-Type", "application/json");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sql", inInfo.getString("sql"));
            jsonObject.put("limit", inInfo.getInt("limit"));
            httpPost.setEntity(new StringEntity(jsonObject.toString()));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String responseContent = EntityUtils.toString(entity, "UTF-8");
            response.close();
            httpClient.close();
            JSONObject responseJson = JSONObject.parseObject(responseContent);
            if (responseJson.containsKey(KEY_ERRINFO)) {
                throw new PlatException(responseJson.get(KEY_ERRINFO).toString());
            }
            if (responseJson.containsKey(KEY_RECORDS)) {
                outInfo.set("result", responseJson.getJSONObject(KEY_RECORDS).get("data"));
            }
        } catch (Exception exception) {
            throw new PlatException(exception);
        }
        return outInfo;
    }
}