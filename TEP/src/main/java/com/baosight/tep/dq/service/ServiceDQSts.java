/**
 * @authoer:YangZ<PERSON>g
 * @createDate:2023/2/9 18:38
 */
package com.baosight.tep.dq.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.EiInfoUtil;
import com.baosight.tep.common.util.JsonUtil;
import com.baosight.tep.dq.domain.DataQueryConfig;
import com.baosight.tep.dq.util.SqlBuildUtil;
import com.baosight.tep.dq.util.StsUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ServiceDQSts extends ServiceBase {

    private static final String SERVERIP = "*************";
    private static final String USERNAME = "admin@irailmetro";
    private static final String PASSWORD = "admin";

    /**
     * 查询指标sts
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo queryTargetSts(EiInfo inInfo) {
        DataQueryConfig params = Convert.convert(DataQueryConfig.class, inInfo.get("params"));
        EiInfo outInfo = StsUtil.stsQuery(EiInfoUtil.builder()
                .set("sql", SqlBuildUtil.buildSql(SqlBuildUtil.TYPE_STS, params))
                .set("limit", 99999999)
                .set("serverIP", SERVERIP)
                .set("userName", USERNAME)
                .set("passWord", PASSWORD).build());
        List<Map<String, String>> list = (List) outInfo.get("result");
        //数据过滤
        List<Map<String, String>> filterList = dataFilter(list, params);
        //数据处理
        List<Map<String, String>> result = filterList.parallelStream()
                .peek(o -> o.remove("start_date_time"))
                .peek(o -> o.remove("end_date_time"))
                .peek(o -> o.put("target_name", params.getTargetName()))
                .peek(o -> o.put("time_interval", params.getTimeIntervalName()))
                .collect(Collectors.toList());

        outInfo.set("result", JsonUtil.convertUnderlineToCamelCase(JSONUtil.toJsonStr(result)));
        return outInfo;
    }


    /**
     * 数据过滤
     * 根据开始时间-结束时间过滤数据
     *
     * @param list   列表
     * @param params 参数
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    private List<Map<String, String>> dataFilter(List<Map<String, String>> list, DataQueryConfig params) {
        return list.stream()
                .filter(o -> {
                    String startDateTime = DateUtil.format(DateUtil.parse(params.getDataQuery().getStartDate()), "yyyy-MM-dd") + " " + params.getDataQuery().getStartTime();
                    return o.get("start_time").compareTo(startDateTime) >= 0;
//                    return o.get("start_date_time").compareTo(startDateTime) >= 0;
                })
                .filter(o -> {
                    String endDateTime = DateUtil.format(DateUtil.parse(params.getDataQuery().getEndDate()), "yyyy-MM-dd") + " " + params.getDataQuery().getEndTime();
                    return o.get("end_time").compareTo(endDateTime) <= 0;
//                    return o.get("end_date_time").compareTo(endDateTime) <= 0;
                }).collect(Collectors.toList());
    }
}
