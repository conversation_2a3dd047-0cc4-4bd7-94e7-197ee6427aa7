package com.baosight.tep.dq.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * 指标树节点
 *
 * <AUTHOR>
 * @date 2023/02/07
 */
@Getter
@Setter
public class TargetTreeNode {
    /**
     * 指标节点
     */
    private String targetNode;
    /**
     * 指标类型（客流。。。）
     */
    private String targetType;
    /**
     * 指标分类（）
     */
    private String targetClass;
    /**
     * 指标级别
     */
    private String targetGrade;
    /**
     * 指标代码
     */
    private String targetCode;

    public TargetTreeNode(String targetNode) {
        this.targetNode = targetNode;
        initTargetNode();
    }

    public void initTargetNode() {
        String[] arr = targetNode.split("_");
        if (arr.length == 5) {
            this.targetType = arr[1];
            this.targetClass = arr[2];
            this.targetGrade = arr[3];
            this.targetCode = arr[4];
        }
    }

    /**
     * 创建TargetTreeNode
     *
     * @param targetNode 指标节点
     * @return {@link TargetTreeNode}
     */
    public static TargetTreeNode create(String targetNode) {
        return new TargetTreeNode(targetNode);
    }


}
