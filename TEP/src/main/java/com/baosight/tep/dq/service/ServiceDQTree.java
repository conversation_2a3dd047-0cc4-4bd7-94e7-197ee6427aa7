package com.baosight.tep.dq.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.EiInfoUtils;
import com.baosight.tep.common.util.GeneralUtil;
import com.baosight.tep.common.util.ListBuilder;
import com.baosight.tep.dq.domain.TreeConfig;
import com.baosight.tep.dq.domain.TreeSpace;
import com.baosight.tep.dq.util.BaseDataUtil;
import com.baosight.tep.dq.util.TimeGradeEnum;
import com.baosight.tep.dq.util.TreeUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date 2022/12/29
 */

public class ServiceDQTree extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    /**
     * 创建树
     *
     * @param inInfo EiInfo
     * @return {@link EiInfo}
     */
    public EiInfo createTree(EiInfo inInfo) {
        EiInfo outInfo;
        dao.delete("DQTree.deleteTreeConfig", inInfo.getAttr());
        if (inInfo.getInt("type") == 1) {
            dao.insert("DQTree.insertTreeConfig", TreeUtil.createTargetNode());
            outInfo = createTargetTree();
        } else if (inInfo.getInt("type") == 2) {
            dao.insert("DQTree.insertTreeConfigs", TreeUtil.createSpaceTreeConfigs());
            outInfo = createSpaceTree();
        } else if (inInfo.getInt("type") == 3) {
            dao.insert("DQTree.insertTreeConfig", TreeUtil.createTimeNode());
            outInfo = createTimeTree();
        } else {
            outInfo = GeneralUtil.createEiInfo().putStatus(-1).putMsg("未知类型，维度树构建失败！");
        }
        return outInfo;
    }

    /**
     * 创建时间树
     *
     * @return {@link EiInfo}
     */
    public EiInfo createTimeTree() {
        List<Map<String, Object>> hList = dao.query("DQTree.queryTimeInterval", null);
        List<Map<String, Object>> rList = dao.query("DQTree.queryInterval", null);
        List<Map<String, Object>> result = ListBuilder
                .create(new ArrayList<Map<String, Object>>())
                .addAll(hList)
                .addAll(rList)
                .build();
        List<TreeConfig> treeConfigs = result.stream()
                .map(o -> setTimeTreeConfig(o.get("type").toString(), o.get("name").toString()))
                .collect(Collectors.toList());
        dao.insert("DQTree.insertTreeConfigs", treeConfigs);

        return GeneralUtil.createEiInfo().putMsg("时间树创建成功！");
    }

	/**
	 * 创建时间树(新)
	 *
	 * @return {@link EiInfo}
	 */
	public EiInfo newCreateTimeTree() {
		//调用时间颗粒度接口
		EiInfo eiInfo = BaseDataUtil.queryTime(new EiInfo());
		if (eiInfo.getStatus() == -1) {
			throw new PlatException("时间颗粒度接口数据获取失败");
		}
		EiBlock blockData = eiInfo.getBlock("result");
		List<Map<String, Object>> timeRows = blockData.getRows();
		List<Map<String, Object>> timeList = new ArrayList<>();
		//存放所需要的时间颗粒度,从基础数据接口拿可能有多余的颗粒度,用默认值来过滤
		ArrayList timeArray = new ArrayList();
		timeArray.addAll(Arrays.asList("5分钟","15分钟","30分钟","1小时","日","周","月","季度","年"));
		for (int i = 0; i < timeRows.size(); i++) {
			String name = (String) timeRows.get(i).get("fd_cname");
			if (timeArray.indexOf(name) > -1) {
				Map map  = new HashMap();
				map.put("type", timeRows.get(i).get("fd_type"));
				map.put("name", name);
				timeList.add(map);
			}
		}
		List<Map<String, Object>> result = ListBuilder
				.create(new ArrayList<Map<String, Object>>())
				.addAll(timeList)
				.build();
		List<TreeConfig> treeConfigs = result.stream()
				.map(o -> setTimeTreeConfig(o.get("type").toString(), o.get("name").toString()))
				.collect(Collectors.toList());
		//创建目录并加到treeConfigs中
		TreeConfig treeConfig = TreeUtil.createRootMenu(TreeUtil.NODE_TIME_TEXT, TreeUtil.NODE_TIME_ROOT, TreeUtil.TIME_TYPE,TreeUtil.NODE_ROOT);
		treeConfigs.add(treeConfig);
		dao.insert("DQTree.insertTreeConfigs", treeConfigs);

		return GeneralUtil.createEiInfo().putMsg("时间树创建成功！");
	}

    /**
     * 设置时间树配置
     *
     * @param type 类型
     * @param name 名字
     * @return {@link TreeConfig}
     */
    private TreeConfig setTimeTreeConfig(String type, String name) {
        return TreeUtil.create(TreeUtil.LEAF_NODE)
                .setTreeClass(TreeUtil.TIME_TYPE)
                .setNode("time_" + TimeGradeEnum.getDataSourceByTimeGrade(type) + "_" + type)
                .setNodeText(name)
                .setParent(TreeUtil.NODE_TIME_ROOT)
                .setParentText(TreeUtil.NODE_TIME_TEXT)
                .setNodeLevel(TreeUtil.NODE_LEVEL_TWO)
                .setNodeOrder(TimeGradeEnum.getOrderByTimeGrade(type));
    }

    /**
     * 创建空间树
     *
     * @return {@link EiInfo}
     */
    public EiInfo createSpaceTree() {
        JSONArray baseData = GeneralUtil.createData(GeneralUtil.COMMAND_STATION);
        List<TreeSpace> lines = JSONUtil.toList(baseData, TreeSpace.class);

        // 一.1.生成三级节点（线路）-叶子节点
        List<TreeConfig> treeConfigs = lines.stream()
                .map(o -> TreeUtil.create(TreeUtil.LEAF_NODE)
                        .setTreeClass(TreeUtil.SPACE_TYPE)
                        .setNode(TreeUtil.NODE_SPACE_ONE + "_" + o.getLineNumber())
                        .setNodeText(o.getName())
                        .setParent(TreeUtil.NODE_SPACE_ONE)
                        .setParentText(TreeUtil.NODE_SPACE_LINE)
                        .setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
                        .setNodeOrder(o.getOrder())
                        .setIsDisabled(Convert.toBool(o.getOnline_t()) ? TreeUtil.STATUS_TRUE : TreeUtil.STATUS_FALSE)
                        .setIsSpread(TreeUtil.STATUS_FALSE)
                ).collect(Collectors.toList());

        // 一.2.生成三级节点（线网）-叶子节点
        treeConfigs.add(TreeUtil.createSpaceNodeNet().setNodeOrder(0));

        // 二.1.生成三级节点（线路.车站）-树节点
        treeConfigs.addAll(lines.stream()
                .map(o -> TreeUtil.create(TreeUtil.TREE_NODE)
                        .setTreeClass(TreeUtil.SPACE_TYPE)
                        .setNode(TreeUtil.NODE_SPACE_TWO + "_" + o.getLineNumber())
                        .setNodeText(o.getName())
                        .setParent(TreeUtil.NODE_SPACE_TWO)
                        .setParentText(TreeUtil.NODE_SPACE_STATION)
                        .setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
                        .setNodeOrder(o.getOrder())
                        .setIsDisabled(Convert.toBool(o.getOnline_t()) ? TreeUtil.STATUS_TRUE : TreeUtil.STATUS_FALSE)
                        .setIsSpread(TreeUtil.STATUS_FALSE)
                ).collect(Collectors.toList())
        );

        // 二.2.生成四级节点（车站）-叶子节点
        for (TreeSpace line : lines) {
            JSONArray jsonArray = line.getStationData();
            List<TreeSpace> list = JSONUtil.toList(jsonArray, TreeSpace.class);
            treeConfigs.addAll(list.stream()
                    .map(o -> TreeUtil.create(TreeUtil.LEAF_NODE)
                            .setTreeClass(TreeUtil.SPACE_TYPE)
                            .setNode(TreeUtil.NODE_SPACE_TWO + "_" + o.getLine_number() + "_" + o.getStationNumber())
                            .setNodeText(o.getName())
                            .setParent(TreeUtil.NODE_SPACE_TWO + "_" + o.getLine_number())
                            .setParentText(TreeUtil.NODE_SPACE_STATION)
                            .setNodeLevel(TreeUtil.NODE_LEVEL_FOUR)
                            .setNodeOrder(o.getOrder())
                            .setIsDisabled(Convert.toBool(o.getOnline_t()) ? TreeUtil.STATUS_TRUE : TreeUtil.STATUS_FALSE)
                            .setIsSpread(TreeUtil.STATUS_FALSE)
                    ).collect(Collectors.toList())
            );
        }

        // 三.1.生成三级节点（线路.换乘站）-树节点

        // 三.2.生成四级节点（换乘站）-叶子节点

        // 四.1.生成三级节点（线路.区间）-树节点
        treeConfigs.addAll(lines.stream()
                .map(o -> TreeUtil.create(TreeUtil.TREE_NODE)
                        .setTreeClass(TreeUtil.SPACE_TYPE)
                        .setNode(TreeUtil.NODE_SPACE_FOUR + "_" + o.getLineNumber())
                        .setNodeText(o.getName())
                        .setParent(TreeUtil.NODE_SPACE_FOUR)
                        .setParentText(TreeUtil.NODE_SPACE_SECTION)
                        .setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
                        .setNodeOrder(o.getOrder())
                        .setIsDisabled(Convert.toBool(o.getOnline_t()) ? TreeUtil.STATUS_TRUE : TreeUtil.STATUS_FALSE)
                        .setIsSpread(TreeUtil.STATUS_FALSE)
                ).collect(Collectors.toList())
        );

        // 四.2.生成四级节点（区间）-叶子节点
        for (TreeSpace line : lines) {
            JSONArray jsonArrayNew = new JSONArray();
            JSONArray jsonArray = line.getStationData();
            for (int j = 0; j < jsonArray.size(); j++) {
                JSONObject jsonObject = new JSONObject();
                if (j < jsonArray.size() - 1) {
                    JSONObject jsonObjectCurr = jsonArray.getJSONObject(j);
                    JSONObject jsonObjectNext = jsonArray.getJSONObject(j + 1);
                    jsonObject.set("stationNumber", jsonObjectCurr.getStr("stationNumber") + "_" + jsonObjectNext.getStr("stationNumber"));
                    jsonObject.set("name", jsonObjectCurr.getStr("name") + "->" + jsonObjectNext.getStr("name"));
                    jsonObject.set("line_number", jsonObjectCurr.getInt("line_number"));
                    jsonObject.set("online_t", jsonObjectCurr.getInt("online_t"));
                    jsonObject.set("order", jsonObjectCurr.getInt("order"));
                    jsonArrayNew.add(jsonObject);
                }
            }
            for (int k = jsonArray.size() - 1; k > 0; k--) {
                JSONObject jsonObject = new JSONObject();
                JSONObject jsonObjectCurr = jsonArray.getJSONObject(k);
                JSONObject jsonObjectPre = jsonArray.getJSONObject(k - 1);
                jsonObject.set("stationNumber", jsonObjectCurr.getStr("stationNumber") + "_" + jsonObjectPre.getStr("stationNumber"));
                jsonObject.set("name", jsonObjectCurr.getStr("name") + "->" + jsonObjectPre.getStr("name"));
                jsonObject.set("line_number", jsonObjectCurr.getInt("line_number"));
                jsonObject.set("online_t", jsonObjectCurr.getInt("online_t"));
                jsonObject.set("order", jsonObjectCurr.getInt("order") + jsonArray.size());
                jsonArrayNew.add(jsonObject);
            }
            List<TreeSpace> list = JSONUtil.toList(jsonArrayNew, TreeSpace.class);
            treeConfigs.addAll(list.stream()
                    .map(o -> TreeUtil.create(TreeUtil.LEAF_NODE)
                            .setTreeClass(TreeUtil.SPACE_TYPE)
                            .setNode(TreeUtil.NODE_SPACE_FOUR + "_" + o.getLine_number() + "_" + o.getStationNumber())
                            .setNodeText(o.getName())
                            .setParent(TreeUtil.NODE_SPACE_FOUR + "_" + o.getLine_number())
                            .setParentText(TreeUtil.NODE_SPACE_STATION)
                            .setNodeLevel(TreeUtil.NODE_LEVEL_FOUR)
                            .setNodeOrder(o.getOrder())
                            .setIsDisabled(Convert.toBool(o.getOnline_t()) ? TreeUtil.STATUS_TRUE : TreeUtil.STATUS_FALSE)
                            .setIsSpread(TreeUtil.STATUS_FALSE)
                    ).collect(Collectors.toList())
            );
        }

        dao.insert("DQTree.insertTreeConfigs", treeConfigs);

        return GeneralUtil.createEiInfo().putMsg("空间树创建成功！");
    }

	/**
	 * 创建空间维度树(新)
	 * @return
	 */
	public EiInfo newCreateSpaceTree() {
		//1、创建线路目录和子节点
		createLineMenu();
		//2、创建车站目录和子节点
		createStationMenu();
		//3、创建区间目录和子节点
		createStationIntervalMenu();
		return GeneralUtil.createEiInfo().putMsg("空间树创建成功！");
	}

	/**
	 * 创建线路目录和子节点
	 * @return
	 */
	public EiInfo createLineMenu() {
		//获取线路基础数据
		EiInfo eiInfo = BaseDataUtil.queryLine(new EiInfo());
		if (eiInfo.getStatus() == -1) {
			throw new PlatException("线路基础数据获取失败");
		}
		List<Map<String,Object>> lineList = new ArrayList<>();
		List<Map<String,Object>> getLineList = eiInfo.getBlock("result").getRows();
		for (Map<String, Object> map : getLineList) {
			Map lineMap = new HashMap();
			lineMap.put("lineId", map.get("line_id"));
			lineMap.put("name", map.get("line_cname"));
			lineList.add(lineMap);
		}
		List<TreeConfig> treeConfigs = lineList.stream()
				.map(o -> TreeUtil.create(TreeUtil.LEAF_NODE)
						.setTreeClass(TreeUtil.SPACE_TYPE)
						.setNode(TreeUtil.NODE_SPACE_ONE + "_" + o.get("lineId"))
						.setNodeText(o.get("name").toString())
						.setParent(TreeUtil.NODE_SPACE_ONE)
						.setParentText(TreeUtil.NODE_SPACE_LINE)
						.setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
						.setNodeOrder(0)
						.setNodeLeaf(1)
						.setIsDisabled(TreeUtil.STATUS_FALSE)
						.setIsSpread(TreeUtil.STATUS_FALSE)
				).collect(Collectors.toList());
		//加上线网子节点(第3层)
		treeConfigs.add(TreeUtil.createSpaceNodeNet().setNodeOrder(0));
		//加入线路目录(第2层)
		TreeConfig rootMenu = TreeUtil.createRootMenu(TreeUtil.NODE_SPACE_LINE, TreeUtil.NODE_SPACE_ONE, TreeUtil.SPACE_TYPE,TreeUtil.NODE_SPACE_ROOT);
		treeConfigs.add(rootMenu);
		dao.insert("DQTree.insertTreeConfigs", treeConfigs);
		return GeneralUtil.createEiInfo().putMsg("空间树的线路目录创建成功！");
	}

	/**
	 * 创建车站目录和子节点
	 * @return
	 */
	public EiInfo createStationMenu() {
		//获取车站基础数据
		EiInfo eiInfo = BaseDataUtil.queryStation(new EiInfo());
		if (eiInfo.getStatus() == -1) {
			throw new PlatException("线路车站数据获取失败");
		}
		//一个存放线路,一个存放车站,用来创建线路目录和车站节点
		List<Map<String,Object>> lineList = new ArrayList<>();
		List<Map<String,Object>> stationList = new ArrayList<>();
		List<Map<String,Object>> getStationList = eiInfo.getBlock("result").getRows();
		//过滤未启用的车站以及只获取车站id、车站名、线路id、线路名
		getStationList.stream().filter(item -> "true".equals(item.get("enable_status")))
				.forEach(item -> {
					//存放车站数据
					Map stationMap = new HashMap();
					stationMap.put("lineId", item.get("line_id"));
					stationMap.put("lineName", item.get("line_cname"));
					stationMap.put("stationId", item.get("sta_id"));
					stationMap.put("stationName", item.get("sta_cname"));
					stationList.add(stationMap);
					//存放线路数据
					Map lineMap = new HashMap();
					lineMap.put("lineId", item.get("line_id"));
					lineMap.put("lineName", item.get("line_cname"));
					lineList.add(lineMap);
				});
		List<TreeConfig> treeConfigs = new ArrayList<>();
		//对线路集合进行去重
		List<Map<String, Object>> newLineList = lineList.stream().distinct().collect(Collectors.toList());
		//创建线路目录
		treeConfigs.addAll(newLineList.stream()
				.map(o -> TreeUtil.create(TreeUtil.TREE_NODE)
						.setTreeClass(TreeUtil.SPACE_TYPE)
						.setNode(TreeUtil.NODE_SPACE_TWO + "_" + o.get("lineId"))
						.setNodeText(o.get("lineName").toString())
						.setParent(TreeUtil.NODE_SPACE_TWO)
						.setParentText(TreeUtil.NODE_SPACE_STATION)
						.setIconClass(TreeUtil.NODE_PARENT_ICON)
						.setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
						.setNodeOrder(0)
						.setNodeLeaf(0)
						.setIsDisabled(TreeUtil.STATUS_FALSE)
						.setIsSpread(TreeUtil.STATUS_FALSE)
				).collect(Collectors.toList())
		);
		//创建车站子节点
		treeConfigs.addAll(stationList.stream()
				.map(o -> TreeUtil.create(TreeUtil.LEAF_NODE)
						.setTreeClass(TreeUtil.SPACE_TYPE)
						.setNode(TreeUtil.NODE_SPACE_TWO + "_" + o.get("lineId") + "_" + o.get("stationId"))
						.setNodeText(o.get("stationName").toString())
						.setParent(TreeUtil.NODE_SPACE_TWO + "_" + o.get("lineId"))
						.setParentText(TreeUtil.NODE_SPACE_STATION)
						.setIconClass(TreeUtil.NODE_ICON)
						.setNodeLevel(TreeUtil.NODE_LEVEL_FOUR)
						.setNodeOrder(1)
						.setNodeLeaf(1)
						.setIsDisabled(TreeUtil.STATUS_TRUE)
						.setIsSpread(TreeUtil.STATUS_FALSE)
				).collect(Collectors.toList())
		);

		//加入车站目录(第2层)
		TreeConfig rootMenu = TreeUtil.createRootMenu(TreeUtil.NODE_SPACE_STATION, TreeUtil.NODE_SPACE_TWO, TreeUtil.SPACE_TYPE,TreeUtil.NODE_SPACE_ROOT);
		treeConfigs.add(rootMenu);
		dao.insert("DQTree.insertTreeConfigs", treeConfigs);
		return GeneralUtil.createEiInfo().putMsg("空间树的车站目录创建成功！");
	}

	/**
	 * 创建车站区间目录和子节点
	 * @return
	 */
	public EiInfo createStationIntervalMenu() {
		//获取车站区间基础数据
		EiInfo eiInfo = BaseDataUtil.queryStationInterval(new EiInfo());
		if (eiInfo.getStatus() == -1) {
			throw new PlatException("线路车站区间数据获取失败");
		}
		//一个存放线路,一个存放车站区间,用来创建线路目录和车站节点
		List<Map<String,Object>> lineList = new ArrayList<>();
		List<Map<String,Object>> stationIntervalList = new ArrayList<>();
		List<Map<String,Object>> getStationIntervalList = eiInfo.getBlock("result").getRows();
		//过滤上行的车站区间以及只获取开始、结束车站id、开始、结束车站名、线路id、线路名
		getStationIntervalList.stream().filter(item -> "UP".equals(item.get("direction")))
				.forEach(item -> {
					//存放车站区间数据
					Map stationMap = new HashMap();
					stationMap.put("lineId", item.get("line_id"));
					stationMap.put("lineName", item.get("line_cname"));
					stationMap.put("startStationId", item.get("start_sta_id"));
					stationMap.put("startStationName", item.get("start_sta_cname"));
					stationMap.put("endStationId", item.get("end_sta_id"));
					stationMap.put("endStationName", item.get("end_sta_cname"));
					stationIntervalList.add(stationMap);
					//存放线路数据
					Map lineMap = new HashMap();
					lineMap.put("lineId", item.get("line_id"));
					lineMap.put("lineName", item.get("line_cname"));
					lineList.add(lineMap);
				});
		List<TreeConfig> treeConfigs = new ArrayList<>();
		//对线路集合进行去重
		List<Map<String, Object>> newLineList = lineList.stream().distinct().collect(Collectors.toList());
		//创建线路目录
		treeConfigs.addAll(newLineList.stream()
				.map(o -> TreeUtil.create(TreeUtil.TREE_NODE)
						.setTreeClass(TreeUtil.SPACE_TYPE)
						.setNode(TreeUtil.NODE_SPACE_FOUR + "_" + o.get("lineId"))
						.setNodeText(o.get("lineName").toString())
						.setParent(TreeUtil.NODE_SPACE_FOUR)
						.setParentText(TreeUtil.NODE_SPACE_STATION)
						.setIconClass(TreeUtil.NODE_PARENT_ICON)
						.setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
						.setNodeOrder(0)
						.setNodeLeaf(0)
						.setIsDisabled(TreeUtil.STATUS_FALSE)
						.setIsSpread(TreeUtil.STATUS_FALSE)
				).collect(Collectors.toList())
		);
		//创建车站区间子节点
		treeConfigs.addAll(stationIntervalList.stream()
				.map(o -> TreeUtil.create(TreeUtil.LEAF_NODE)
						.setTreeClass(TreeUtil.SPACE_TYPE)
						.setNode(TreeUtil.NODE_SPACE_FOUR + "_" + o.get("lineId") + "_" + o.get("startStationId") + "_" + o.get("endStationId"))
						.setNodeText(o.get("startStationName") + "->" + o.get("endStationName") )
						.setParent(TreeUtil.NODE_SPACE_FOUR + "_" + o.get("lineId"))
						.setParentText(TreeUtil.NODE_SPACE_STATION)
						.setNodeLevel(TreeUtil.NODE_LEVEL_FOUR)
						.setNodeOrder(1)
						.setNodeLeaf(1)
						.setIsDisabled(TreeUtil.STATUS_TRUE)
						.setIsSpread(TreeUtil.STATUS_FALSE)
				).collect(Collectors.toList())
		);

		//加入区间目录(第2层)
		TreeConfig intervalMenu = TreeUtil.createRootMenu(TreeUtil.NODE_SPACE_SECTION, TreeUtil.NODE_SPACE_FOUR, TreeUtil.SPACE_TYPE,TreeUtil.NODE_SPACE_ROOT);
		treeConfigs.add(intervalMenu);
		//加入空间维度目录(根目录)
		TreeConfig rootMenu = TreeUtil.createRootMenu(TreeUtil.NODE_SPACE_TEXT, TreeUtil.NODE_SPACE_ROOT, TreeUtil.SPACE_TYPE,TreeUtil.NODE_ROOT);
		treeConfigs.add(rootMenu);
		dao.insert("DQTree.insertTreeConfigs", treeConfigs);
		return GeneralUtil.createEiInfo().putMsg("空间树的车站目录创建成功！");
	}

    /**
     * 创建指标树
     *
     * @return {@link EiInfo}
     */
    public EiInfo createTargetTree() {
        // 1.生成二级节点（指标类型）
        List<Map<String, Object>> typeList = dao.query("DQTree.queryTargetType", null);
        List<TreeConfig> treeConfigs = typeList.stream()
                .map(o -> TreeUtil.create(TreeUtil.TREE_NODE)
                        .setTreeClass(TreeUtil.TARGET_TYPE)
                        .setNode("target_" + o.get("type"))
                        .setNodeText(o.get("name").toString())
                        .setParent(TreeUtil.NODE_TARGET_ROOT)
                        .setParentText(TreeUtil.NODE_TARGET_TEXT)
                        .setNodeLevel(TreeUtil.NODE_LEVEL_TWO)
                        .setIsDisabled(Convert.toBool(o.get("status")) ? TreeUtil.STATUS_FALSE : TreeUtil.STATUS_TRUE)
                        .setIsSpread(TreeUtil.STATUS_FALSE)
                ).collect(Collectors.toList());
        // 2.生成三级节点（指标分类）
        List<Map<String, Object>> classList = dao.query("DQTree.queryTargetClassParent", null);
        treeConfigs.addAll(classList.stream()
                .map(o -> TreeUtil.create(TreeUtil.TREE_NODE)
                        .setTreeClass(TreeUtil.TARGET_TYPE)
                        .setNode("target_" + o.get("parentType") + "_" + o.get("type"))
                        .setNodeText(o.get("name").toString())
                        .setParent("target_" + o.get("parentType"))
                        .setParentText(o.get("parentName").toString())
                        .setNodeLevel(TreeUtil.NODE_LEVEL_THREE)
                        .setIsDisabled(Convert.toBool(o.get("status")) ? TreeUtil.STATUS_FALSE : TreeUtil.STATUS_TRUE)
                        .setIsSpread(TreeUtil.STATUS_FALSE)
                ).collect(Collectors.toList())
        );
        // 3.生成四级节点（生产指标）
        List<Map<String, Object>> defineList = dao.query("DQTree.queryTargetDefine", null);
        treeConfigs.addAll(defineList.stream()
                .map(o -> TreeUtil.create(TreeUtil.LEAF_NODE)
                        .setTreeClass(TreeUtil.TARGET_TYPE)
                        .setNode("target_" + o.get("typeCode") + "_" + o.get("classCode") + "_" + o.get("gradeCode") + "_" + o.get("type"))
                        .setNodeText(o.get("name").toString())
                        .setParent("target_" + o.get("typeCode") + "_" + o.get("classCode"))
                        .setParentText(o.get("parentName").toString())
                        .setNodeLevel(TreeUtil.NODE_LEVEL_FOUR)
                        .setIsDisabled(Convert.toBool(o.get("status")) ? TreeUtil.STATUS_FALSE : TreeUtil.STATUS_TRUE)
                        .setIsSpread(TreeUtil.STATUS_FALSE)
                ).collect(Collectors.toList())
        );

        dao.insert("DQTree.insertTreeConfigs", treeConfigs);

        return GeneralUtil.createEiInfo().putMsg("指标树创建成功！");
    }

}
