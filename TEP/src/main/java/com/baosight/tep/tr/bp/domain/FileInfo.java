package com.baosight.tep.tr.bp.domain;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;

import java.util.Map;

/**
 * @className: FileInfo
 * @author: tanhaowen
 * @date: 2023/3/25
 **/
public class FileInfo {
    private DateTime date;
    private String bucketName = "";
    private String fileName = "";
    private String fileNameCN = "";
    private String exportName = "";

    public FileInfo(){}

    public void setFileInfo(EiInfo paramInfo) throws PlatException{
        Map param = paramInfo.getAttr();
        if (param.containsKey("date")){
            this.date = DateFormat.parse(StrUtil.toString(param.get("date")));
        }
        if (param.containsKey("fileNameCN")){
            this.fileNameCN = StrUtil.toString(param.get("fileNameCN"));
        }
    }

    public FileInfo(String bucketName, String fileName) {
        this.bucketName = bucketName;
        this.fileName = fileName;
    }

    public void setFilePath(Map param) {
        if (param.containsKey("bucketName")){
            this.bucketName = StrUtil.toString(param.get("bucketName"));
        }
        if (param.containsKey("fileName")){
            this.fileName = StrUtil.toString(param.get("fileName"));
        }
        if (param.containsKey("fileNameCN")){
            this.fileNameCN = StrUtil.toString(param.get("fileNameCN"));
        }
    }

    public Map getFileInfoPath(){
        return MapBuilder.create()
                .put("bucketName",this.bucketName)
                .put("fileName",this.fileName)
                .build();
    }

    public Map getFileInfo(){
        return  MapBuilder.create()
                .put("date",DateUtil.format(this.date,"yyyyMMdd"))
                .put("fileNameCN",fileNameCN)
                .build();
    }

    /**
     * 验证对象是否为空
     * @return
     */
    public boolean validFileInfo(){
        boolean flag = true;
        if (this.date == null || StrUtil.isBlankIfStr(this.fileNameCN)){
            flag = false;
        }
        return flag;
    }

    /**
     * 验证对象路径是否为空
     * @return
     */
    public boolean validFileInfoPath(){
        boolean flag = true;
        if (StrUtil.isBlankIfStr(this.bucketName) || StrUtil.isBlankIfStr(this.fileName)){
            flag = false;
        }
        return flag;
    }

    public String getExportName(){
        String suffix = ".xls";
//        String[] fileNames = this.fileName.split(".");
        if (!StrUtil.isBlankIfStr(this.fileName)){
            suffix = "."+fileName.substring(fileName.lastIndexOf(".")+1);
        }
        return "("+DateUtil.format(this.date,"yyyyMMdd")+")"+this.fileNameCN+suffix;
    }

    public String getBucketName() {
        return bucketName;
    }

    public String getFileName() {
        return fileName;
    }

    public String getFileNameCN() {
        return fileNameCN;
    }
}
