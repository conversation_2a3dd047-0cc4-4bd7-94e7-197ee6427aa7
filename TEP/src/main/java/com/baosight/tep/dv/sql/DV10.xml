<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="DV10">

    <!--查询本年全网正点率/兑现率-->
    <select id="getZDLandDXL" parameterClass="java.util.Map" resultClass="java.util.HashMap">
        select
        to_char(sysdate,'yyyy') as "year_str",
        fd_line_number as "line_id",
        round(sum(fd_fulfill_train)/sum(fd_plan_train)*100,2) as "duixian",
        round((sum(fd_actual_train)-sum(fd_2min_delay_train+fd_5min_delay_train+fd_15min_delay_train+fd_30min_delay_train))/sum(fd_actual_train)*100,2) as "zhengdian"
        from ${tepProjectSchema}.t_ats_day_target_line tadtl
        where 1=1 and fd_interval_t = 410005
        and to_char(date(fd_start_datetime),'yyyy') = to_char(sysdate,'yyyy')
        and to_char(date(fd_end_datetime),'yyyy') = to_char(sysdate,'yyyy')
        group by fd_line_number
        order by fd_line_number
    </select>

    <!--查询本年全网累计载客人数-->
    <select id="getLJKY" resultClass="java.util.HashMap" parameterClass="java.util.Map">
        SELECT
        sum(fd_count_rs) as "ljzk"
        FROM ${tepProjectSchema}.t_acc_day_target_line
        WHERE
        fd_interval_t = 410005
        and to_char(date(fd_start_datetime),'yyyy') = to_char(sysdate,'yyyy')
        and to_char(date(fd_end_datetime),'yyyy') = to_char(sysdate,'yyyy')
        and fd_line_number = '0000000000'
    </select>

    <!--查询本年全网累计开行列次-->
    <select id="getLJKHRS" resultClass="java.util.HashMap" parameterClass="java.util.Map">
        select
            sum(fd_actual_train) as "ljkh"
        from ${tepProjectSchema}.t_ats_day_target_line
        where fd_interval_t = 410005
        and to_char(date(fd_start_datetime),'yyyy') = to_char(sysdate,'yyyy')
        and to_char(date(fd_end_datetime),'yyyy') = to_char(sysdate,'yyyy')
        and fd_line_number = '0000000000'
    </select>

    <!--查询全网峰值日客运 单位：万人-->
    <select id="getFZRKY" resultClass="java.util.HashMap" parameterClass="java.util.Map">
        select
            fd_rs_net/10000 as "fzky"
        from ${tepProjectSchema}.t_max_rs_line
        where  fd_line_number = '0000000000'
    </select>

    <!--查询车站信息（基础车站数、换乘车站数）-->
    <select id="getCZXX" resultClass="java.util.HashMap" parameterClass="java.util.Map">
        <!--SELECT
        count(sta_id) as "czSum",
        sum(case when (transfer_info is not null and transfer_info != '') then 1 else 0 end ) as "transStaSum"
        FROM noccbase.noccbase_sta_infog
        WHERE enable_status-->
        SELECT
        count(distinct(sta_cname)) as "czSum",
        (SELECT count(distinct sta_cname) FROM noccbase.noccbase_sta_info WHERE enable_status and transfer_info is not null and transfer_info != '') as "transStaSum"
        FROM noccbase.noccbase_sta_info
        WHERE enable_status and sta_type = 'STATION'
    </select>

    <!--查询里程数-->
    <select id="getLCS" resultClass="java.util.HashMap" parameterClass="java.util.Map">
        SELECT
            net_length as "lcs"
        FROM noccbase.noccbase_net_info
    </select>


    <!--查询去年客运强度、全国排名、公共交通分担率-->
    <select id="getQNZB" resultClass="java.util.HashMap" parameterClass="java.util.Map">
        SELECT
        fd_rs_intensity as "kyqd", <!--客运强度-->
        fd_rank_national as "qgpm", <!--全国排名-->
        fd_public_transport_sharing_rate as "ggjtfdl" <!--公共交通分担率-->
        FROM ${tepProjectSchema}.t_manual_rs_target_year
        WHERE fd_interval_t = '410010'
        and to_char(date(fd_start_datetime),'yyyy') = to_char(add_months(SYSDATE,-12),'yyyy')
        and to_char(date(fd_end_datetime),'yyyy') = to_char(add_months(SYSDATE,-12),'yyyy')
    </select>



</sqlMap>