package com.baosight.tep.dv.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ei.parser.EiInfoParserFactory;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.tep.common.util.TepDateUtils;
import com.baosight.tep.common.util.TepTFUtil;
import redis.clients.jedis.Jedis;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * NOCC大屏可视化——细分页面-客流指标1后台服务
 * HuangXJ
 * 2024-08-12 10:42:00
 */
public class ServiceDV08 extends ServiceBase {

    /**
     * redisHost,*************
     */
    private static final String REDIS_HOST = "*************";
    /**
     * 南宁主行政区
     */
    private static final List<String> DISTRICT_NAMES = new ArrayList<>(Arrays.asList("兴宁区", "西乡塘区", "江南区", "良庆区", "青秀区"));


    private static final Logger logger = LoggerFactory.getLogger(ServiceDV08.class);

    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return super.initLoad(inInfo);
    }

    /* **************************************** 当日车站客运Top10数据：开始 ****************************************** */
    /**
     * 当日车站客运Top10数据
     * NOCC微服务serviceId：S_NOCC_DV_DP_0801
     * E PLAT共享服务id：S_DV_DP_0801
     * @param inInfo EiInfo
     * @return EiInfo
     */
    public EiInfo getTodayStaRsTop10(EiInfo inInfo){
        //查询并处理当日车站客运数据
        return TepTFUtil.returnInfo(getRequestIds(inInfo), handleTodayStaRs());
    }

    /**
     * 查询并处理当日车站客运数据
     * @return List<Object>
     */
    public List<Object> handleTodayStaRs(){
        //基础数据, 车站号:车站名
        Map<String, String> staNumNameMap = getStaNumNameMap();
        //数据库查询当日车站客运数据
        List<Map<String,Object>> todayStaRsQuery = queryTodayStaRs();
        Map<String, List<Map<String,Object>>> todayStaRs = todayStaRsQuery.stream().collect(
                        //数据库数据去重
                        Collectors.toMap(
                                e -> Convert.toStr(e.get("date")) + e.get("startTime") + e.get("endTime")+ e.get("stationNumber"),
                                e -> e,
                                (v1,v2) -> v1))
                //根据车站名将数据进行分组
                .values()
                .stream()
                .collect(Collectors.groupingBy(map -> staNumNameMap.getOrDefault(Convert.toStr(map.get("stationNumber")),"")));
        //把相同站名的时间片数据相加
        Map<String,Integer> staRsMap = new HashMap<>(16);
        for (Map.Entry<String,List<Map<String,Object>>> entry : todayStaRs.entrySet()){
            double sum = 0;
            List<Map<String,Object>> list = entry.getValue();
            for (Map<String,Object> map : list){
                sum += Convert.toDouble(map.get("count"),0d);
            }
            staRsMap.put(entry.getKey(), (int) Math.round(sum));
        }
        //将键值对存入集合中，利用list排序
        List<Map.Entry<String,Integer>> staRsMapEntryList = new ArrayList<>(staRsMap.entrySet());
        staRsMapEntryList.sort(Comparator.comparingDouble(Map.Entry::getValue));
        //取Top10
        int dataSize = staRsMapEntryList.size();
        int getNum = dataSize>10 ? dataSize-10 : 0;
        staRsMapEntryList = staRsMapEntryList.subList(getNum, dataSize);
        //自定义格式输出
        List<Object> nameAndValueMap = new ArrayList<>();
        int ranking = 1;
        List<String> names = new ArrayList<>();
        List<Integer> values = new ArrayList<>();
        for (int i=staRsMapEntryList.size()-1; i>=0; i--){
            Map.Entry<String,Integer> item = staRsMapEntryList.get(i);
            Map<String, Object> map = new HashMap<>(16);
            map.put("ranking", ranking++);
            String name =  item.getKey();
            int value = item.getValue();
            map.put("staName",name);
            map.put("value", value);
            names.add(name);
            values.add(value);
            nameAndValueMap.add(map);
        }
        //数现输出
        List<Object> data = new ArrayList<>();
        data.add(0, new String[]{"appEname", "date","unitCover", "url"});
        data.add(1, names);
        data.add(2, values);
        data.add(3, nameAndValueMap);
        return data;
    }

    /**
     * 获取基础数据
     * @return  Map<String, String> {车站号:车站名, ...}
     */
    private Map<String, String> getStaNumNameMap(){
        List<Map<String, Object>> staBaseData= TepTFUtil.queryBaseData("S_BASE_DATA_03", new HashMap<>(16));
        //基础数据输出集，格式为 =》 车站号:车站名
        Map<String, String> staNumNameMap = new LinkedHashMap<>(16);
        //得到以下车站基础数据Map备用
        for (Map<String, Object> baseStaItem : staBaseData){
            //只处理已启用车站
            boolean enable = Convert.toBool(baseStaItem.get("enable_status"), true);
            if (enable){
                String staNum = baseStaItem.get("sta_id").toString();
                String staName = baseStaItem.get("sta_cname").toString();
                staNumNameMap.put(staNum, staName);
            }
        }
        return staNumNameMap;
    }

    /**
     * 设置当日车站客运TOP10查询sql的查询参数
     * @return Map<String, Object> {"timeData":[{"date":"yyyyMMdd", "startTime": "HHmm", "endTime": "HHmm"},...]}
     */
    public Map<String, Object> setTodayStaRsTop10Params(){
        Map<String, Object> queryParams = new HashMap<>(16);
        LocalDateTime currentTime = LocalDateTime.now();
        String today = DateTimeFormatter.ofPattern("yyyyMMdd").format(currentTime);
        String startTime = "0400";
        String endTime = DateTimeFormatter.ofPattern("HHmm").format(currentTime.minusMinutes(5));
        //判断是否跨天
        boolean isCrossDay = isCrossDayAcc(startTime, endTime);
        List<Map<String,Object>> timeData = new ArrayList<>();
        Map<String,Object> firstDay = new HashMap<>(16);
        if(isCrossDay){
            //如果时间跨天则分两段数据查询，再求和
            //第一天：昨日0400~2355
            LocalDateTime yesterday = currentTime.plusDays(-1);
            firstDay.put("date", DateTimeFormatter.ofPattern("yyyyMMdd").format(yesterday));
            firstDay.put("startTime", "0400");
            firstDay.put("endTime","2355");
            //第二天：今日0000~当前时间HHmm
            Map<String,Object> secondDay = new HashMap<>(16);
            secondDay.put("date", today);
            secondDay.put("startTime","0000");
            secondDay.put("endTime",endTime);
            timeData.add(firstDay);
            timeData.add(secondDay);
        }else{
            //不跨天：今日0400~当前时间HHmm
            firstDay.put("date", today);
            firstDay.put("startTime", "0400");
            firstDay.put("endTime", endTime);
            timeData.add(firstDay);
        }
        queryParams.put("timeData", timeData);
        return queryParams;
    }
    /**
     * 判断时间段是否跨天
     * @param startTime "HH:mm"
     * @param endTime "HH:mm"
     * @return boolean
     */
    private boolean isCrossDayAcc(String startTime,String endTime){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HHmm");
        LocalTime start = LocalTime.parse(startTime,timeFormatter);
        LocalTime end = LocalTime.parse(endTime,timeFormatter);
        if (end.isAfter(start)){
            return false;
        }
        return start.isAfter(end);
    }
    /* ****************************************当日车站客运Top10数据：结束 ****************************************** */



    /* ****************************************当日线网实时与预测客运数据：开始 ****************************************** */
    /**
     * @description 当日线网实时与预测客运数据
     * NOCC微服务serviceId：S_NOCC_DV_DP_0802
     * E PLAT共享服务id：S_DV_DP_0802
     * @param info EiInfo
     * @return EiInfo
     */
    public EiInfo getFlowTrend(EiInfo info){
        return TepTFUtil.returnInfo(getRequestIds(info), handleFlowTrend());
    }

    private List<Object> handleFlowTrend(){
        //调用PFM的S_NOCC_KM_DV_0105接口，获取当日线网实时与预测客运数据
        //其中参数固定获取线网30分钟粒度客运量数据
        Map<String, Object> params = new HashMap<>(16);
        params.put("spaceDesension", "线网");
        params.put("lineNumber", "0000000000");
        params.put("interval", 410003);
        params.put("firstDimension", "客运量");
        EiInfo paramsInfo = new EiInfo();
        paramsInfo.set("params", params);
        Map<String, Object> result = new HashMap<>(16);
        try {
            EiInfo dataInfo = TepTFUtil.callXService(paramsInfo, "S_NOCC_KM_DV_0105");
            result = (Map<String, Object>) dataInfo.get("result");
        }catch (Exception ignored){};
        List<Object> data = new ArrayList<>();
        data.add(0, new String[]{"appEname", "date","unitCover", "url"});
        data.add(1, result.getOrDefault("rsResult", new ArrayList<>()));
        data.add(2, result.getOrDefault("xAxisData", new ArrayList<>()));
        return data;
    }
    /* ****************************************当日线网实时与预测客运数据：结束 ****************************************** */






    /* ****************************************当日断面满载率TOP10：开始 ****************************************** */

    /**
     * 当日断面满载率TOP10数据
     * NOCC微服务serviceId：S_NOCC_DV_DP_0803
     * E PLAT共享服务id：S_DV_DP_0803
     * @param info EiInfo
     * @return EiInfo
     */
    public EiInfo getCurrentSectionRatioTop10(EiInfo info){
        Map<String, Map<String, Object>> sectionBaseData = getBaseSectionMap();
        //装载总数据的
        List<Object> totalData = new ArrayList<>();
        //查询线网维度下所有断面的断面客流量，断面满载率
        List<Map<String, Object>> sectionQuery = queryCurrentSectionRatio();
        //去重
        List<Map<String,Object>> section = new ArrayList<>(
                sectionQuery.stream().collect(Collectors.toMap(
                        e -> Convert.toStr(e.get("date")) + Convert.toStr(e.get("startTime")) + Convert.toStr(e.get("endTime")) + Convert.toStr(e.get("sectionId")),
                        e -> e,
                        (v1, v2) -> v1,
                        LinkedHashMap::new
                )).values()
        );
        section.sort(Comparator.comparingDouble(e-> Convert.toDouble(e.get("congestion"))));
        //倒序取最后10个
        int dataSize = section.size();
        int getNum = dataSize>10 ? dataSize-10 : 0;
        section = section.subList(getNum, dataSize);
        int ranking = 1;
        List<Map<String, Object>> data = new ArrayList<>();
        List<Object> values = new ArrayList<>();
        for (int i=section.size()-1; i>=0; i--){
            Map<String,Object> item = section.get(i);
            Map<String, Object> map = new HashMap<>(16);
            map.put("ranking", ranking++);
            //数据库数据
            String itemSectionId = Convert.toStr(item.get("sectionId"), "");
            float value = Convert.toFloat(item.get("congestion"));
            //填充基础数据
            Map<String, Object> sectionBaseItem = Optional.ofNullable(sectionBaseData.get(itemSectionId)).orElse(new HashMap<>(16));
            map.put("lineName", sectionBaseItem.getOrDefault("line_cname", "-"));
            map.put("sectionName", sectionBaseItem.getOrDefault("sectionName", "-"));
            map.put("direction", sectionBaseItem.getOrDefault("directionName", "-"));
            map.put("value", value);
            values.add(value);
            data.add(map);
        }
        EiInfo outInfo = new EiInfo();
        outInfo.set("values", values);
        outInfo.set("data", data);
        return outInfo;
    }

    /**
     * 获取断面基础数据
     * @return Map<String, Map<String, Object>>
     */
    private Map<String, Map<String, Object>> getBaseSectionMap(){
        //断面基础数据
        List<Map<String, Object>> sectionBase = TepTFUtil.queryBaseData("S_BASE_DATA_04", new HashMap<>(16));
        return sectionBase.stream().collect(Collectors.toMap(
                item -> item.get("section_id").toString(),
                TepTFUtil :: handleSectionBase,
                (v1, v2) -> v1
        ));
    }

    /**
     * 当日断面满载率TOP10参数设置
     */
    private Map<String, Object> setCurrentSectionRatioTop10Params(){
        Map<String, Object> params = new HashMap<>(16);
        //获取当前时间的整15分钟时间，得到结束时间
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:00");
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        int minutes = calendar.get(Calendar.MINUTE);
        int roundedMinutes = minutes / 15 * 15;
        calendar.set(Calendar.MINUTE, roundedMinutes);
        String endTime = sdf.format(calendar.getTime());
        //时间往前偏移15分钟，得到开始时间
        calendar.add(Calendar.MINUTE, -15);
        String startTime = sdf.format(calendar.getTime());
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        //固定15分钟粒度
        params.put("interval", 410002);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        params.put("date", dateFormat.format(calendar.getTime()));
        return params;
    }

    /* ****************************************当日断面满载率TOP10：结束 ****************************************** */



    /* ****************************************天气：开始 ****************************************** */

    /**
     * 获取南宁主行政区天气信息
     * 主行政区：["兴宁区", "西乡塘区", "江南区", "良庆区", "青秀区"]
     * NOCC微服务serviceId：S_NOCC_DV_DP_0804
     * E PLAT共享服务id：S_DV_DP_0804
     *
     * @param inInfo 无参数内容
     * @return EiInfo 所需信息：
     * {"主行政区名"：{天气图标"weatherIcon": "阵雨",
     *                当前温度（摄氏度）"airTemperature": "27.9",
     *                行政区名"districtName":"青秀区"
     *                天气现象"weatherDesc":"阵雨"
     *                风向+风速"windInfo":"东北风3级"
     *                过去一小时降雨量"precipitation": "6.7"},...}
     */
    public EiInfo getDistrictWeatherInfo(EiInfo inInfo) {
        // 配置Redis连接信息
        Jedis jedis = new Jedis(REDIS_HOST, 6379);
        try {
            //天气实况
            String currentWeatherJsonStr = jedis.get("currentWeather");
            //天气预报
            String weatherForecastJsonStr = jedis.get("weatherForecast");
            if (currentWeatherJsonStr != null && weatherForecastJsonStr != null) {
                //处理天气信息 =》

                //查看风速等级对应表，如风速范围[0.3,1.5]则为1级
                Map<String, Object> params = new HashMap<>(16);
                List<Map<String, Object>> windSpLevelList = dao.query("DV08.queryWidSpLevel", params);
                //天气描述信息
                List<Map<String, Object>> weatherDescList = dao.query("DV08.queryWeatherDesc", params);

                //天气实况信息
                EiInfo currentWeatherEiInfo = EiInfoParserFactory.getParser("json").parse(currentWeatherJsonStr);
                List<Map<String, Object>> weatherList = currentWeatherEiInfo.getBlock("result").getRows();
                Map<String, Map<String, Object>> weatherData = weatherList.stream().collect(Collectors.toMap(
                        e -> e.get("one_name").toString(),
                        e -> e,
                        (v1, v2) -> v1
                ));
                //天气预报信息
                EiInfo weatherForecastEiInfo = EiInfoParserFactory.getParser("json").parse(weatherForecastJsonStr);
                List<Map<String, Object>> forecastList = weatherForecastEiInfo.getBlock("result").getRows();

                //输出结果集
              List<Map<String, Object>> weatherInfo = new ArrayList<>();
                for (String district : DISTRICT_NAMES){
                    Map<String, Object> saveMap = new HashMap<>(16);
                    if (weatherData.containsKey(district)){
                        Map<String, Object> currentWeather = weatherData.get(district);
                        //区县名称
                        saveMap.put("districtName", district);
                        //天气图标
                        String weatherIconStr = getWeatherIconStr(forecastList, district);
                        saveMap.put("weatherIcon", weatherIconStr);
                        String weatherIconUrl = "http://10.124.87.201:80/ossrest/api/object/DPBJ/20240730确认版本/客流指标1/weather/"+weatherIconStr+".png?tenant=1";
                        saveMap.put("weatherIconUrl", weatherIconUrl);
                        //当前温度
                        saveMap.put("airTemperature", currentWeather.get("air_temperature"));
                        //天气现象描述
                        int weather = Convert.toInt(currentWeather.get("weather"), -1);
                        String weatherDesc = getWeatherDesc(weather, weatherDescList);
                        saveMap.put("weatherDesc", weatherDesc);
                        //风向+风速信息
                        String windInfo = getWindInfo(currentWeather, windSpLevelList);
                        saveMap.put("windInfo", windInfo);
                        //过去一小时降水量
                        saveMap.put("precipitation", currentWeather.get("precipitation"));
                    }
                    else {
                        saveMap.put("districtName", district);
                        saveMap.put("weatherIcon", "-");
                        saveMap.put("weatherIconUrl", "-");
                        saveMap.put("airTemperature", "-");
                        saveMap.put("weatherDesc", "-");
                        saveMap.put("windInfo", "-");
                        saveMap.put("precipitation", "-");
                    }
                    weatherInfo.add(saveMap);
                }
                inInfo.set("weatherInfo", weatherInfo);
            }
        } catch (PlatException platException) {
            logger.error("获取天气接口失败===》{}", platException.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("获取天气接口失败===》" + platException.getMessage());
        }
        //释放资源
        return inInfo;
    }

    /**
     * 获取风级中文名
     *
     * @param weatherItem     行政区天气信息
     * @param windSpLevelList 风级对照关系集合
     * @return String
     */
    private String getWindInfo(Map<String, Object> weatherItem, List<Map<String, Object>> windSpLevelList) {
        String windDirectionInfo = getWindDirection(weatherItem);
        //风速
        double velocity = Convert.toDouble(weatherItem.get("wind_velocity"));
        StringBuilder windInfo = new StringBuilder(windDirectionInfo);
        //风速对比数据库枚举，得到具体的风级
        for (Map<String, Object> windSpLevelMap : windSpLevelList){
            //风速区间最小值
            double windMin = Double.parseDouble(windSpLevelMap.get("windMin").toString());
            //风速区间最大值
            double windMax = Double.parseDouble(windSpLevelMap.get("windMax").toString());
            //取出风速所在区间对应的等级
            if (velocity >= windMin && velocity <= windMax){
                String windLevel = Convert.toStr(windSpLevelMap.get("windLevel"), "-") + "级";
                windInfo.append(windLevel);
                return windInfo.toString();
            }
        }
        return windInfo.toString();
    }

     /**
     * 获取风向描述
     * @param weatherItem 行政区天气信息
     * @return String
     */
    private String getWindDirection(Map<String, Object> weatherItem) {
        //风向
        String windDirection = Convert.toStr(weatherItem.get("wind_direction"));
        Map<String, String> windDirJsonMap = JSON.parseObject(WIND_DIRECTIONS, Map.class);
        return windDirJsonMap.getOrDefault(windDirection, "-");
    }

    /**
     * 获取天气现象描述中文名:获取编号 =》 对照数据库枚举 =》 得到描述
     *
     * @param weather         天气现象编号
     * @param weatherDescList 天气现象编号对照关系集合
     * @return String
     */
    private String getWeatherDesc(int weather, List<Map<String, Object>> weatherDescList) {
        //天气现象描述:获取编号 =》 对照数据库枚举 =》 得到描述
        String weatherDesc = "";
        for (Map<String, Object> weatherDescItem : weatherDescList){
            int weatherCode = Convert.toInt(weatherDescItem.get("weatherCode"));
            if (weather == weatherCode){
                return weatherDescItem.get("weatherDesc").toString();
            }
        }
        return weatherDesc;
    }

    /**
     * 根据天气预报信息获取天气图标字符串
     * @param forecastList 天气预报集
     * @param districtName 当前行政区
     * @return String
     * 天气图标获取逻辑汇总：
     * 1、当前时间> 08:00 < 20:00
     *     当日20:00
     * 2、当前时间 > 08:00 > 20：00
     *     后一天的08:00
     * 3、当前时间 < 08:00 < 20:00
     *     前一天 20：00
     */
    private String getWeatherIconStr(List<Map<String, Object>> forecastList, String districtName) throws PlatException{
        String weatherIconStr = "多云";
        //当天
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime currentTime = LocalDateTime.now();
        //遍历天气预报中的数据，筛选得到天气图标：行政区一致 + 日期对应
        for (Map<String, Object> forecastItem : forecastList){
            boolean isCurrentDistrict = districtName.equals(forecastItem.get("one_name"));
            if (isCurrentDistrict){
                int currentHour = currentTime.getHour();
                LocalDateTime forecastTime = LocalDateTime.parse(forecastItem.get("time").toString(), formatter1);
                if (currentHour < 8){
                    //当前时间 < 08:00 则取前一日的20:00的数据
                    //前一日
                    boolean dateTrue = (formatter2.format(forecastTime)).equals(formatter2.format(currentTime.plusDays(-1)));
                    if (dateTrue && forecastTime.getHour()==20){
                        weatherIconStr = forecastItem.get("weather").toString();
                        return weatherIconStr;
                    }
                }
                else if (currentHour<=20){
                    //当前时间> 08:00 < 20:00,则取当日20:00
                    boolean dateTrue = (formatter2.format(forecastTime)).equals(formatter2.format(currentTime));
                    if (dateTrue && forecastTime.getHour()==20){
                        weatherIconStr = forecastItem.get("weather").toString();
                        return weatherIconStr;
                    }
                }
                else {
                    //当前时间 > 20：00，则取  后一天的08:00
                    boolean dateTrue = (formatter2.format(forecastTime)).equals(formatter2.format(currentTime.plusDays(1)));
                    if (dateTrue && forecastTime.getHour()==8){
                        weatherIconStr = forecastItem.get("weather").toString();
                        return weatherIconStr;
                    }
                }
            }
        }
        return weatherIconStr;
    }


    /**
     * 获取南宁市未来七天天气预报
     * NOCC微服务serviceId：S_NOCC_DV_DP_0805
     * E PLAT共享服务id：S_DV_DP_0805
     * @param inInfo 无参数内容
     * @return EiInfo weatherForecastEiInfo
     */
    public EiInfo getWeatherForecastInfo(EiInfo inInfo) {
        // 配置Redis连接信息
        Jedis jedis = new Jedis(REDIS_HOST, 6379);
        try {
            //天气实况
            String currentWeatherJsonStr = jedis.get("currentWeather");
            //天气预报
            String weatherForecastJsonStr = jedis.get("weatherForecast");
            if (currentWeatherJsonStr != null && weatherForecastJsonStr != null) {
                //处理天气信息 =》
                //天气描述信息
                List<Map<String, Object>> weatherDescList = dao.query("DV08.queryWeatherDesc", new HashMap<>(16));
                //天气预报信息:只获取南宁市数据
                Map<String, Map<String, Object>> nanNingForecastData = getNanNingForecastData(weatherForecastJsonStr);
                //根据未来七天的日期顺序处理数据
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM月dd日");
                LocalDateTime currentDate = LocalDateTime.now();
                List<Map<String, Object>> forecastData = new ArrayList<>();
                for (int i=0; i<7; i++){
                    Map<String, Object> saveMap = new HashMap<>(16);
                    LocalDateTime dateTime = currentDate.plusDays(i);
                    String date = formatter.format(dateTime);
                    if (nanNingForecastData.containsKey(date)){
                        Map<String, Object> currentWeather = nanNingForecastData.get(date);
                        //天气图标
                        String weatherIconStr = currentWeather.get("weather").toString();
                        saveMap.put("weatherIcon", weatherIconStr);
                        String weatherIconUrl = "http://10.124.87.201:80/ossrest/api/object/DPBJ/20240730确认版本/客流指标1/weather/"+weatherIconStr+".png?tenant=1";
                        saveMap.put("weatherIconUrl", weatherIconUrl);
                        //气温范围：最低气温~最高气温
                        String temperature = currentWeather.get("min_temperature") + "~" + currentWeather.get("max_temperature") + "℃";
                        saveMap.put("temperature", temperature);
                        //天气现象描述
                        int weather = Convert.toInt(currentWeather.get("weather"), -1);
                        String weatherDesc = getWeatherDesc(weather, weatherDescList);
                        saveMap.put("weatherDesc", weatherDesc);
                        //风向+风速信息
                        saveMap.put("windDirection", getWindDirection(currentWeather));
                        saveMap.put("windVelocity", currentWeather.get("wind_velocity"));
                    }
                    else {
                        saveMap.put("weatherIcon", "-");
                        saveMap.put("weatherIconUrl", "-");
                        saveMap.put("temperature", "-");
                        saveMap.put("weatherDesc", "-");
                        saveMap.put("windDirection", "-");
                        saveMap.put("windVelocity", "-");
                    }
                    saveMap.put("dayOfWeek", getDayOfWeek(dateTime));
                    saveMap.put("date", date);
                    forecastData.add(i, saveMap);
                }
                forecastData.get(0).putAll(getTodayWeatherInitData(currentWeatherJsonStr));
                inInfo.set("forecastData", forecastData);
            }
        } catch (PlatException platException) {
            logger.error("获取天气接口失败===》{}", platException.getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("获取天气接口失败===》" + platException.getMessage());
        }
        //释放资源
        return inInfo;
    }

    /**
     * 南宁市今日天气情况基础信息 ： 当前温度、气温范围
     * @param currentWeatherJsonStr String
     * @return 南宁市当前温度
     */
    private Map<String, Object> getTodayWeatherInitData(String currentWeatherJsonStr){
        Map<String, Object> todayWeather = new HashMap<>(16);
        String result = "";
        //天气实况信息
        EiInfo currentWeatherEiInfo = EiInfoParserFactory.getParser("json").parse(currentWeatherJsonStr);
        List<Map<String, Object>> weatherList = currentWeatherEiInfo.getBlock("result").getRows();
        for (Map<String, Object> item : weatherList){
            if ("南宁市".equals(item.get("one_name").toString())){
                todayWeather.put("airTemperature", Convert.toStr(item.get("air_temperature"), ""));
                //气温范围：最低气温~最高气温
                String temperature = item.get("min_temperature") + "~" + item.get("max_temperature") + "℃";
                todayWeather.put("temperature", temperature);
            }
        }
        return todayWeather;
    }

    /**
     * 天气预报信息:只获取南宁市数据
     */
    private Map<String, Map<String, Object>> getNanNingForecastData(String weatherForecastJsonStr){
        List<Map<String, Object>> forecastList = EiInfoParserFactory.getParser("json").parse(weatherForecastJsonStr)
                .getBlock("result").getRows();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String today = formatter.format(LocalDateTime.now());
        return forecastList.stream()
                //筛出南宁市+对应（每天有时间为08:00 和 20:00的数据，取有高低气温数据的）的数据
                .filter(e -> {
                    boolean b1 =  "南宁市".equals(e.get("one_name").toString());
                    String maxTemperature = Convert.toStr(e.get("max_temperature"), "-");
                    boolean b2 = !("-".equals(maxTemperature));
                    boolean b3 = !("<no value>".equals(maxTemperature));
                    boolean b4 = today.equals(e.get("time").toString().substring(0, 10));
                    return b1 && ((b2&&b3)||b4);
                })
                .collect(Collectors.toMap(
                        e -> e.get("time").toString().substring(5, 7) + "月" + e.get("time").toString().substring(8, 10) + "日",
                        e -> e,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 获取 周几 数据
     */
    private String getDayOfWeek(LocalDateTime dateTime){
        String result = "";
        switch (dateTime.getDayOfWeek()){
            case MONDAY:
                result = "周一";
                break;
            case TUESDAY:
                result = "周二";
                break;
            case WEDNESDAY:
                result = "周三";
                break;
            case THURSDAY:
                result = "周四";
                break;
            case FRIDAY:
                result = "周五";
                break;
            case SATURDAY:
                result = "周六";
                break;
            case SUNDAY:
                result = "周日";
                break;
        }
        return result;
    }
//周一：Monday； 周二：Tuesday ； 周三：Wednesday ； 周四：Thursday ； 周五：Friday； 周六：Saturday； 周日：Sunday；
    /* ****************************************天气：结束 ***************************************** */

    /**
     * 获取日报上的客流数据：昨日客流、日均、本年累计
     * NOCC微服务serviceId：S_NOCC_DV_DP_0806
     * @param info EiInfo
     * @return EiInfo
     */
    public EiInfo getReportRsData(EiInfo info){
        Map<String, Object> params = new HashMap<>(16);
        params.put("lineNumber", "0000000000");

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String date = dateFormat.format(System.currentTimeMillis() - 86400000);
        if (hour <= 7){
            date = dateFormat.format(System.currentTimeMillis() - 2*86400000);
        }
        params.put("date", date);
        List<Map<String, Object>> list = queryReportRsData(params);
        if (list.size() <= 0){
            //若没数据则返回dataStatus为-1，有数据则返回1：有数据时才更新
            info.set("dataStatus", -1);
        }else {
            info.set("dataStatus", 1);
            Map<String, Object> data =  list.get(0);
            Map<String, Object> result = new HashMap<>(16);
            double rs = keepTwoDecimal(Convert.toDouble(data.get("rs"), 0d) / 10000);
            double avgRs = keepTwoDecimal(Convert.toDouble(data.get("avgRs"), 0d) / 10000);
            result.put("rs", rs);
            result.put("yearRs", Convert.toDouble(data.get("yearRs"), 0d));
            result.put("avgRs", avgRs);
            info.set("data", result);
        }
        return info;
    }

    /**
     * 获取日报上的设备故障
     */
    private List<Map<String,Object>> queryRepFault(Map<String, Object> params){
        List<?> list = dao.query("DV08.queryRepFault", params, 0,-999999);
        return TepTFUtil.toListMap(list);
    }
    /**
     * 日报上的线网设备故障数据
     * NOCC微服务serviceId：S_NOCC_DV_DP_0807
     */
    public EiInfo getFaultNetData(EiInfo info){
        Map<String, Object> params = new HashMap<>(16);
        params.put("noLineNumber", "0000000000");
        //昨日数据
        String yesterday = TepDateUtils.getYesterday(2);
        params.put("startDate", yesterday);
        params.put("endDate", yesterday);
        List<Map<String,Object>> yesterdayQuery = queryRepFault(params);
        List<Integer> yesterdayArray = handleFaultData(yesterdayQuery);

        //本周数据
        //获取本周日期
        String currentDate = TepDateUtils.getCurrentDate(2);
        List<String> datesOfWeek = TepDateUtils.getDatesOfWeek(currentDate);
        params.put("startDate", datesOfWeek.get(0));
        params.put("endDate", datesOfWeek.get(datesOfWeek.size()-1));
        List<Map<String,Object>> weekQuery = queryRepFault(params);
        List<Integer>  weekArray = handleFaultData(weekQuery);

        //本月数据
        List<String> datesOfMonth = TepDateUtils.getDatesOfMonth(currentDate.substring(0, 7));
        params.put("startDate", datesOfMonth.get(0));
        params.put("endDate", datesOfMonth.get(datesOfMonth.size()-1));
        List<Map<String,Object>> monthQuery = queryRepFault(params);
        List<Integer>  monthArray = handleFaultData(monthQuery);


        ArrayList<Object> data = new ArrayList<>();
        //车辆1、信号2、供电3、门梯4、机电（数据取0）、其它5
        data.add(new String[]{"vehicle", "signal", "powerSupply", "doorLadder", "electromechanical", "other"});
        data.add(yesterdayArray);
        data.add(weekArray);
        data.add(monthArray);
        info.set("data", data);
        return regular(info, data);
    }

    private List<Integer> handleFaultData(List<Map<String,Object>> list){
        List<Integer> arr = new ArrayList<>();
        //车辆1、信号2、供电3、门梯4、机电（数据取0）、其它5
        int vehicle = 0, signal = 0, powerSupply = 0, doorLadder = 0, other = 0;
        for (Map<String,Object> map : list){
            int faultType = Convert.toInt(map.get("type"), -1);
            switch (faultType){
                case 1:
                    vehicle++;
                    break;
                case 2:
                    signal++;
                    break;
                case 3:
                    powerSupply++;
                    break;
                case 4:
                    doorLadder++;
                    break;
                case 5:
                    other++;
                    break;
                default:break;
            }
        }
        arr.add(0, vehicle);
        arr.add(1, signal);
        arr.add(2, powerSupply);
        arr.add(3, doorLadder);
        arr.add(4, 0);
        arr.add(5, other);
        return arr;
    }

    /**
     * 日报上的线路设备故障数据
     * NOCC微服务serviceId：S_NOCC_DV_DP_0808
     */
    public EiInfo getFaultLineData(EiInfo info){
        Map<String, Object> params = new HashMap<>(16);
        params.put("noLineNumber", "0000000000");
        //查询本月数据
        String currentMonth= TepDateUtils.getCurrentDate(2).substring(0, 7);
        List<String> datesOfMonth = TepDateUtils.getDatesOfMonth(currentMonth);
        params.put("startDate", datesOfMonth.get(0));
        params.put("endDate", datesOfMonth.get(datesOfMonth.size()-1));
        Map<String, List<Map<String,Object>>> monthQuery = queryRepFault(params)
                .stream().collect(Collectors.groupingBy(e -> e.get("line").toString()));
        //输出数据
        ArrayList<Object> data = new ArrayList<>();
        String[] lineNums = new String[]{"0100000000", "0200000000", "0300000000", "0400000000", "0500000000"};
        //type：车辆1、信号2、供电3、门梯4、机电（数据取0）、其它5
        String[] head = new String[]{"门梯故障", "供电故障", "车辆故障", "机电故障", "信号故障", "其他故障"};
        data.add(head);
        for (String line : lineNums){
            //车辆1、信号2、供电3、门梯4、机电（数据取0）、其它5
            int vehicle = 0, signal = 0, powerSupply = 0, doorLadder = 0, other = 0;
            List<Integer> arr = new ArrayList<>();
           if (monthQuery.containsKey(line)){
               List<Map<String,Object>> oneLineData = monthQuery.get(line);
               for (Map<String,Object> map : oneLineData){
                   int faultType = Convert.toInt(map.get("type"), -1);
                   switch (faultType){
                       case 1:
                           vehicle++;
                           break;
                       case 2:
                           signal++;
                           break;
                       case 3:
                           powerSupply++;
                           break;
                       case 4:
                           doorLadder++;
                           break;
                       case 5:
                           other++;
                           break;
                       default:break;
                   }
               }
           }
            arr.add(0, doorLadder);
            arr.add(1, powerSupply);
            arr.add(2, vehicle);
            arr.add(3, 0);
            arr.add(4, signal);
            arr.add(5, other);
            //添加1~5号线数据
            data.add(arr);
        }
        info.set("data", data);
        return info;
    }









    /* ---------------------------------------SQL：开始----------------------------------------------*/
    /**
     * 数据库查询当日车站客运数据
     * @return List<Map<String,Object>>
     *     内容为：[{"date": "20240814", "startTime": "2320", "endTime": "2325", "stationNumber": "010000000012", 时间片内客运量"count": "58"},...]
     */
    private List<Map<String,Object>> queryTodayStaRs(){
        List<?> todayStaRsQuery = dao.query("DV08.queryTodayStaRs", setTodayStaRsTop10Params(),0,-999999);
        return TepTFUtil.toListMap(todayStaRsQuery);
    }

    /**
     * 当日当前时间片断面满载率TOP10参数设置
     */
    private List<Map<String,Object>> queryCurrentSectionRatio(){
        List<?> todayStaRsQuery = dao.query("DV08.queryCurrentSectionRatio", setCurrentSectionRatioTop10Params(),0,-999999);
        return TepTFUtil.toListMap(todayStaRsQuery);
    }

    /**
     * 获取日报上的客流数据：日均、本年累计
     */
    private List<Map<String,Object>> queryReportRsData(Map<String, Object> params){
        List<?> list = dao.query("DV08.queryReportRsData", params,0,-999999);
        return TepTFUtil.toListMap(list);
    }
    /* ---------------------------------------SQL：结束----------------------------------------------*/



    /* ---------------------------------------小方法：开始----------------------------------------------*/
    /**
     * @description 返回给eplat的固定接口
     * @param info 获取ids:组键ID
     * @param data 封装好的数据体
     * @return 返回固定格式参数
     */
    private EiInfo regular(EiInfo info,List<Object> data){
        EiInfo outInfo = new EiInfo();
        //固定代码
        //requestList [{ ids:[id1], params:{key1:value1,key2:value2} }]
        List requestList = JSONObject.parseObject(info.get("requestList").toString(), List.class);
        Map parameter =(Map) requestList.get(0);
        List ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);
        //固定流程
        //构建数组对象 [{ids:["..."], data:[[...],[...]]}]
        HashMap<Object, Object> map = new HashMap<>();
        //组键ID列表  可以不解析直接返回  必填
        map.put("ids",ids);
        //返回的二维数组数据
        map.put("data",data);
        ArrayList<Object> result = new ArrayList<>();
        result.add(map);
        outInfo.set("result",result);
        return outInfo;
    }

    /**
     * @description 获取从数现传过来的ids（为ids必须获取的）
     * @param info EiInfo
     * @return List<?>
     */
    private static List<?> getRequestIds(EiInfo info){
        List<?> ids = new ArrayList<>();
        try {
            List<?> requestList = JSONObject.parseObject(info.get("requestList").toString(), List.class);
            Map parameter = (Map) requestList.get(0);
            ids = JSONObject.parseObject(parameter.get("ids").toString(), List.class);
        } catch (Exception ignored) {
        }
        return ids;
    }
    /**
     * @description 获取从数现传过来的ids（为ids必须获取的，取空）
     * @return List<?>
     */
    private List<?> getRequestIds1(){
        //本地测试
        return new ArrayList<>();
    }

    /**
     * 值并保留两位小数
     */
    private Double keepTwoDecimal(double value){
        return Convert.toDouble(String.format("%.2f", value));
    }

    private final static String WIND_DIRECTIONS = "{\n" +
            "    \"东\": \"东风\",\n" +
            "    \"南\": \"南风\",\n" +
            "    \"西\": \"西风\",\n" +
            "    \"北\": \"北风\",\n" +
            "    \"东东南\": \"东风转东南风\",\n" +
            "    \"东西南\": \"东风转西南风\",\n" +
            "    \"东西北\": \"东风转西北风\",\n" +
            "    \"东东北\": \"东转东北风\",\n" +
            "    \"南东南\": \"南风转东南风\",\n" +
            "    \"南西南\": \"南风转西南风\",\n" +
            "    \"南西北\": \"南风转西北风\",\n" +
            "    \"南东北\": \"南风转东北风\",\n" +
            "    \"西东南\": \"西风转东南风\",\n" +
            "    \"西西南\": \"西风转西南风\",\n" +
            "    \"西西北\": \"西风转西北风\",\n" +
            "    \"西东北\": \"西风转东北风\",\n" +
            "    \"北东南\": \"北风转东南风\",\n" +
            "    \"北西南\": \"北风转西南风\",\n" +
            "    \"北西北\": \"北风转西北风\",\n" +
            "    \"北东北\": \"北风转东北风\",\n" +
            "    \"东南东\": \"东南风转东风\",\n" +
            "    \"东南南\": \"东南风转南风\",\n" +
            "    \"东南西\": \"东南风转西风\",\n" +
            "    \"东南北\": \"东南风转北风\",\n" +
            "    \"东南\": \"东南风\",\n" +
            "    \"东南西南\": \"东南风转西南风\",\n" +
            "    \"东南西北\": \"东南风转西北风\",\n" +
            "    \"东南东北\": \"东南风转东北风\",\n" +
            "    \"西南东\": \"西南风转东风\",\n" +
            "    \"西南南\": \"西南风转南风\",\n" +
            "    \"西南西\": \"西南风转西风\",\n" +
            "    \"西南北\": \"西南风转北风\",\n" +
            "    \"西南东南\": \"西南风转东南风\",\n" +
            "    \"西南\": \"西南风\",\n" +
            "    \"西南西北\": \"西南风转西北风\",\n" +
            "    \"西南东北\": \"西南风转东北风\",\n" +
            "    \"西北东\": \"西北风转东风\",\n" +
            "    \"西北南\": \"西北风转南风\",\n" +
            "    \"西北西\": \"西北风转西风\",\n" +
            "    \"西北北\": \"西北风转北风\",\n" +
            "    \"西北东南\": \"西北风转东南风\",\n" +
            "    \"西北西南\": \"西北风转西南风\",\n" +
            "    \"西北\": \"西北风\",\n" +
            "    \"西北东北\": \"西北风转东北风\",\n" +
            "    \"东北东\": \"东北风转东风\",\n" +
            "    \"东北南\": \"东北风转南风\",\n" +
            "    \"东北西\": \"东北风转西风\",\n" +
            "    \"东北北\": \"东北风转北风\",\n" +
            "    \"东北东南\": \"东北风转东南风\",\n" +
            "    \"东北西南\": \"东北风转西南风\",\n" +
            "    \"东北西北\": \"东北风转西北风\",\n" +
            "    \"东北\": \"东北风\"\n" +
            "}";

    /* ---------------------------------------小方法：结束----------------------------------------------*/

}
