package com.baosight.tep.dv.service;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 指标与客流预测系统接口_NOCC接口_日期类型与节假日一览接口
 * <AUTHOR>
 * @date 2023/7/3
 */
@Slf4j
public class ServiceDV01 extends ServiceBase {
    @Override
    public EiInfo initLoad(EiInfo initInfo){
        return initInfo;
    }

    /**
     * 获取日期类型管理表源数据（原本存在表中的数据）
     * @param info EiInfo，内不需含查询条件，因为要查全部
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> queryAllDate(EiInfo info){
        //源数据（原本存在表中的数据）
        return dao.query("DV01.queryAllDate", info.getAttr());
    }

    /**
     * 批量更新日期类型管理表数据
     * @param list 需要更新的数据集合，
     *              必含参数date、date_type、holiday_type,update_time
     */
    public void updateDateBatch(List<Map<String, Object>> list){
        dao.updateBatch("DV01.updateDateBatch", list);
    }

    /**
     * 批量数据插入日期类型管理表
     * @param list 需要插入的数据集合，
     *              必含参数date、date_type、holiday_type,update_time
     */
    public void insertDateBatch(List<Map<String, Object>> list){
        dao.insertBatch("DV01.insertDateBatch", list);
    }


    /**
     * 获取假日类型表源数据（原本存在表中的数据）
     * @param info EiInfo，内不需含查询条件，因为要查全部
     * @return List<Map<String, Object>>
     */
    public List<Map<String, Object>> queryAllHolidayType(EiInfo info){
        //源数据（原本存在表中的数据）
        return dao.query("DV01.queryAllHolidayType", info.getAttr());
    }

    /**
     * 批量更新假日类型表数据
     * @param list 需要更新的数据集合，必含参数holiday_type,holiday_name、update_time
     */
    public void updateHolidayTypeBatch(List<Map<String, Object>> list){
        dao.updateBatch("DV01.updateHolidayTypeBatch", list);
    }

    /**
     * 批量数据插入假日类型表
     * @param list 需要插入的数据集合，必含参数holiday_type,holiday_name、update_time
     */
    public void insertHolidayTypeBatch(List<Map<String, Object>> list){
        dao.insertBatch("DV01.insertHolidayTypeBatch", list);
    }

    public EiInfo returnOut(int status, String msg){
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(status);
        outInfo.setMsg(msg);
        System.out.println("返回结果：");
        System.out.println(outInfo);
        System.out.println("**************************接口日志结尾********************************");
        return outInfo;
    }

    /**
     * 指标系统与客流预测系统内部接口：日期类型、假日类型公共方法
     * @param initInfo 客流预测传入的EiInfo，内含要更新or插入的数据
     *                 日期类型：传入数据字段包含：日期date、日期类型date_type、假日类型holiday_type、更新时间update_time
     *                 假日类型：传入数据字段包含：假日类型holiday_type、中文名holiday_name、更新时间update_time
     * @param target "date" or "holiday_type"
     * @return EiInfo操作结果，包含"msg"、"traceId"、"detailMsg"、"msgKey"、"status
     */
    public EiInfo editor(EiInfo initInfo, String target){
        //（注:以下代码逻辑以日期类型为例）
        EiInfo outInfo = new EiInfo();
        //日志打印
        System.out.println("**************************接口日志开始********************************");
        long startTime = System.currentTimeMillis();
        System.out.println("传入数据：");
        System.out.println(initInfo);
        if (!initInfo.getBlocks().containsKey("result")){
            return returnOut(-1, "数据接收失败，请补传！");
        }
        //新传入的需要进行操作的数据
        List<Map<String, Object>> newList = initInfo.getBlock("result").getRows();
        if (newList.size() <= 0){
            return returnOut(-1, "传入空数据！");
        }
        int newSize = newList.size();
        int insertSize;
        int updateSize = 0;
        //源数据（原本存在表中的数据）
        List<Map<String, Object>> oriList = "date".equals(target) ? queryAllDate(outInfo) : queryAllHolidayType(outInfo);
        int oriSize = oriList.size();
        //总逻辑：取出原数据集合中的oriDates，用新传入newList对比oriDates，将newList中date在oriDates的放入更新集合，然后newList去掉更新集合剩下的即为插入集合
        if (oriList.size() > 0){
            //1.取出原数据集合中的oriDates
            List<String> oriDates = new ArrayList<>();
            oriList.forEach(e -> oriDates.add(e.get(target).toString()));
            //2.用新传入newList对比oriDates，将newList中date在oriDates的放入更新集合
            List<Map<String, Object>> updateList = new ArrayList<>();
            newList.stream().filter(map -> oriDates.contains(map.get(target).toString()))
                    .forEach(updateList::add);
            updateSize = updateList.size();
            //数据更新
            if ("date".equals(target)) {
                updateDateBatch(updateList);
            } else {
                updateHolidayTypeBatch(updateList);
            }
            //3.然后newList去掉更新集合剩下的即为插入集合
            newList.removeAll(updateList);
        }
        //数据插入
        if ("date".equals(target)) {
            insertDateBatch(newList);
        } else {
            insertHolidayTypeBatch(newList);
        }
        long endTime = System.currentTimeMillis();
        insertSize = newList.size();
        long needTime = endTime - startTime;
        System.out.println("数据量：源表含" + oriSize + "条,新传入："+newSize+"条，更新:"
                + updateSize + "条，插入："+ insertSize + "条，需要时长为：" + needTime + "ms");
        return returnOut(0, "处理成功！");
    }

    /**
     * 指标系统与客流预测系统内部接口：更新或插入日期类型
     * @param initInfo 客流预测传入的EiInfo，内含要更新or插入的日期类型数据
     *                 传入数据字段包含：日期date、日期类型date_type、假日类型holiday_type、更新时间update_time
     * @return EiInfo操作结果，包含"msg"、"traceId"、"detailMsg"、"msgKey"、"status
     */
    public EiInfo editorDateType(EiInfo initInfo){
        return  editor(initInfo, "date");
    }

    /**
     * 指标系统与客流预测系统内部接口：更新或插入假日类型数据
     * @param initInfo 客流预测传入的EiInfo，内含要更新或插入假日类型数据
     *                 传入数据字段包含：假日类型holiday_type、中文名holiday_name、更新时间update_time
     * @return EiInfo操作结果，包含"msg"、"traceId"、"detailMsg"、"msgKey"、"status
     */
    public EiInfo editorHolidayType(EiInfo initInfo){
        return editor(initInfo, "holiday_type");
    }

}
