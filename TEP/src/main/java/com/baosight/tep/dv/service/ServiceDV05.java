package com.baosight.tep.dv.service;

import com.baosight.iplat4j.core.ei.EiBlock;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.impl.ServiceBase;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.tep.common.util.DvUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 施工和安全系统的接口
 * @author: lanyifu
 * @date: 2023/10/18/9:45
 */
public class ServiceDV05 extends ServiceBase {

	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		return super.initLoad(inInfo);
	}

	/**
	 * 施工计划分析定时任务
	 * @param inInfo
	 * @serivceId S_NOCC_TEP_06
	 * @task J_DV_01
	 * @trigger task_dv_01
	 * @cron 0 30 5 * * ? 每天凌晨5:30
	 * @return
	 */
	public EiInfo constructionPlanTask(EiInfo inInfo) throws Exception {
		inInfo.set("dataType", "2001");
		inInfo.set("start_date", DvUtils.getAppointDate(DateUtils.curDateTimeStr19(),-1,"day").substring(0,10));
		inInfo.set("end_date", DvUtils.getAppointDate(DateUtils.curDateTimeStr19(),-1,"day").substring(0,10));
		//调用微服务接口S_NOCC_TEP_URL_01，获取内容
		inInfo.set(EiConstant.serviceId, "S_NOCC_TEP_URL_01");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		//调用方法
		EiInfo eiInfo = addConstructionPlan(outInfo);
		if (eiInfo.getStatus() < 0) {
			throw new PlatException(eiInfo.getMsg());
		}
		inInfo.setMsg("施工计划分析更新成功");
		return inInfo;
	}

	/**
	 * 本周公司级重点监控作业定时任务
	 * @param inInfo
	 * @serivceId S_NOCC_TEP_07
	 * @task J_DV_02
	 * @trigger task_dv_02
	 * @cron 0 30 5 * * ? 每天凌晨5:30
	 * @return
	 */
	public EiInfo keyMonitoringWorkTask(EiInfo inInfo) {
		// 创建日期格式化器
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		// 获取当前日期
		LocalDate currentDate = LocalDate.now();
		// 获取本周的第一天和最后一天日期
		LocalDate startOfWeek = currentDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
		LocalDate endOfWeek = currentDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
		inInfo.set("dataType", "2002");
		inInfo.set("start_date", startOfWeek.format(formatter));
		inInfo.set("end_date", endOfWeek.format(formatter));
		//调用微服务接口S_NOCC_TEP_URL_01，获取内容
		inInfo.set(EiConstant.serviceId, "S_NOCC_TEP_URL_01");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		//调用方法
		EiInfo eiInfo = addKeyMonitoringTasks(outInfo);
		if (eiInfo.getStatus() < 0) {
			throw new PlatException(eiInfo.getMsg());
		}
		inInfo.setMsg("本周公司级重点监控作业更新成功");
		return inInfo;
	}

	/**
	 * 昨日各施工完成情况定时任务
	 * @param inInfo
	 * @serivceId S_NOCC_TEP_08
	 * @task J_DV_03
	 * @trigger task_dv_03
	 * @cron 0 30 5 * * ? 每天凌晨5:30
	 * @return
	 */
	public EiInfo constructionCompletionStatusTask(EiInfo inInfo) throws Exception {
		inInfo.set("dataType", "2003");
		inInfo.set("start_date", DvUtils.getAppointDate(DateUtils.curDateTimeStr19(),-1,"day").substring(0,10));
		inInfo.set("end_date", DvUtils.getAppointDate(DateUtils.curDateTimeStr19(),-1,"day").substring(0,10));
		//调用微服务接口S_NOCC_TEP_URL_01，获取内容
		inInfo.set(EiConstant.serviceId, "S_NOCC_TEP_URL_01");
		EiInfo outInfo = XServiceManager.call(inInfo);
		if (outInfo.getStatus() < 0) {
			throw new PlatException(outInfo.getMsg());
		}
		//调用方法
		EiInfo eiInfo = addConstructionCompletionStatus(outInfo);
		if (eiInfo.getStatus() < 0) {
			throw new PlatException(eiInfo.getMsg());
		}
		inInfo.setMsg("昨日各施工完成情况更新成功");
		return inInfo;
	}

	/**
	 * 施工计划分析接口
	 * @param inInfo
	 * @return
	 */
	public EiInfo addConstructionPlan(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			String datetime = DvUtils.getAppointDate(DateUtils.curDateTimeStr19(), -1, "day").substring(0,10);
			//传来的数据没有时间间隔类型和日期类型
			EiBlock blockData = inInfo.getBlock("result");
			for (int i = 0; i < blockData.getRows().size(); i++) {
				Map map = new HashMap();
				map.put("lineNumber", blockData.getRow(i).get("line_number"));
				map.put("interval", 410005);
				map.put("startDatetime", datetime);
				map.put("endDatetime", datetime);
				if (blockData.getRow(i).get("start_datetime") != null) {
					map.put("startDatetime", blockData.getRow(i).get("start_datetime"));
				}
				if (blockData.getRow(i).get("end_datetime") != null) {
					map.put("endDatetime", blockData.getRow(i).get("end_datetime"));
				}
				map.put("A1", blockData.getRow(i).get("A1"));
				map.put("A2", blockData.getRow(i).get("A2"));
				map.put("A3", blockData.getRow(i).get("A3"));
				map.put("B1", blockData.getRow(i).get("B1"));
				map.put("B2", blockData.getRow(i).get("B2"));
				map.put("C1", blockData.getRow(i).get("C1"));
				map.put("sumA", blockData.getRow(i).get("sum_A"));
				map.put("sumB", blockData.getRow(i).get("sum_B"));
				map.put("sumC", blockData.getRow(i).get("sum_C"));
				map.put("sum", blockData.getRow(i).get("sum"));
				map.put("cancelPlan", blockData.getRow(i).get("cnacel_plan"));
				map.put("completePlan", blockData.getRow(i).get("complete_plan"));
				map.put("unauthPlan", blockData.getRow(i).get("unauth_plan"));
				map.put("fulfillRate", blockData.getRow(i).get("fulfill_rate"));
				map.put("doubleWeekPlan", blockData.getRow(i).get("double_week_plan"));
				map.put("dayPlan", blockData.getRow(i).get("day_plan"));
				map.put("temporaryPlan", blockData.getRow(i).get("temporary_plan"));
				map.put("uploadTime", blockData.getRow(i).get("upload_time"));
				dao.insert("DV05.insertConstructionPlan", map);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("记录插入失败！" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("记录插入成功！");
		return outInfo;
	}

	/**
	 * 本周公司级重点监控作业接口
	 * @param inInfo
	 * @return
	 */
	public EiInfo addKeyMonitoringTasks(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			List<Map<String,Object>> insertList = new ArrayList();
			//传来的数据没有时间间隔类型和日期类型,没有开始时间和结束时间
			EiBlock blockData = inInfo.getBlock("result");
			for (int i = 0; i < blockData.getRows().size(); i++) {
				Map map = new HashMap();
				map.put("lineNumber", blockData.getRow(i).get("line_number"));
				map.put("interval", 410005);
				map.put("taskDate", blockData.getRow(i).get("task_date"));
				map.put("startDatetime", blockData.getRow(i).get("task_date"));
				map.put("endDatetime", blockData.getRow(i).get("task_date"));
				map.put("taskTime", blockData.getRow(i).get("task_time"));
				map.put("taskStartDatetime", blockData.getRow(i).get("task_start_time"));
				map.put("taskEndDatetime", blockData.getRow(i).get("task_end_time"));
				map.put("taskAreaContent", blockData.getRow(i).get("task_area_content"));
				map.put("taskContent", blockData.getRow(i).get("task_content"));
				map.put("uploadTime", blockData.getRow(i).get("upload_time"));
				map.put("uuid", blockData.getRow(i).get("plan_id"));
				insertList.add(map);
			}
			//获取数据库数据,判断是否有重复数据,有先去重再插入
			inInfo.set("weekDate", DvUtils.getWeekDate());
			List<Map<String,String>> uuidList = dao.query("DV05.queryWeekTask", inInfo.getAttr());
			//判断数据库是否有本周数据,没有就直接添加; 有先判断uuid是否重复,重复就不添加
			if (uuidList.size() == 0) {
				dao.insertBatch("DV05.insertKeyMonitoringTasks", insertList);
			} else {
				List newInsertList = new ArrayList();
				Map uuidMap = new HashMap();
				for (Map<String, String> key : uuidList) {
					uuidMap.put(key.get("uuid"), key.get("uuid"));
				}
				for (int i = 0; i < insertList.size(); i++) {
					String uuid = (String) insertList.get(i).get("uuid");
					if (!uuidMap.containsKey(uuid)) {
						newInsertList.add(insertList.get(i));
					}
				}
				dao.insertBatch("DV05.insertKeyMonitoringTasks", newInsertList);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("记录插入失败！" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("记录插入成功！");
		return outInfo;
	}

	/**
	 * 昨日各施工完成情况接口
	 * @param inInfo
	 * @return
	 */
	public EiInfo addConstructionCompletionStatus(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//传来的数据没有时间间隔类型和日期类型
			EiBlock blockData = inInfo.getBlock("result");
			for (int i = 0; i < blockData.getRows().size(); i++) {
				Map map = new HashMap();
				map.put("lineNumber", blockData.getRow(i).get("line_number"));
				map.put("interval", 410005);
				map.put("taskDate", blockData.getRow(i).get("task_date"));
				map.put("startDatetime", blockData.getRow(i).get("task_date"));
				map.put("endDatetime", blockData.getRow(i).get("task_date"));
				map.put("taskNumber", blockData.getRow(i).get("task_number"));
				map.put("taskStartDatetime", blockData.getRow(i).get("task_start_time"));
				map.put("taskEndDatetime", blockData.getRow(i).get("task_end_time"));
				map.put("actualStartTime", blockData.getRow(i).get("actual_start_time"));
				map.put("actualEndTime", blockData.getRow(i).get("actual_end_time"));
				map.put("uploadTime", blockData.getRow(i).get("upload_time"));
				map.put("uuid", UUID.randomUUID().toString());
				dao.insert("DV05.insertConstructionCompletionStatus", map);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("记录插入失败！" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("记录插入成功！");
		return outInfo;
	}

	/**
	 * 风险总数、隐患分级新增和完成数量接口
	 * @param inInfo
	 * @return
	 */
	public EiInfo addRiskPitfallCompleteTotal(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//传来的数据没有时间粒度和日期类型
			EiBlock blockData = inInfo.getBlock("result");
			for (int i = 0; i < blockData.getRows().size(); i++) {
				Map map = new HashMap();
				map.put("interval", 410005);
				map.put("startDatetime", blockData.getRow(i).get("start_datetime"));
				map.put("endDatetime", blockData.getRow(i).get("end_datetime"));
				map.put("safeRisk1", blockData.getRow(i).get("safe_risk_1"));
				map.put("safeRisk2", blockData.getRow(i).get("safe_risk_2"));
				map.put("safeRisk3", blockData.getRow(i).get("safe_risk_3"));
				map.put("safeRisk4", blockData.getRow(i).get("safe_risk_4"));
				map.put("generalPitfallAdd", blockData.getRow(i).get("general_pitfall_add"));
				map.put("majorPitfallAdd", blockData.getRow(i).get("major_pitfall_add"));
				map.put("generalPitfallDone", blockData.getRow(i).get("general_pitfall_done"));
				map.put("majorPitfallDone", blockData.getRow(i).get("major_pitfall_done"));
				map.put("uploadTime", blockData.getRow(i).get("upload_time"));
				dao.insert("DV05.insertRiskPitfallCompleteTotal", map);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("记录插入失败！" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("记录插入成功！");
		return outInfo;
	}

	/**
	 * 隐患、安全问题分类整改完成数量接口
	 * @param inInfo
	 * @return
	 */
	public EiInfo addRectificationQuantity(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//传来的数据没有时间粒度和日期类型
			EiBlock blockData = inInfo.getBlock("result");
			for (int i = 0; i < blockData.getRows().size(); i++) {
				Map map = new HashMap();
				map.put("interval", 410005);
				map.put("startDatetime", blockData.getRow(i).get("start_datetime"));
				map.put("endDatetime", blockData.getRow(i).get("end_datetime"));
				map.put("safety", blockData.getRow(i).get("safety_t"));
				map.put("countPitfall", blockData.getRow(i).get("count_pitfall"));
				map.put("countSafe", blockData.getRow(i).get("count_safe"));
				map.put("uploadTime", blockData.getRow(i).get("upload_time"));
				dao.insert("DV05.insertRectificationQuantity", map);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("记录插入失败！" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("记录插入成功！");
		return outInfo;
	}

	/**
	 * 隐患/安全问题分类信息接口
	 * @param inInfo
	 * @return
	 */
	public EiInfo addClassificationInformation(EiInfo inInfo) {
		EiInfo outInfo = new EiInfo();
		try {
			//传来的数据没有时间粒度和日期类型
			EiBlock blockData = inInfo.getBlock("result");
			for (int i = 0; i < blockData.getRows().size(); i++) {
				Map map = new HashMap();
				map.put("type", blockData.getRow(i).get("type"));
				map.put("name", blockData.getRow(i).get("name"));
				map.put("uploadTime", blockData.getRow(i).get("upload_time"));
				dao.insert("DV05.insertClassificationInformation", map);
			}
		} catch (Exception e) {
			outInfo.setStatus(EiConstant.STATUS_FAILURE);
			outInfo.setMsg("记录插入失败！" + e.getMessage());
			return outInfo;
		}
		outInfo.setStatus(EiConstant.STATUS_SUCCESS);
		outInfo.setMsg("记录插入成功！");
		return outInfo;
	}

}
