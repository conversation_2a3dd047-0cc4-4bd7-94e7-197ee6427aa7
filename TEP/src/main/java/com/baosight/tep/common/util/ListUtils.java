package com.baosight.tep.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 集合扩展工具类
 *
 * <AUTHOR>
 * @date 2023/02/06
 */
public class ListUtils {

    /**
     * 新建一个ArrayList
     *
     * @param list  列表
     * @param clazz clazz
     * @return {@link List}<{@link T}>
     */
    public static <T> List<T> toList(List<?> list, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSON.toJSONString(item, SerializerFeature.WriteNullStringAsEmpty),
                clazz, Feature.InitStringFieldAsEmpty)));
        return result;
    }

    /**
     * List「Object」转List「Map」
     *
     * @param list 列表
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    public static <T> List<Map<String, Object>> toListMap(List<T> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        list.forEach(item -> result.add(JSON.parseObject(JSONObject.toJSONString(item), new TypeReference<Map<String, Object>>() {
        })));
        return result;
    }

    /**
     * 构建器
     *
     * @return {@link ListBuilder}<{@link T}>
     */
    public static <T> ListBuilder<T> builder() {
        return builder(new ArrayList<>());
    }

    /**
     * 构建器
     *
     * @param clazz clazz
     * @return {@link ListBuilder}<{@link T}>
     */
    public static <T> ListBuilder<T> builder(Class<T> clazz) {
        return ListBuilder.create(clazz);
    }

    /**
     * 构建器
     *
     * @param list 列表
     * @return {@link ListBuilder}<{@link T}>
     */
    public static <T> ListBuilder<T> builder(List<T> list) {
        return new ListBuilder<>(list);
    }

}
