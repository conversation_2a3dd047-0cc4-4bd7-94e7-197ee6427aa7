package com.baosight.app.xw.service;


import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baosight.app.common.CYUtils;
import com.baosight.app.common.RedisConstants;
import com.baosight.app.common.util.DateUtils;
import com.baosight.app.common.util.EplatUtil;
import com.baosight.app.common.util.eiinfo.EiInfoUtils;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ei.parser.EiInfoParserFactory;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.impl.ServiceEPBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import redis.clients.jedis.Jedis;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


public class ServiceXW01 extends ServiceEPBase {

    private static final Logger logger = LoggerFactory.getLogger(ServiceXW01.class);

    /////////////redis相关/////////////////////
    //引用 RedisTemplate
    private static RedisTemplate<String,Object> redisTemplate = (RedisTemplate) PlatApplicationContext.getApplicationContext().getBean("redisTemplate");
    public static String redisHost = PlatApplicationContext.getProperty("spring.redis.host");
    private static int redisPort = Integer.parseInt(PlatApplicationContext.getProperty("spring.redis.port"));
//    public static String redisPassword = PlatApplicationContext.getProperty("spring.redis.password");

    /////////////redis相关/////////////////////


    /**
     * 查询最新运营日报附件信息
     *  本地系统微服务：S_APP_NN_12
     * @param inInfo
     * @return
     */
    public EiInfo getFileInfo(EiInfo inInfo){
        List<Map<String,String>> query = dao.query("XW01.queryReportInfo", new HashMap<>());
        if (query.size()>0){
            String filePath = query.get(0).get("filePath");
            JSONObject jsonObject = JSONObject.parseObject(filePath);
            inInfo.set("result",jsonObject);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }else{
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("获取数据失败！");
        }
        return inInfo;
    }

    /**
     * 客流监察--客流查询
     *  外部系统后台微服务：S_NOCC_KM_DA_0401
     *  本地系统后台微服务：S_APP_NN_08
     * @param inInfo
     * @return
     * @throws Exception
     */
    public EiInfo getKLJCBaseInfo(EiInfo inInfo) {
//        EiInfo outInfo = eplatService(inInfo, "app2");
        String jsonStr = JSONUtil.toJsonStr(inInfo.get("data"));
        EiInfo inInfo1 = EiInfoParserFactory.getParser("json").parse(jsonStr);
        EiInfo outInfo = EiInfoUtils.callParam(inInfo.getString("urlId"), inInfo1).build();
        return outInfo;
    }

    /**
     * APP后台serviceId:S_APP_NN_00
     * 微服务调用(通用方法)
     * @param inInfo
     * @return
     */
    public EiInfo callService(EiInfo inInfo){
        String jsonStr = JSONUtil.toJsonStr(inInfo.get("data"));
        EiInfo inInfo1 = EiInfoParserFactory.getParser("json").parse(jsonStr);
        return EiInfoUtils.callParam(inInfo1.get("serviceId").toString(), inInfo1).build();
    }
    
    /**
     * 月度客流对比
     * 定时器 TASK_APP_YDDB01
     * 微服务 S_APP_NN_28
     * 任务编号 TASK_APP_NN_05
     * 0 55 4 * * ?
     * @param inInfo
     * @return
     */
    public EiInfo monthPassengerContrast(EiInfo inInfo){
        // 创建一个 Map 来表示数据结构
        Map<String, Object> data = new HashMap<>();
        Map<String, String> firstDayOfLastMonthAndToday = DateUtils.getFirstDayOfLastMonthAndToday();
        // 设置 serviceId
        data.put("serviceId", "S_TEP_APP_01");
        data.put("command", "month_passenger_contrast");
        List<Map<String, Object>> list = new ArrayList<>();
        // 创建 params Map
        Map<String, Object> params1 = new HashMap<>();
        params1.put("k", "start_date");
        params1.put("v", firstDayOfLastMonthAndToday.get("start_date"));
        list.add(params1);
        // 创建 params Map
        Map<String, Object> params2 = new HashMap<>();
        params2.put("k", "end_date");
        params2.put("v", firstDayOfLastMonthAndToday.get("end_date"));
        list.add(params2);
        // 将 params 放入 data
        data.put("conditions", list);
        inInfo.set("data",data);
        EiInfo app2 = EplatUtil.getInstance().eplatService(inInfo, "app2");
        Map attr = app2.getAttr();
        if(isRedisAvailable(redisHost,redisPort)){
            redisTemplate.opsForValue().set(RedisConstants.month_passenger_contrast, JSON.toJSONString(attr));
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }


    /**
     * 客流预测轮询5min
     * 微服务：S_APP_NN_27
     * 触发器编号：TASK_APP_KLJC01
     * 定时任务：TASK_APP_NN_04
     * @param inInfo
     * @return
     */
    public EiInfo sAppNn065min(EiInfo inInfo){
        // 创建一个 Map 来表示数据结构
        Map<String, Object> data = new HashMap<>();
        // 设置 serviceId
        data.put("serviceId", "S_KY_NC_08");
        // 创建 params Map
        Map<String, Object> params = new HashMap<>();
        params.put("time", DateUtils.getNearestMultipleOf5Minutes()); // 获取最近的5分钟倍数时间
        // 将 params 放入 data
        data.put("params", params);
        String jsonStr = JSONUtil.toJsonStr(data);
        EiInfo inInfo1 = EiInfoParserFactory.getParser("json").parse(jsonStr);
        EiInfo outInfo = new EiInfo();
        EiInfo eiInfo = CYUtils.queryLine(outInfo);
        eiInfo.set("dateTime",CYUtils.getCurrentNow());
        outInfo = EiInfoUtils.callParam("S_APP_NN_07", inInfo1).build();
        if(isRedisAvailable(redisHost,redisPort)){
            Map<String, Object> map1 = new HashMap<>();
            map1.put("outInfo",outInfo.getBlock("result").getRows());
            map1.put("eiInfo",eiInfo.getBlock("result").getRows());
            map1.put("dateTime",CYUtils.getCurrentNow());
            redisTemplate.opsForValue().set(RedisConstants.sAppNn065min, JSON.toJSONString(map1));
        }
        inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        return inInfo;
    }

    /**
     * 客流预测
     *  外部系统后台微服务：S_KY_NC_08
     *  本地系统后台微服务：S_APP_NN_06
     * @param inInfo
     * @return
     * @throws Exception
     */
    public EiInfo getKLYCBaseInfo(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        EiInfo eiInfo = new EiInfo();;
        try {

            //从redis里获取，如果没有相关数据，走一遍setBasicInfo，再获取
            if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.sAppNn065min)){
                Object o = redisTemplate.opsForValue().get(RedisConstants.sAppNn065min);
                String jsonStr = JSONUtil.toJsonStr(o);
                EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                Map<String,Object> attr = json.getAttr();
                List dataOutInfo = (ArrayList)attr.get("outInfo");
                List data = (ArrayList)attr.get("eiInfo");
                outInfo.addBlock("result").setRows(dataOutInfo);
                eiInfo.addBlock("result").setRows(data);
                outInfo.set("cacheFlat","true");//缓存标识
            }else{
                eiInfo = CYUtils.queryLine(outInfo);
                String jsonStr = JSONUtil.toJsonStr(inInfo.get("data"));
                EiInfo inInfo1 = EiInfoParserFactory.getParser("json").parse(jsonStr);
                outInfo = EiInfoUtils.callParam("S_APP_NN_07", inInfo1).build();
            }


            List<Map<String, String>> result = eiInfo.getBlock("result").getRows();
            List<Map<String, String>> list = outInfo.getBlock("result").getRows();
            List<Map<String, String>> maps = list.stream().map(data -> {
                String lineName = result.stream().filter(fil -> data.get("line_id").equals(fil.get("line_id"))).map(map -> {
                    return map.get("line_cname");
                }).collect(Collectors.joining());
                data.put("line_name", lineName);
                return data;
            }).collect(Collectors.toList());
            outInfo.addBlock("result").setRows(maps);
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            return outInfo;
        }catch (Exception e){
            e.printStackTrace();
            logger.error("获取客流预测数据失败，原因{}",e.getMessage());
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return outInfo;
    }

    /**
     * 获取线网基础数据
     * 本地后台微服务：S_APP_NN_01
     * 外部系统微服务：D_NOCC_BASE_LINE_INFO
     * @param inInfo
     * @return
     * @throws Exception 获取基础数据中如下数据：
     *  ①、线路、车站信息基本信息
     *  ②、运营里程、运营车站数量、换乘站数量
     */
    public EiInfo getXWBaseInfo(EiInfo inInfo) throws Exception {
        boolean redisAvailable = isRedisAvailable(redisHost, redisPort);//true-redis 可用，false-redis不可用
        EiInfo outInfo = new EiInfo();
        if(redisAvailable){
//            Optional.ofNullable(redisTemplate.opsForValue().get(RedisConstants.REDISKEY_STAINFO_APP)).ifPresent(data -> {
////                outInfo.addBlock("stationInfo").setRows(JSON.parseArray(data.toString(), Map.class));
//            });
            // redisTemplate.opsForValue().get(RedisConstants.REDISKEY_STAINFO_APP) == null
        }
        outInfo = EplatUtil.getInstance().eplatService(inInfo, "app1");
//        redisTemplate.opsForValue().set(RedisConstants.REDISKEY_STAINFO_APP,new HashMap<>());
//        redisTemplate.expire(RedisConstants.REDISKEY_STAINFO_APP, 7, TimeUnit.DAYS);
        return outInfo;
    }


    /**
     * 获取与指标系统数据
     * 本地后台微服务：S_APP_NN_02
     * 外部系统微服务：S_TEP_APP_01 指标
     * 外部系统微服务：S_NOCC_LSV_01 大屏
     *
     * @param inInfo
     * @return
     * @throws Exception 获取指标系统如下数据：
     *  ①、各线路上线列数、最小行车间隔、线路运行图编号
     *  ②、线网峰值客运量、峰值日期、昨日单程票比例、本年度已完成客运指标
     *  ③、客流趋势（线网客运量）
     *  ④、历史最大客流-线网历史客运、车站历史客运排行
     *  ⑤、节假日分析-节假日信息
     *  ⑥、节假日分析-同比去年历史客运、本年节假日线网客运、本年节假日车站客运排行
     *  ⑦、运营报表
     */
    public EiInfo getMetricsInfo(EiInfo inInfo) throws Exception {
        EiInfo outInfo = new EiInfo();
        Jedis jedis = new Jedis(redisHost, redisPort);
        Map<String,Object> map = (HashMap) inInfo.get("data");
        switch (map.get("command").toString()) {
            case "month_passenger_contrast":
                if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.month_passenger_contrast)) {
                    Object o = redisTemplate.opsForValue().get(RedisConstants.month_passenger_contrast);
                    String jsonStr = JSONUtil.toJsonStr(o);
                    EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                    outInfo.setAttr(json.getAttr());
                }else{
                    outInfo = EplatUtil.getInstance().eplatService(inInfo, "app2");
                }
                break;
            case "passenger_volume":
                List conditionsObj =(ArrayList) map.get("conditions");

                if (conditionsObj instanceof ArrayList) {
                    List<Map<String, String>> conditionsList = (List<Map<String, String>>) conditionsObj;
                    String targetKey = "param_type";
                    String redisValue = getValueByKey(conditionsList, targetKey);

                    if (redisValue != null) {
//                        String redisValue = getRedisValue(result);
                        if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.passenger_Volume)) {
                            Object o = redisTemplate.opsForValue().get(RedisConstants.passenger_Volume);
                            String jsonStr = JSONUtil.toJsonStr(o);
                            EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                            Map<String,Object> attr = json.getAttr();
                            outInfo.set("data",attr.get(redisValue));
                        }else{
                            outInfo = EplatUtil.getInstance().eplatService(inInfo, "app2");
                        }
                    } else {
                        outInfo = EplatUtil.getInstance().eplatService(inInfo, "app2");
                    }
                    outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                }
                break;
            case "online_headway":
                if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.Online_HeadWay_Data)) {
                    Object o = redisTemplate.opsForValue().get(RedisConstants.Online_HeadWay_Data);
                    String jsonStr = JSONUtil.toJsonStr(o);
                    EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                    Map<String,Object> attr = json.getAttr();
                    outInfo.setAttr(attr);
                }else{
                    outInfo = EplatUtil.getInstance().eplatService(inInfo, "app2");
                }
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                break;
            case "peak_value":

                if(isRedisAvailable(redisHost,redisPort) && jedis.exists(RedisConstants.Peak_Value)) {
//                    Object o = redisTemplate.opsForValue().get(RedisConstants.Peak_Value);
//                    String jsonStr = JSONUtil.toJsonStr(o);
                    String jsonStr = jedis.get(RedisConstants.Peak_Value);
                    EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                    Map<String,Object> attr = json.getAttr();
                    outInfo.setAttr(attr);
                }else{
                    outInfo = EplatUtil.getInstance().eplatService(inInfo, "app2");
                }
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);

                break;
            case "large_screen":
                if(isRedisAvailable(redisHost,redisPort) && jedis.exists(RedisConstants.Large_screen)) {
                    String jsonStr = jedis.get(RedisConstants.Large_screen);
                    EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                    Map<String,Object> attr = json.getAttr();
                    outInfo.setAttr(attr);
                }else{
                    outInfo = EplatUtil.getInstance().eplatService(inInfo, "app2");
                }
                outInfo.setStatus(EiConstant.STATUS_SUCCESS);
                break;
            default:
                outInfo = EplatUtil.getInstance().eplatService(inInfo, "app2");
        }
        jedis.close();
        return outInfo;
    }

    // 根据 k 获取对应的 v
    public static String getValueByKey(List<Map<String, String>> conditions, String key) {
        for (Map<String, String> map : conditions) {
            String k = map.get("k");
            String v = map.get("v");
            if (key.equals(k)) {
                return v;  // 找到目标键时返回对应的值
            }
        }
        return null;  // 如果没有找到对应的键，返回 null
    }

    /**
     * 当日客流统计图数据
     *
     * @param inInfo
     * @return
     */
    private EiInfo getLineValue(EiInfo inInfo) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            for (int i = 1; i <= 5; i++) {
                Map<String, Object> map = new HashMap<>();
                map.put("name", i + "号线");
                map.put("line_id", "000000" + i);
                map.put("sum_in", "000000" + i);
                map.put("sum_out", "000000" + i);
                map.put("sum_trans", "000000" + i);
                map.put("update_datetime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                map.put("count_sum", Math.round(Math.random() * (1000000 - 5000) + 100000));
                list.add(map);
            }
            inInfo.addBlock("result").setRows(list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg("error");
        }

        return inInfo;
    }


    /**
     * 测试redis是否可用
     * @param host redisIP
     * @param port redis 端口
     * @return true-可用，false-不可用
     */
    public static boolean isRedisAvailable(String host, int port) {
        try (Jedis jedis = new Jedis(host, port)) {
            return "PONG".equals(jedis.ping());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 客流总览-车站进站量客流接口和区间断面拥挤度接口
     * 微服务号：S_APP_NN_15
     * 根据command判断调用对应接口
     *  "command": "passenger_section_waring",客流总览进站量、断面拥挤度预警接口
     *  "command": "emergency_materials",查询对应线路应急物资接口
     *  "command": "this_day_max_section_passenger",查询获取当日最大断面客流信息接口
     */
    public EiInfo getKLZLInfo(EiInfo inInfo) throws Exception {
        EiInfo outInfo = new EiInfo();
        String m = (String) inInfo.get("command");
//        Map map = (Map) inInfo.get("params");

        if(m.equals("emergency_materials")){
            Jedis jedis = new Jedis(redisHost, redisPort);
            //应急物资接口
            if(isRedisAvailable(redisHost,redisPort) && jedis.exists(RedisConstants.Emergency_materials)) {
                String jsonStr = jedis.get(RedisConstants.Emergency_materials);
                EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                Map<String,Object> attr = json.getAttr();
                outInfo.setAttr(attr);

            }else{
                //"conditions": [{"k": "line_number","v": "0000000000",}]参数：线路编码
                List<Map<String,Object>> list = (List<Map<String, Object>>) inInfo.get("conditions");
                if(list.size()>0){
                    for (int i=0;i<list.size();i++){
                        if("line_number".equals(list.get(i).get("k"))){
                            inInfo.set("lineCode",list.get(i).get("v"));
                        }else if("sta_number".equals(list.get(i).get("k"))){
                            inInfo.set("positionId",list.get(i).get("v"));
                        }
                    }
                }
                outInfo = EiInfoUtils.callParam("S_YJ_ZY_06", inInfo).build();
            }
                String sta_id = Optional.ofNullable(inInfo.get("number")).map(Object::toString).orElse("");
                Map<String,Object> yjwzMap = new HashMap<>();
                List<Map<String,Object>> yjwzList = (List<Map<String, Object>>) outInfo.get("data");
                for (Map<String, Object> item : yjwzList) {
                    if (item.get("sta_id").equals(sta_id)) {
                        yjwzMap.put("sta_longitude", item.get("sta_longitude"));
                        yjwzMap.put("lineMap", item.get("lineMap"));
                        yjwzMap.put("sta_id", item.get("sta_id"));
                        yjwzMap.put("sta_dimension", item.get("sta_dimension"));
                        yjwzMap.put("sta_cname", item.get("sta_cname"));
                        break;
                    }
                }
                yjwzList.clear();
                yjwzList.add(yjwzMap);
                outInfo.set("data",yjwzList);
        }else if(m.equals("passenger_section_waring")){
//            "params": {"startTime": "09:00:00","endTime": "09:30:00","warnType": "进站量","interval": 410003}
            //客流总览进站量、断面拥挤度预警接口

            if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.Passenger_section_waring)) {
                Object o = redisTemplate.opsForValue().get(RedisConstants.Passenger_section_waring);
                String jsonStr = JSONUtil.toJsonStr(o);
                EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                Map<String,Object> attr = json.getAttr();
                outInfo.setAttr(attr);
            }else{
                outInfo = EiInfoUtils.callParam("S_NOCC_KM_DV_0101", inInfo).build();
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }else if(m.equals("this_day_max_section_passenger")){
            //查询获取当日最大断面客流信息接口

            if(isRedisAvailable(redisHost,redisPort) && redisTemplate.hasKey(RedisConstants.This_day_max_section_passenger)) {
                Object o = redisTemplate.opsForValue().get(RedisConstants.This_day_max_section_passenger);
                String jsonStr = JSONUtil.toJsonStr(o);
                EiInfo json = EiInfoParserFactory.getParser("json").parse(jsonStr);
                Map<String,Object> attr = json.getAttr();
                outInfo.setAttr(attr);
            }else{
                outInfo = EiInfoUtils.callParam("S_NOCC_KM_DV_0309", inInfo).build();
            }
            outInfo.setStatus(EiConstant.STATUS_SUCCESS);
            return outInfo;
        }else {
            inInfo.setMsg("command参数不符合本接口需要参数！");
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }

        return outInfo;
    }

    /**
     * CCTV视频服务接口
     *  本地系统后台微服务：S_APP_NN_20
     *  command:cctv_tree 获取视频树地址
     * @param inInfo
     */
    public EiInfo getCCTVservice(EiInfo inInfo) {
        EiInfo outInfo = new EiInfo();
        String command = (String) inInfo.get("command");
        if("cctv_tree".equals(command)){
            String regionId = (String) inInfo.get("regionId");
            String mediaIpPort = (String) inInfo.get("mediaIpPort");
            if (isNullOrEmpty(regionId) || isNullOrEmpty(mediaIpPort)) {
                outInfo.setMsg("regionId或者mediaIpPort数据格式不对，请检查！");
                outInfo.setStatus(-1);
                return outInfo;
            }
            outInfo = EiInfoUtils.callParam("S_APP_NN_21", inInfo).build();
            List<Map<String,Object>> list = (List<Map<String, Object>>) outInfo.get("treeGroup");
            if(list == null || list.isEmpty()){
                outInfo.setMsg("没有查询到视频树地址数据，请检查！");
                outInfo.setStatus(-1);
                return outInfo;
            }else {
                outInfo.setMsg("视频树查询成功！");
                outInfo.setStatus(1);
                outInfo.set("treeGroup",list);
            }
        }else if("query_camera_url".equals(command)){
            outInfo = EiInfoUtils.callParam("S_APP_NN_22", inInfo).build();
        }else {
            inInfo.setMsg("command参数不符合本接口需要参数！");
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            return inInfo;
        }
        return outInfo;
    }

    private boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }

}