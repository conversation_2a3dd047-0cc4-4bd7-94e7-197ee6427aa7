package com.baosight.menu.bi.dr.sse.util;

import cn.hutool.core.util.StrUtil;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.menu.common.model.Cache;
import com.baosight.menu.common.model.LocalCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * SSE服务器发送工具类
 *
 * <AUTHOR>
 * @Description： SSE 服务器发送事件
 * @date 2022/10/26
 */
@Slf4j
@Component
public class SseEmitterUtils {
    private static String cacheKey = PlatApplicationContext.getProperty("model.cache.sse");

    /**
     * 当前连接数
     */
    private static AtomicInteger count = new AtomicInteger(0);
    /**
     * 存储 SseEmitter 信息
     */
    private static Cache<SseEmitter> sseEmitterMap = createCache();


    private static Cache<SseEmitter> createCache() {
        return new LocalCache<>(cacheKey);
    }

    /**
     * 连接
     *
     * @param key 关键
     * @return {@link SseEmitter}
     * @Description： 创建用户连接并返回 SseEmitter
     */
    public static SseEmitter connect(String key) {
        if (sseEmitterMap.containsKey(key)) {
            return sseEmitterMap.get(key);
        }

        try {
            // 设置超时时间，0表示不过期。默认30秒
            SseEmitter sseEmitter = new SseEmitter(3600000L);
            // 注册回调
            sseEmitter.onCompletion(completionCallBack(key));
            sseEmitter.onError(errorCallBack(key));
            sseEmitter.onTimeout(timeoutCallBack(key));
            sseEmitterMap.put(key, sseEmitter);
            // 数量+1
            count.getAndIncrement();
            return sseEmitter;
        } catch (Exception e) {
            log.info("创建新的SSE连接异常，当前连接Key为：{}", key);
        }
        return null;
    }

    /**
     * 发送消息
     *
     * @param key     关键
     * @param message 消息
     * @Description： 给指定用户发送消息
     */
    public static void sendMessage(String key, String message) {
        if (sseEmitterMap.containsKey(key)) {
            try {
                sseEmitterMap.get(key).send(message);
            } catch (IOException e) {
                log.error("用户[{}]推送异常:{}", key, e.getMessage());
                remove(key);
            }
        }
    }

    /**
     * 组发送消息
     *
     * @param groupId 组id
     * @param message 消息
     * @Description： 向同组人发布消息，要求：key + groupId
     */
    public static void groupSendMessage(String groupId, String message) {
        if (sseEmitterMap.isNotEmpty()) {
            sseEmitterMap.forEach((k, v) -> {
                try {
                    if (k.startsWith(groupId)) {
                        v.send(message, MediaType.APPLICATION_JSON);
                    }
                } catch (IOException e) {
                    log.error("用户[{}]推送异常:{}", k, e.getMessage());
                    remove(k);
                }
            });
        }
    }

    /**
     * 批量发送信息
     *
     * @param message 消息
     * @Description： 广播群发消息
     */
    public static void batchSendMessage(String message) {
        sseEmitterMap.forEach((k, v) -> {
            try {
                v.send(message, MediaType.APPLICATION_JSON);
            } catch (IOException e) {
                log.error("用户[{}]推送异常:{}", k, e.getMessage());
                remove(k);
            }
        });
    }

    /**
     * 批量发送信息
     *
     * @param message 消息
     * @param ids     id
     * @Description： 群发消息
     */
    public static void batchSendMessage(String message, Set<String> ids) {
        ids.forEach(userId -> sendMessage(userId, message));
    }

    /**
     * 删除
     *
     * @param key 关键
     * @Description： 移除连接
     */
    public static String remove(String key) {
        if (sseEmitterMap.containsKey(key)) {
            sseEmitterMap.remove(key);
            // 数量-1
            count.getAndDecrement();
            log.info("移除连接：{}", key);
            return StrUtil.format("移除连接：{}", key);
        }
        return StrUtil.format("已移除连接：{}", key);
    }

    /**
     * 获取当前连接信息
     *
     * @return {@link List}<{@link String}>
     */
    public static List<String> getIds() {
        return new ArrayList<>(sseEmitterMap.keySet());
    }

    /**
     * 获取当前连接数量
     *
     * @return int
     */
    public static int getCount() {
        return count.intValue();
    }


    /**
     * 心跳检测
     */
    @Scheduled(fixedRate = 30000)
    public static void heartbeatCheck() {
        sseEmitterMap.forEach((key, sseEmitter) -> {
            try {
                // 发送心跳消息
                sseEmitter.send("heartbeat");
                log.info("心跳检测正常: {}", key);
            } catch (IOException e) {
                log.error("心跳检测异常: {}", e.getMessage());
                remove(key);
            }
        });
    }

    private static Runnable completionCallBack(String key) {
        return () -> {
            log.info("结束连接：{}", key);
            remove(key);
        };
    }

    private static Runnable timeoutCallBack(String key) {
        return () -> {
            log.info("连接超时：{}", key);
            remove(key);
        };
    }

    private static Consumer<Throwable> errorCallBack(String key) {
        return throwable -> {
            log.info("连接异常：{}", key);
            remove(key);
        };
    }

}

