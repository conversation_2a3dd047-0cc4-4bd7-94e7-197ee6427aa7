package com.baosight.menu.bi.dr.sse.service;

import java.util.concurrent.*;

/**
 * 任务调度器类，用于定时执行任务
 *
 * <AUTHOR>
 */
public class SseTaskScheduler {
    /**
     * 初始延迟时间，单位为毫秒
     */
    private static final long INITIAL_DELAY = 0L;
    /**
     * 延迟时间的增量，单位为毫秒
     */
    private static final long DELAY_INCREMENT = 100L;
    /**
     * 最大延迟时间，单位为毫秒
     */
    private static final long MAX_DELAY = 5000L;

    private ScheduledExecutorService executor;
    private long delayIncrement;
    private long maxDelay;
    private long currentDelay;

    /**
     * 构造一个SseTaskScheduler实例，使用指定的初始延迟时间和延迟时间增量。
     *
     * @param initialDelay   初始延迟时间，单位为毫秒
     * @param delayIncrement 延迟时间的增量，单位为毫秒
     * @param maxDelay       最大延迟时间，单位为毫秒
     */
    public SseTaskScheduler(long initialDelay, long delayIncrement, long maxDelay) {
        this.executor = createThreadPool();
        this.delayIncrement = delayIncrement;
        this.maxDelay = maxDelay;
        this.currentDelay = initialDelay;
    }

    /**
     * 提交任务到调度器中进行调度执行。
     *
     * @param task 要执行的任务
     */
    public void scheduleTask(Runnable task) {
        executor.schedule(task, currentDelay, TimeUnit.MILLISECONDS);
        updateDelay();
    }

    private void updateDelay() {
        currentDelay += delayIncrement;
        if (currentDelay > maxDelay) {
            currentDelay = maxDelay;
        }
    }

    /**
     * 关闭调度器。
     */
    public void shutdown() {
        executor.shutdown();
    }

    /**
     * 创建一个默认的SseTaskScheduler实例，使用初始延迟时间和延迟时间增量的默认值。
     *
     * @return SseTaskScheduler实例
     */
    public static SseTaskScheduler createScheduler() {
        return new SseTaskScheduler(INITIAL_DELAY, DELAY_INCREMENT, MAX_DELAY);
    }

    /**
     * 创建一个自定义初始延迟时间和延迟时间增量的SseTaskScheduler实例。
     *
     * @param initialDelay   初始延迟时间，单位为毫秒
     * @param delayIncrement 延迟时间的增量，单位为毫秒
     * @param maxDelay       最大延迟时间，单位为毫秒
     * @return SseTaskScheduler实例
     */
    public static SseTaskScheduler createScheduler(long initialDelay, long delayIncrement, long maxDelay) {
        return new SseTaskScheduler(initialDelay, delayIncrement, maxDelay);
    }

    /**
     * 创建线程池
     *
     * @return 线程池对象
     */
    private static ScheduledExecutorService createThreadPool() {
        // 核心线程数
        int corePoolSize = 1;
        // 使用默认的线程工厂
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        // 使用丢弃策略
        RejectedExecutionHandler handler = new ThreadPoolExecutor.DiscardPolicy();
        return new ScheduledThreadPoolExecutor(corePoolSize, threadFactory, handler);
    }

}
