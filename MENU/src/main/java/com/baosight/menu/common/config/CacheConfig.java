package com.baosight.menu.common.config;

import com.baosight.iplat4j.core.cache.CacheRegistry;
import com.baosight.iplat4j.core.cache.impl.RedisTempleteCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存配置
 *
 * <AUTHOR>
 * @date 2023/07/27
 */
@Configuration
public class CacheConfig {
    private static final String CACHE_TYPE_REDIS = "redis";
    private static final String CACHE_TYPE_LOCAL = "local";

    @Value("${iplat.core.cache.type}")
    private String cacheType;

    @Value("${iplat.core.cache.redisExpireTime}")
    private long redisExpireTime;

    @Value("${model.cache}")
    private String cacheKey;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private CacheRegistry cacheRegistry;

    @PostConstruct
    public void init() {
        cacheRegistry = new CacheRegistry();
        cacheRegistry.setCacheKey(cacheKey);
        if (CACHE_TYPE_REDIS.equalsIgnoreCase(cacheType)) {
            RedisTempleteCache menuRedisCache = redisCache();
            cacheRegistry.setCache(menuRedisCache);
        } else if (CACHE_TYPE_LOCAL.equalsIgnoreCase(cacheType)) {
            ConcurrentHashMap<String, Object> menuLocalCache = localCache();
            cacheRegistry.setCache(menuLocalCache);
        }
    }

    @Bean
    public ConcurrentHashMap<String, Object> localCache() {
        return new ConcurrentHashMap<>(16);
    }

    @Bean
    public RedisTempleteCache redisCache() {
        RedisTempleteCache cache = new RedisTempleteCache();
        cache.setCacheEname(cacheKey);
        cache.setExpireTime(redisExpireTime);
        cache.setRedisTemplate(redisTemplate);
        return cache;
    }

    @Bean
    public CacheRegistry cacheRegistry() {
        return cacheRegistry;
    }
}
