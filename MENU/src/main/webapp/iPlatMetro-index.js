const rootId = "NN";//要显示的菜单的根节点

;(function ($, _) {
        var trim = $.trim,
            extend = $.extend;

        var isObject = IPLAT.isObject || function (obj) {
            return null !== obj && typeof obj === 'object';
        };

        var _defaultOptions = {
            menu: true,  // 左侧菜单
            pageSearch: true, // 页面号查询
            logout: true,//注销登录
            lockScreen: true,//锁屏
            // favorite: true, // 收藏页面
        };

        var V6Index = function (options) {
            if (this instanceof V6Index) {
                this._options = extend({}, _defaultOptions, options);
                this.init();
            } else {
                return new V6Index(options);
            }
        };

        // 调用各个模块
        V6Index.prototype.init = function () {
            var _options = this._options,
                that = this,
                key;
            var keys = _.keys(_options);

            for (var i = 0, length = keys.length; i < length; i++) {
                key = keys[i];
                if (key && _options[key] && _.isFunction(that[key])) {
                    // 防止页面JS报错，导致页面无法继续执行
                    try {
                        that[key].call(that);
                    } catch (e) {
                        console.error(e);
                    }
                }
            }
        };

        // 轨道线网上侧菜单渲染
        V6Index.prototype.menu = function () {
            let ei = new EiInfo();
            ei.set("root", rootId);

            const his_color = "#03E7A4", his_not_color = "#aaa";
            // 构建menu
            ajaxPost("BIDR56", "queryMenu", ei, (response) => {
                let data = response.get("menu");
                let $navBox = $('#navBox');
                let offsets = [];
                for (let i = 0; i < data.length; i++) {
                    if (!data[i].nodeParam.trim()) break;
                    offsets.push($.extend({}, {id: data[i].label}, JSON.parse(data[i].nodeParam)));//菜单配置，设置二级菜单偏移
                }
                data.splice(5, 0, {});//添加空数据，用于菜单分隔
                const event = "hover", selfHeight = true; //hover

                $navBox.bxNavbar({
                    skin: "cocc",
                    rootID: rootId,
                    data: data,
                    event: event, //用于二级菜单显示方式
                    selfHeight: selfHeight,//高度自适应
                    offset: offsets,//二级菜单偏移 菜单配置 参数：{[left],[top]}
                    onSelect: function (vl) {
                        const bool = openPage(vl);
                        if (!bool) return bool;//阻止点击一级菜单添加历史记录

                        ph.visit(vl.label);//添加历史记录
                        $(".go-back>svg").css("color", his_color).addClass("scale");
                        $(".forward>svg").css("color", his_not_color).removeClass("scale");
                    }
                });

                //登录成功，默认打开页面
                /**
                 *  2022/6/15
                 * 一机多屏时默认打开页面
                 * */
                let viceCode = IPLAT.getParameterByName("code");
                let code = !!viceCode ? viceCode : $("[bx-select=true]").attr("bx-id");
                let ph = new PageHistory(code); //历史记录对象初始
                if (!!code) {
                    openHistoryPage($navBox, code);
                }

                //上一页
                $(".go-back").on("click", function () {
                    if (!ph.currentIndex) return false;
                    ph.goBack();
                    if (ph.currentIndex > 0 && ph.currentIndex < ph.queue.length) {
                        $(".go-back>svg").css("color", his_color).addClass("scale");
                        $(".forward>svg").css("color", his_color).addClass("scale");
                    } else {
                        $(".go-back>svg").css("color", his_not_color).removeClass("scale");
                        $(".forward>svg").css("color", his_color).addClass("scale");
                    }
                    openHistoryPage($navBox, ph.current);
                });

                //下一页
                $(".forward").on("click", function () {
                    if (ph.currentIndex === (ph.queue.length - 1)) return false;
                    ph.forward();
                    if (ph.currentIndex > 0 && ph.currentIndex < ph.queue.length - 1) {
                        $(".go-back>svg").css("color", his_color).addClass("scale");
                        $(".forward>svg").css("color", his_color).addClass("scale");
                    } else {
                        $(".go-back>svg").css("color", his_color).addClass("scale");
                        $(".forward>svg").css("color", his_not_color).removeClass("scale");
                    }
                    openHistoryPage($navBox, ph.current);
                });
            });

            //打开页面
            let openPage = (data) => {
                let url = '';
                /*
                * begin
                * 阻止一级菜单点击事件触发
                * 阻止含有子节点的二级菜单点击事件触发
                * */
                if (data.level === "1") {
                    return false;
                }
                if (data.level === "2" && !!data?.children.length) {
                    return false;
                }
                /*end*/

                if (/\s+/g.test(data.nodeUrl)) {
                    url = data.nodeUrl.trim();
                    console.warn(`The node "${data.label}" URL path contains Spaces`);
                } else {
                    url = data.nodeUrl;
                }
                //url为空，打开默认页面
                url = !!url ? url : `./static/develop.html?pageCname=${data.text}`;

                //配置打开菜单测试页面
                if (!!data.nodeParam.trim() && JSON.parse(data.nodeParam).test) {
                    url = `${IPLATUI.CONTEXT_PATH}/web/${data.label}`;
                }

                //判断打开页面是否存在，不存在跳转404页面
                $.ajaxSettings.timeout = '3000';
                $.get(url, {}).error(function (err) {
                    if (err.status === 404 || err.statusText === "timeout") {
                        url = `./static/404.html?pageCname=${data.text}&label=${data.label}&nodeUrl=${data.nodeUrl}`;
                        if (!$iframe.hasClass("frame-container")) {
                            $iframe.addClass("frame-container");
                        }
                        $('#contentFrame')[0].contentWindow.location.replace(encodeURI(url));
                    }
                });

                //获取打开页面类型
                let modal = getPageModal(url);
                let $iframe = $(".contentFrame");

                //iplat画面大小：1920*948 / 其余类型页面画面：1835*900
                modal === 1 ? $iframe.removeClass("frame-container") : $iframe.addClass("frame-container"); //iframe大小调整

                //iplat、iplat4j画面单点登录
                if (modal === 1 || modal === 2) {
                    url = `${url}?p_username=${localStorage.getItem("loginName")}&p_password=admin123&p_authen=CasRPlatAuth`;
                }

                $('#contentFrame')[0].contentWindow.location.replace(encodeURI(url));
                return true;
            };

            //获取页面类型
            let getPageModal = (url) => {
                let modal = -1;//画面打开方式；1.iplat;2.iplat4j;3.exe
                url = url.split('?')[0];
                if (url.indexOf("/iplat/") !== -1) {
                    modal = 1
                } else if (url.indexOf("COCCCCTV") !== -1) {
                    modal = 3
                } else if (url.indexOf("/static/") !== -1) {
                    modal = 4
                } else if (url.includes("/web/")) {
                    modal = 2
                } else {
                    modal = 0
                }
                return modal;
            };

            //打开历史页面
            let openHistoryPage = (element, pageCode) => {
                element.bxNavbar("select", {"id": pageCode, "onSelect": false});
                let data = $(".bx-this").data("nodeItem");
                data !== undefined && openPage(data);//pageCode存在时打开页面
            };

            //设置上一页/下一页默认样式
            $(".go-back>svg").css("color", his_not_color);
            $(".forward>svg").css("color", his_not_color);
        };

        // 页面号查询功能
        V6Index.prototype.pageSearch = function () {
            var v6Index = this;
            // 防止抖动
            var filterChinese = _.debounce(function (td) {
                td.value = td.value.replace(/[\u4e00-\u9fa5]/g, '');
            }, 60);

            var $formEname = $("#inqu_status-0-form_ename");

            // 过滤中文
            $formEname.on("input", function () {
                filterChinese(this)
            });
            var defaultPageSize = 100;

            // 页面号的查询
            var dataSource = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: IPLATUI.CONTEXT_PATH + "/service/EF0001/query",
                        type: 'POST',
                        dataType: "json",
                        contentType: "application/json;charset=utf-8"
                    },
                    parameterMap: function () {
                        var info = new EiInfo();
                        info.set("inqu_status-0-form_ename", $("#inqu_status-0-form_ename").val());
                        info.set("result-limit", defaultPageSize);
                        info.set("result-offset", 0);
                        return info.toJSONString(true);
                    }
                },
                schema: {
                    model: {
                        id: "form_ename"
                    },
                    data: function (response) {
                        // 处理异常
                        var ajaxEi = EiInfo.parseJSONObject(response);
                        if (ajaxEi.getStatus() < 0) {
                            NotificationUtil(ajaxEi);
                            return [];
                        }
                        return ajaxEi.getBlock("result").getMappedRows();
                    }
                },
                error: function (e) {
                    NotificationUtil('网络发生异常, 请稍后再试', 'error');
                    return;
                },
                pageSize: defaultPageSize,
                serverFiltering: true
            });

            // 设置下拉列的宽度
            var width = $formEname.width() * 2;
            var template = "<div class='text-overflow' style='width:" + width + "px'>" + '#: form_ename #-#: form_cname#' + "</div>";

            // 按下Enter键后触发change事件
            var enterFunc = function (e) {
                if (kendo.keys.ENTER === e.keyCode) {
                    $formEname.unbind("keyup.iplat", enterFunc); // 解绑keyup事件，防止单页展示时出现两个相同tab
                    var autoComplete = $("#inqu_status-0-form_ename").data("kendoAutoComplete");
                    autoComplete.trigger("change", {sender: autoComplete, open: true});
                }
            };

            $formEname.kendoAutoComplete({
                autoWidth: false,
                dataSource: dataSource,
                dataTextField: "form_ename",
                // minLength: 2,
                enforceMinLength: true,
                height: 200,
                template: template,
                suggest: false,
                select: function (e) {
                    var param = "",
                        form_ename = e.dataItem.form_ename;
                    if (v6Index._options.tabs && v6Index.tabs) {
                        v6Index.tabs.addTab({
                            title: form_ename,
                            url: IPLAT.createUrl(form_ename.toUpperCase(), param)
                        });
                    } else {
                        IPLAT.openNewForm(form_ename.toUpperCase(), param);
                    }
                },
                change: function (e) {
                    // 支持重新打开页面
                    $formEname.unbind("keydown.iplat");
                    $formEname.on("keydown.iplat", enterFunc);

                    // 支持Enter时候触发，其他时候触发change不打开页面
                    if (e.open) {
                        var dataSource = e.sender.dataSource,
                            form_ename = trim(e.sender.element.val()),
                            param = "",
                            item = dataSource.get(form_ename);
                        if (!!item) {
                            param = trim(item['form_param']);
                        }

                        if (v6Index._options.tabs && v6Index.tabs) {
                            v6Index.tabs.addTab({
                                title: form_ename,
                                url: IPLAT.createUrl(form_ename.toUpperCase(), param)
                            });
                        } else {
                            IPLAT.openNewForm(form_ename.toUpperCase(), param);
                        }
                    }
                }
            });
            // 页面第一次加载时，用keyup事件弹出新窗口
            $formEname.on("keyup.iplat", enterFunc);

            //2022/4/24暂定方案
            $(".search-input").on("click", '.iconPosition', function (e) {
                $(".search-input").toggleClass("active");
            });
        };

        // 锁屏功能
        V6Index.prototype.lockScreen = function () {
            var $lockScreen = $("#shortcut li:first");
            var $example = $("#example");
            //解除锁屏
            var requestFunc = function () {
                const pw = $("#unlockPassword").val();
                let ei = new EiInfo();
                ei.set("loginName", localStorage.getItem("loginName"));
                ei.set("password", pw);
                ajaxPost("BIDR56", "queryPW", ei, (response) => {
                    $("#unlockPassword").val('');
                    if (response.getStatus() !== -1) {
                        NotificationUtil(response.getMsg());
                        localStorage.removeItem("lockStatus");
                        $example.css("display", "none");
                    }
                });
            };

            //锁屏按钮触发事件绑定
            var clickFunc = function () {
                localStorage.setItem("lockStatus", "yes");
                $example.css("display", "block");
                //获取模板
                var template = kendo.template($("#lockScreenTemplate").html());
                $example.html(template([]));
                $("#submit").bind("click", requestFunc);
            };

            $lockScreen.on("click", clickFunc);

            //初始化判断key是否存在
            if (!!localStorage.getItem("lockStatus")) {
                $lockScreen.triggerHandler("click");
            }
        };

        window.V6Index = V6Index;

        //下拉菜单阻止事件冒泡
        $(window).load(function () {
            $("#shortcut").on("click", "li", function (e) {
                e.stopPropagation();
            });
        });

        // 禁止鼠标右键功能
        $(document).bind("contextmenu", function (e) {
            e = window.event || e; //解决浏览器兼容的问题
            return false;
        });

        // 禁止键盘F5刷新
        $(document).bind("keydown", function (e) { //文档绑定键盘按下事件
            e = window.event || e; //解决浏览器兼容的问题
            if (e.keyCode === 116) { //F5按下
                e.keyCode = 0;
                return false;
            }
        });
    }
)(jQuery, _);


let printLog = (msg, color, head) => {
    console.debug(`%c${!!head ? head : "debug"}：${msg}`, `background-color:#000;font-size:20px;color:${color}`);
};

let ajaxPost = (serviceName, methodName, inInfo, onSuccess, onFail, ajaxOptions) => {
    EiCommunicator.send(serviceName, methodName, inInfo, {
        onSuccess: (response) => {
            // console.info(response);
            if (response.getStatus() === -1) {
                NotificationUtil(response, IPLAT.Notification.ERROR);
            }
            onSuccess(response);
        },
        onFail: (errorMsg, status, e) => {
            console.error(errorMsg);
            onFail(errorMsg, status, e);
        }
    }, ajaxOptions);
};
