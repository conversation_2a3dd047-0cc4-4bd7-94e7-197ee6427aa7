
/* 悬浮按钮容器 */
.floating-container {
    position: fixed;
    right: 0;
    top: 30%;
    transform: translateY(-50%);
    display: flex;
    z-index: 1000;
    transition: all 0.4s ease;
}

/* 主按钮样式 */
.floating-button {
    width: 32px;
    height: 32px;
    background-image: url("static/images/disposaling.png");
}

.floating-button:hover {
    transform: scale(1.05);
}

.floating-button i {
    font-size: 28px;
    color: white;
    margin-right: 5px;
}

/* 数据列表样式 */
.data-panel {
    width: 0;
    overflow: hidden;
    background: rgb(19, 106, 157);
    backdrop-filter: blur(10px);
    border-radius: 15px 0 0 15px;
    /*box-shadow: -5px 5px 30px rgb(19, 112, 165);*/
    transition: width 0.4s ease;
    z-index: 1;
}

.floating-container:hover .data-panel {
    width: 400px;
}

.panel-content {
    width: 400px;
    padding: 20px;
    height: 100%;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h2 {
    font-size: 1.8rem;
    color: #fff;
}

.badge {
    background: #023d68;
    color: white;
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 1.4rem;
}

/* 数据列表样式 */
.data-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 10px;
}

.data-list::-webkit-scrollbar {
    width: 6px;
}

.data-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.data-list::-webkit-scrollbar-thumb {
    background: #4fc3f7;
    border-radius: 10px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.data-item:hover {
    background: rgba(79, 195, 247, 0.15);
    transform: translateX(-5px);
}

.item-name {
    font-weight: 500;
    font-size: 1.5rem;
}

.item-actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    width: 24px;
    height: 24px;
    border-radius: 0;          /* 移除圆角 */
    border: none;
    background: transparent;   /* 透明背景 */
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;        /* 为伪元素定位 */
    padding: 0;                /* 移除默认内边距 */
}

.action-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 12px solid white;  /* 箭头颜色 */
}

/* 可选悬停效果 */
.action-btn:hover {
    background: rgba(255, 255, 255, 0.15);
}
/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
}

.loader {
    width: 48px;
    height: 48px;
    border: 5px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    border-top-color: #4fc3f7;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.error-message {
    color: #ff6b6b;
    text-align: center;
    padding: 20px;
    font-size: 1rem;
}

.retry-btn {
    margin-top: 15px;
    padding: 8px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #ff6b6b;
    color: #ff6b6b;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: rgba(255, 107, 107, 0.2);
}
@media (max-width: 768px) {
    .floating-container:hover .data-panel {
        width: 280px;
    }

    .panel-content {
        width: 280px;
    }

    h1 {
        font-size: 2.2rem;
    }
}