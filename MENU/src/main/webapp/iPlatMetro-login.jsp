<!DOCTYPE html>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ page import="com.baosight.iplat4j.core.FrameworkInfo" %>
<%@ page import="com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext" %>

<%@ page import="com.baosight.iplat4j.core.license.LicenseStub" %>
<%@ page import="com.baosight.iplat4j.core.util.StringUtils" %>
<%@ page import="java.net.URLDecoder" %>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="EF" tagdir="/WEB-INF/tags/EF" %>

<%
    org.springframework.security.core.context.SecurityContextHolder.clearContext();
    LicenseStub.setLicenseDir(application.getRealPath("/WEB-INF"));
    String[] ret = LicenseStub.checkLicense2();
    boolean valid = "true".equals(ret[0]); //LicenseStub.checkLicense2();
    int days = 0;
    if (!"".equals(ret[2]) && !"0".equals(ret[2])) {
        days = Integer.parseInt(ret[2]);
    }
    String licMsg = valid ? (("false".equals(ret[3]) && days >= -10 && days < 0) ? "<div style='color:#ee9933;font-weight:bold;font-size:18px'>许可证还有[" + (-days) + "]天将过期!</div>" : "")
            : "<div style='color:red;font-weight:bold;font-size:22px'>许可证非法!</div>";

    Exception exp = (Exception) request.getAttribute("AuthenticationException");
    String user = (String) request.getAttribute("AuthenticationUser");

    if (!org.springframework.util.StringUtils.isEmpty(request.getParameter("expmsg"))) {
        String expmsg = request.getParameter("expmsg");
        exp = new Exception(URLDecoder.decode("Exception:" + expmsg));
    }
    String loginErrTag = "0";
    if (!org.springframework.util.StringUtils.isEmpty(request.getParameter("login_error"))) {
        loginErrTag = request.getParameter("login_error");
    }

    String username = "";
    String password = "";
    String captcha = "";
    if (exp != null) {
        username = user;
    }

    String usrHeader = request.getHeader("user-agent");


    String projectCname = FrameworkInfo.getProjectCname();
    String projectTypeDesc = FrameworkInfo.getProjectTypeDesc();

    // 获取iPlatUI静态资源地址
    String iPlatStaticURL = FrameworkInfo.getPlatStaticURL(request);

    String theme = org.apache.commons.lang.StringUtils.defaultIfEmpty(PlatApplicationContext.getProperty("theme"), "ant");

    // 获取Context根路径，考虑到分布式部署的场景，不能直接使用WebContext
    String iPlatContext = FrameworkInfo.getPlatWebContext(request);
%>
<c:set var="ctx" value="<%=iPlatContext%>"/>
<c:set var="iPlatStaticURL" value="<%=iPlatStaticURL%>"/>

<c:set var="loginExp" value="<%=exp%>"/>
<c:set var="theme" value="<%=theme%>" scope="session"/>

<html class="i-theme-blue">
<head>
    <meta charset="utf-8"/>
    <meta name="robots" content="noindex, nofollow"/>
    <meta name="description" content="登录界面"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>

    <% if (StringUtils.isNotEmpty(projectCname) && StringUtils.isNotEmpty(projectTypeDesc)) { %>
    <title><%=projectCname%>[<%=projectTypeDesc%>]登录界面</title>
    <% } else { %>
    <title>登录界面</title>
    <% } %>

    <link rel="shortcut icon" href="iplat.ico" type="image/x-icon">
    <link rel="stylesheet" id="css-main" href="${iPlatStaticURL}/iplatui/assets/css/iplat.ui.bootstrap.min.css">
    <link href="${iPlatStaticURL}/iPlatV6-login.css" rel="stylesheet" type="text/css"/>
    <link href="${iPlatStaticURL}/iPlatMetro-login.css" rel="stylesheet" type="text/css"/>
    <%--<link rel="stylesheet" type="text/css"  href="${iPlatStaticURL}/iplatui/css/iplat.ui.ued.login.css">&lt;%&ndash;ued亮色样式&ndash;%&gt;--%>
    <script src="${iPlatStaticURL}/kendoui/js/jquery.min.js"></script>

    <!--[if lte IE 8]>
    <link href="${iPlatStaticURL}/iPlatV6-login-ie.css" rel="stylesheet" type="text/css"/>
    <script src="${iPlatStaticURL}/iplatui/assets/js/polyfills/iplat.ui.ie8.polyfills.min.js"></script>
    <![endif]-->

    <script src="${iPlatStaticURL}/iPlatMetro-login.js"></script>
    <%
        String domain = FrameworkInfo.getProjectAppTopDomain();
        if (domain != null && domain.startsWith(".")) {
            domain = domain.substring(1);
    %>
    <script type="text/javascript">
        try {
            document.domain = '<%=domain%>';
        } catch (ex) {
            alert('model not valid[<%=domain%>]');
        }
    </script>
    <%
        }
    %>
</head>
<body>
<div class="main">
    <div class="wrapper">
        <div class="content overflow-hidden">
            <div class="row">
                <div class="col-sm-8 col-sm-offset-2 col-md-8 col-md-offset-4">
                    <div class="login-block <c:if test="${not empty loginExp}"> animated shake</c:if>">
                        <div class="form-header">
                            <%--                            <div class="logo"></div>--%>
                            <%--                            <p>系统登录</p>--%>
                            <p class="text-danger">
                                <c:if test="${not empty loginExp}">
                                    <%
                                        String loginError = exp.getMessage();
                                        int index = loginError.indexOf("Exception:");
                                        if (index >= 0) {
                                            loginError = loginError.substring(index + 10);
                                        }
                                        if (!"1".equals(loginErrTag) &&
                                                (request.getAttribute("AuthenticationUser") == null || request.getAttribute("AuthenticationUser") == "")) {
                                            loginError = "请输入用户名";
                                        }
                                    %>
                                    <%=loginError%>
                                </c:if>
                            </p>
                        </div>

                        <form autocomplete="off" class="form-horizontal push-10-t push-10" action="${ctx}/login"
                              method="post" onsubmit="javascript:return loginClick();">
                            <div class="form-group">
                                <div class="col-xs-12 form-layout">
                                      <span class="form-label">
                                        <i class="icon icon-user" aria-hidden="true"></i> 用户名
                                    </span>
                                    <input class="form-input" type="text" name="p_username" placeholder="用户名"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-xs-12 form-layout">
                                     <span class="form-label">
                                        <i class="icon icon-password" aria-hidden="true"></i> 密&nbsp;&nbsp;&nbsp;码
                                    </span>
                                    <input class="form-input" type="password" name="p_password" placeholder="密码"
                                           autocomplete="false"/>
                                </div>
                            </div>
<%--                            <div class="form-group">--%>
<%--                                &lt;%&ndash;2022.11.28 移除用户组&ndash;%&gt;--%>
<%--                                <div class="col-xs-12 form-layout" style="visibility: hidden;">--%>
<%--                                     <span class="form-label">--%>
<%--                                        <i class="icon icon-user-group" aria-hidden="true"></i> 用户组--%>
<%--                                  </span>--%>
<%--                                    <input class="form-input" type="text" name="p_userGroup" placeholder="用户组"/>--%>
<%--                                </div>--%>
<%--                            </div>--%>
<%--                            <div class="form-group">--%>
<%--                                <div class="col-xs-12 form-layout" >--%>
<%--								    <span class="form-label">--%>
<%--										<i class="icon icon-verification" aria-hidden="true"></i> 验证码--%>
<%--									</span>--%>
<%--                                    <div class="code-group">--%>
<%--                                        <input type="text" placeholder="请输入验证码" name="p_code" id="p_code"--%>
<%--                                               class="form-input" onkeyup="checkCodeCorrect(this)"/>--%>
<%--                                        <img src="${iPlatStaticURL}/encode/captcha.jpg" onclick="changeVerifyCode(this)"--%>
<%--                                             alt="" height="40px">--%>
<%--                                        <i class="fa fa-check check_code"></i>--%>
<%--                                    </div>--%>
<%--                                </div>--%>
<%--                            </div>--%>
                            <div class="form-group log-in">
                                <div class="col-xs-12 form-layout">
                                    <button id="login" class="login-btn" type="submit">登&nbsp;&nbsp;&nbsp;录
                                    </button>
                                    <%--阻止表单默认提交--%>
                                    <button id="close" class="login-btn" type="button">
                                        <a href="COCCEXEEXIT://" title="退出">退&nbsp;&nbsp;&nbsp;出</a>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="i-overlay"></div>
</body>


<%--<script>--%>
<%--    loginClick = function () {--%>
<%--        // var loginForm = document.getElementsByTagName("form")[0];--%>
<%--        // var valueString = getParameterByName("jumpUrl", location.href);--%>
<%--        // if (valueString != null) {--%>
<%--        // var tmpInput = document.createElement("input");--%>
<%--        // tmpInput.type = "hidden";--%>
<%--        // tmpInput.name = "jumpUrl";--%>
<%--        // tmpInput.value = valueString;--%>
<%--        // loginForm.appendChild(tmpInput);--%>
<%--        // }--%>

<%--        //判断是否是需要第三方初始化账号--%>
<%--        var username = $("input[name='p_username']").val();--%>
<%--        var $p = $("input[name='p_password']");--%>
<%--        var password = $p.val();--%>
<%--        var code=$("input[name='p_code']").val();--%>
<%--        //后台校验密码是否正确--%>
<%--        var flag = false;--%>
<%--        if(!isAvailable(username)){--%>
<%--            $(".text-danger").empty().append("请输入账号！");--%>
<%--            return false;--%>
<%--        }else if(!isAvailable(password)){--%>
<%--            $(".text-danger").empty().append("请输入密码！");--%>
<%--            return false;--%>
<%--        }else if(!isAvailable(code)||code.length === 0 ){--%>
<%--            $(".text-danger").empty().append("请输入验证码！");--%>
<%--            return false;--%>
<%--        }else if($("#login").data("flag")||code.length !== 4){--%>
<%--            $(".text-danger").empty().append("验证码不正确，请重新输入！");--%>
<%--            return false;--%>
<%--        }--%>

<%--        $(".login-btn").css("pointer-events", "none");--%>
<%--        var encrypt = new JSEncrypt();--%>
<%--        var publicKey = $("#rsaPublicKey").val();--%>
<%--        encrypt.setPublicKey(publicKey);--%>
<%--        var checkUserName = encrypt.encrypt(username);--%>
<%--        var checkPassWord = encrypt.encrypt(password);--%>
<%--        $.ajax({--%>
<%--            type: 'post',--%>
<%--            url: '/eplat' + '/encode/checkNeedInit',--%>
<%--            data: {username: checkUserName, password: checkPassWord},--%>
<%--            async: false,--%>
<%--            success: function (data) {--%>
<%--                var status = data.status;--%>
<%--                if (status === 0) {--%>
<%--                    $(".text-danger").empty().append("请输入正确的账号/密码完成账号初始化!");--%>
<%--                } else if (status === 1) {--%>
<%--                    //跳转至初始化页面--%>
<%--                    window.location.href = '/eplat' + '/web/BEAA08?userId=' + username;--%>
<%--                } else if (status === 2) {--%>
<%--                    var encrypt = new JSEncrypt();--%>
<%--                    var publicKey = $("#rsaPublicKey").val();--%>
<%--                    encrypt.setPublicKey(publicKey);--%>
<%--                    $("input[name='p_username']").val(encrypt.encrypt(username));--%>
<%--                    $p.val(encrypt.encrypt(password));--%>
<%--                    flag = true;--%>
<%--                } else {--%>
<%--                    $(".text-danger").empty().append("校验账号/密码失败，请稍后重试!");--%>
<%--                    $(".login-btn").css("pointer-events", "auto");--%>
<%--                }--%>
<%--            },--%>
<%--            failed: function () {--%>
<%--                $(".text-danger").empty().append("校验账号/密码失败，请稍后重试!");--%>
<%--            }--%>
<%--        });--%>
<%--        if (flag === false) {--%>
<%--            $("#p_username").val(username);--%>
<%--        }--%>
<%--        return flag;--%>
<%--    };--%>

<%--    function isAvailable(obj) {--%>
<%--        if (obj === undefined) {--%>
<%--            return false--%>
<%--        }--%>
<%--        if (obj === null) {--%>
<%--            return false--%>
<%--        }--%>
<%--        return obj !== ''--%>
<%--    }--%>
<%--    function changeVerifyCode(img) {--%>
<%--        img.src = "${iPlatStaticURL}/encode/captcha.jpg?" + new Date().getTime();--%>
<%--    }--%>

<%--    function checkCodeCorrect(obj) {--%>
<%--        var code = $.trim(obj.value);--%>
<%--        if (code.length === 4) {--%>
<%--            $.ajax({--%>
<%--                type: 'post',--%>
<%--                url: '${iPlatStaticURL}' + '/encode/checkVerifyCodeCorrect',--%>
<%--                data: {verifyCode: code},--%>
<%--                success: function (response) {--%>
<%--                    if (response.status === -1) {--%>
<%--                        //不正确--%>
<%--                        $(obj).next().next().css("display", "none");--%>
<%--                        $("#login").data("flag",true)--%>
<%--                    } else if (response.status === 1) {--%>
<%--                        //验证码正确--%>
<%--                        $(obj).next().next().css("display", "inline");--%>
<%--                        $("#login").data("flag",false)--%>
<%--                    }--%>
<%--                },--%>
<%--                error: function (response) {--%>
<%--                    console.log('checkCode error: ', response)--%>
<%--                }--%>
<%--            })--%>
<%--        }else{--%>
<%--            $(obj).next().next().css("display", "none");--%>
<%--            $("#login").data("flag",true)--%>
<%--        }--%>
<%--    }--%>
<%--</script>--%>

</html>
