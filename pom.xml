<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.baosight.irailebs</groupId>
    <artifactId>cqyy</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>irailebs-parent</module>
        <module>irailebs-common</module>
        <module>irailebs-vue</module>
    </modules>

    <!-- 🎯 独立模块版本管理 -->
    <properties>
        <!-- 框架版本 -->
        <iplat4j.version>7.1.0</iplat4j.version>

        <!-- 项目基础版本 -->
        <project.base.version>1.0.0</project.base.version>

        <!-- 各模块独立版本 -->
        <irailebs-common.version>1.0.0</irailebs-common.version>
        <irailebs-pm.version>1.0.0-7.1.0-SNAPSHOT</irailebs-pm.version>
        <irailebs-pm-cqyy.version>1.0.0-7.1.0-SNAPSHOT</irailebs-pm-cqyy.version>
        <irailebs-web.version>1.0.0-7.1.0-SNAPSHOT</irailebs-web.version>
        <irailebs-vue.version>1.0.0</irailebs-vue.version>

        <!-- 编译配置 -->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <!-- 🎯 统一依赖版本管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- 业务模块依赖管理 -->
            <dependency>
                <groupId>com.baosight.irailebs</groupId>
                <artifactId>irailebs-common</artifactId>
                <version>${irailebs-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baosight.irailebs</groupId>
                <artifactId>irailebs-pm-cqyy</artifactId>
                <version>${irailebs-pm-cqyy.version}</version>
            </dependency>

            <!-- 第三方依赖版本管理 -->
            <dependency>
                <groupId>com.dm</groupId>
                <artifactId>dm8-jdbc-driver-18</artifactId>
                <version>*********</version>
            </dependency>
            <dependency>
                <groupId>com.baosight.iplat4j</groupId>
                <artifactId>redis-plugin</artifactId>
                <version>${iplat4j.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 🛠️ Maven Versions Plugin 配置 -->
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>2.16.2</version>
                    <configuration>
                        <!-- 允许SNAPSHOT版本 -->
                        <allowSnapshots>true</allowSnapshots>
                        <!-- 生成备份文件 -->
                        <generateBackupPoms>false</generateBackupPoms>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- 统一仓库配置 -->
    <repositories>
        <repository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </repository>
        <repository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </repository>
        <repository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>rt-releases</id>
            <name>rt release</name>
            <url>http://rt.baosight.com/nexus/repository/maven-public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>rt-snapshots</id>
            <name>rt snapshots</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-snapshots/</url>
        </pluginRepository>
        <pluginRepository>
            <id>aliyun-maven</id>
            <name>aliyun maven</name>
            <url>http://nexus3.rtdomain.com:8081/nexus/repository/maven-aliyun/</url>
        </pluginRepository>
        <pluginRepository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>http://nexus.baocloud.cn/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

</project>