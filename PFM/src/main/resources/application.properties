spring.mvc.servlet.path=/
spring.main.allow-bean-definition-overriding=true
server.port=8083
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**
server.error.whitelabel.enabled=false
#默认never，无法输出trace
server.error.include-stacktrace=always
spring.profiles.active = dev


#\u9879\u76EE\u540D\u79F0
projectName=pfm
componentEname=ep
moduleName=DN
projectEnv=dev
#projectPackage=com.niwei
#platSchema=IPLAT4J

#"2134Baosteel Group" unicode encoding in Chinese, and ANY Chinese should be unicode encoded in this file.
customerName=\u4E2D\u56FD\u5B9D\u6B66\u94A2\u94C1\u96C6\u56E2\u6709\u9650\u516C\u53F8
enterpriseName=\u5B9D\u4FE1\u8F6F\u4EF6\u5E73\u53F0\u7814\u7A76\u4E00\u6240

datasource.type=dbcp


##上海开发环境
#jdbc.driverClassName=com.gbasedbt.jdbc.Driver
#jdbc.url=jdbc:gbasedbt-sqli://************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
#jdbc.username=gbasedbt
#jdbc.password=Ba0sight

#南宁实验室环境
#jdbc.driverClassName=com.gbasedbt.jdbc.Driver
#jdbc.url=jdbc:gbasedbt-sqli://*************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
#jdbc.username=gbasedbt
#jdbc.password=Ba0sight

#南宁现场开发环境
jdbc.driverClassName=com.gbasedbt.jdbc.Driver
#正式数据库
jdbc.url=jdbc:gbasedbt-sqli://*************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
#旧版数据库
#jdbc.url=jdbc:gbasedbt-sqli://*************:9088/nocciplat:GBASEDBTSERVER=gbase8s;SQLMODE=Oracle;DB_LOCALE=zh_cn.utf8;APPENDISAM=TRUE;NON_QUOTATION_COLUNAME_ALIAS_UPPERCASE_FORCE=Y;DELIMIDENT=Y;
jdbc.username=gbasedbt
jdbc.password=Ba0sight

platSchema=nocciplat
pfmProjectSchema=irailmetropfm
accProjectSchema=irailmetroacc
#
jdbc.maxActive=50
jdbc.validationQuery=SELECT 1 FROM ${platSchema}.TEDFA00
jdbc.minimumIdle=10
jdbc.idleTimeout=180000

spring.jmx.enabled=false

configEx=xservices;xservice.security;xservices.job;xservices.message;xservices.bpm;

iplat.core.invoke.remote=off


appTopDomain=/