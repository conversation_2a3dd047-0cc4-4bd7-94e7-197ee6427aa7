package com.baosight.pfm.common.annotation.validator;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * EiInfo参数验证
 *
 * <AUTHOR>
 * @date 2023/11/14
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.PARAMETER})
public @interface EiValidation {
    String key() default "params";

    Class<?> clazz();

    boolean enableCache() default false;

    boolean printOutput() default true;

    boolean handleException() default false;
}
