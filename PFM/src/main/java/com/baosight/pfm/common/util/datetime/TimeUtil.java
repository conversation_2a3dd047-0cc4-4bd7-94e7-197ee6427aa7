package com.baosight.pfm.common.util.datetime;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间转换工具类
 *
 * <AUTHOR>
 * @date 2023/12/06
 */
public class TimeUtil {
    private final DateTimeFormatter formatter;
    private final int interval;

    /**
     * TimeUtil构造器
     *
     * @param format   时间格式
     * @param interval 时间间隔
     */
    private TimeUtil(String format, int interval) {
        this.formatter = DateTimeFormatter.ofPattern(format);
        this.interval = interval;
    }

    /**
     * 获取当前时间最接近时刻的时间（使用默认格式和间隔）
     *
     * @return 最接近的整点时间字符串
     */
    public static String convertDefaultTime() {
        return TimeUtil.create().getNearestWholeTime();
    }

    /**
     * 使用特定格式获取当前时间最接近时刻的时间
     *
     * @param format 特定的时间格式
     * @return 最接近的整点时间字符串
     */
    public static String convertDefaultTime(String format) {
        return TimeUtil.create(format).getNearestWholeTime();
    }

    /**
     * 使用特定间隔获取当前时间最接近时刻的时间
     *
     * @param interval 特定的时间间隔
     * @return 最接近的整点时间字符串
     */
    public static String convertDefaultTime(int interval) {
        return TimeUtil.create(interval).getNearestWholeTime();
    }

    /**
     * 使用特定格式和特定间隔获取当前时间最接近时刻的时间
     *
     * @param format   特定的时间格式
     * @param interval 特定的时间间隔
     * @return 最接近的整点时间字符串
     */
    public static String convertDefaultTime(String format, int interval) {
        return TimeUtil.create(format, interval).getNearestWholeTime();
    }

    /**
     * 使用默认设置转换和返回给定时间字符串的最接近的整点时间
     *
     * @param timeStr 需要转换的时间字符串
     * @return 最接近的整点时间
     */
    public static String convertTime(String timeStr) {
        return TimeUtil.create().getNearestWholeTime(timeStr);
    }

    /**
     * 使用特定格式和默认间隔转换和返回给定时间字符串的最接近的整点时间
     *
     * @param timeStr 需要转换的时间字符串
     * @param format  指定的时间格式
     * @return 最接近的整点时间
     */
    public static String convertTime(String timeStr, String format) {
        return TimeUtil.create(format).getNearestWholeTime(timeStr);
    }

    /**
     * 使用默认格式和特定间隔转换和返回给定时间字符串的最接近的整点时间
     *
     * @param timeStr  需要转换的时间字符串
     * @param interval 指定的时间间隔
     * @return 最接近的整点时间
     */
    public static String convertTime(String timeStr, int interval) {
        return TimeUtil.create(interval).getNearestWholeTime(timeStr);
    }

    /**
     * 使用特定格式和特定间隔转换和返回给定时间字符串的最接近的整点时间
     *
     * @param timeStr  需要转换的时间字符串
     * @param format   指定的时间格式
     * @param interval 指定的时间间隔
     * @return 最接近的整点时间
     */
    public static String convertTime(String timeStr, String format, int interval) {
        return TimeUtil.create(format, interval).getNearestWholeTime(timeStr);
    }

    /**
     * 使用默认格式"H:mm"及间隔5创建工具类实例
     *
     * @return TimeUtil实例
     */
    public static TimeUtil create() {
        return new TimeUtil("H:mm", 5);
    }

    /**
     * 使用特定格式和默认间隔5创建工具类实例
     *
     * @param format 时间格式
     * @return TimeUtil实例
     */
    public static TimeUtil create(String format) {
        return new TimeUtil(format, 5);
    }

    /**
     * 使用默认格式"H:mm"和特定间隔创建工具类实例
     *
     * @param interval 时间间隔
     * @return TimeUtil实例
     */
    public static TimeUtil create(int interval) {
        return new TimeUtil("H:mm", interval);
    }

    /**
     * 使用特定格式和特定间隔创建工具类实例
     *
     * @param format   时间格式
     * @param interval 时间间隔
     * @return TimeUtil实例
     */
    public static TimeUtil create(String format, int interval) {
        return new TimeUtil(format, interval);
    }

    /**
     * 获取给定时间字符串对应的最近整点时间
     *
     * @param timeStr 时间字符串
     * @return 最近整点时间
     */
    public String getNearestWholeTime(String timeStr) {
        try {
            LocalTime inputTime = LocalTime.parse(timeStr, formatter);
            return getNearestWholeTime(inputTime);
        } catch (DateTimeParseException ex) {
            throw new IllegalArgumentException("Failed to parse time string: " + timeStr, ex);
        }
    }




    /**
     * 获取当前时间的最近整点时间
     *
     * @return 最近整点时间
     */
    public String getNearestWholeTime() {
        LocalTime inputTime = LocalTime.now();
        return getNearestWholeTime(inputTime);
    }

    /**
     * 计算给定时间的最近整点时间
     *
     * @param inputTime 输入时间
     * @return 最近整点时间
     */
    private String getNearestWholeTime(LocalTime inputTime) {
        int minute = inputTime.getMinute();
        int wholeMinute = (minute / interval) * interval;
        LocalTime wholeTime = inputTime.plusMinutes(wholeMinute - minute);
        return formatter.format(wholeTime);
    }

}
