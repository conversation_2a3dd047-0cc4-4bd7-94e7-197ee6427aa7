package com.baosight.pfm.km.common.dataUtils;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baosight.iplat4j.core.ei.EiInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.baosight.pfm.common.BaseDataUtils.*;

public class dataProcess  {
    //时间段，跨天处理
    /**
     * 设置日期的情况下进行开始时间和结束时间进行acc格式的转换，acc格式yyyyMMddHHmm
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String, Object> timeTurnAcc(String startTime, String endTime ,String date){
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalDate todayDate = LocalDate.parse(date);
        LocalTime start = LocalTime.parse(startTime, timeFormatter);
        LocalTime end = LocalTime.parse(endTime, timeFormatter);
        LocalDateTime startDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), start.getHour(), start.getMinute());
        LocalDateTime endDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), end.getHour(), end.getMinute());
        Map<String, Object> result = new HashMap<>();
        //结束时间在第二天,起始时间在第一天
        if (end.isBefore(start) || end.equals(start)) {
            endDateTime = endDateTime.plusDays(1);
            result.put("flag","");
        }
//        //起始时间和结束时间在同天且都在明日
        else if (end.isAfter(start) && start.getHour()>=0&&start.getHour()<4){
            startDateTime = startDateTime.plusDays(1);
            endDateTime = endDateTime.plusDays(1);
            result.put("flag","0000");
        }
        //起始时间和结束时间都在明天
        else if (end.isAfter(start) && start.getHour()>=4){
            result.put("flag","0000");
        }
        startTime = startDateTime.format(dateFormatter);
        endTime = endDateTime.format(dateFormatter);
        result.put("startTime",startTime);
        result.put("endTime",endTime);
        return result;
    }
    /**
     * 不设置日期的情况下默认当日时间下开始时间和结束时间进行acc格式的转换
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String, Object> timeTurnAcc(String startTime, String endTime){
        LocalDate todayDate = LocalDate.now();
        Map<String, Object> result = timeTurnAcc(startTime,endTime, Convert.toStr(todayDate));
        return result;
    }
    /**
     * 设置日期的情况下进行开始时间和结束时间进行pfp格式的转换，pfp格式yyyyMMddHHmmss
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String,Object> timeTurnPfp(String startTime,String endTime,String date){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-ddHH:mm:ss");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalDate todayDate = LocalDate.parse(date);
        LocalTime start = LocalTime.parse(startTime,timeFormatter);
        LocalTime end = LocalTime.parse(endTime,timeFormatter);
        LocalDateTime startDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), start.getHour(), start.getMinute(), start.getSecond());
        LocalDateTime endDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), end.getHour(), end.getMinute(), end.getSecond());
        Map<String, Object> result = new HashMap<>();
        if (end.isAfter(start) && start.getHour()>=0&&start.getHour()<5){
            startDateTime = startDateTime.plusDays(1);
            endDateTime = endDateTime.plusDays(1);
        }else if(start.isAfter(end)){
            endDateTime = endDateTime.plusDays(1);
        }
        startTime = startDateTime.format(dateTimeFormatter);
        endTime = endDateTime.format(dateTimeFormatter);
        result.put("startTime",startTime);
        result.put("endTime",endTime);
        return result;
    }
    /**
     * 不设置日期的情况下默认当日时间下开始时间和结束时间进行pfp格式的转换
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String,Object> timeTurnPfp(String startTime,String endTime){
        LocalDate todayDate = LocalDate.now();
        Map<String, Object> result = timeTurnPfp(startTime,endTime, Convert.toStr(todayDate));
        return result;
    }
    //时间片数据，跨天不处理
    /**
     * 设置日期的情况下进行开始时间和结束时间进行pfp格式的转换，pfp格式yyyyMMddHHmmss
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String,Object> timeTurnPfpForTimeSplice(String startTime,String endTime,String date){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-ddHH:mm:ss");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalDate todayDate = LocalDate.parse(date);
        LocalTime start = LocalTime.parse(startTime,timeFormatter);
        LocalTime end = LocalTime.parse(endTime,timeFormatter);
        LocalDateTime startDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), start.getHour(), start.getMinute(), start.getSecond());
        LocalDateTime endDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), end.getHour(), end.getMinute(), end.getSecond());
        Map<String, Object> result = new HashMap<>();
        if (end.isAfter(start) && start.getHour()>=0&&start.getHour()<5){
            startDateTime = startDateTime.plusDays(1);
            endDateTime = endDateTime.plusDays(1);
        }
        startTime = startDateTime.format(dateTimeFormatter);
        endTime = endDateTime.format(dateTimeFormatter);
        result.put("startTime",startTime);
        result.put("endTime",endTime);
        return result;
    }
    /**
     * 不设置日期的情况下默认当日时间下开始时间和结束时间进行pfp格式的转换
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String,Object> timeTurnPfpForTimeSplice(String startTime,String endTime){
        LocalDate todayDate = LocalDate.now();
        Map<String, Object> result = timeTurnPfpForTimeSplice(startTime,endTime, Convert.toStr(todayDate));
        return result;
    }
    /**
     * 设置日期的情况下进行开始时间和结束时间进行acc格式的转换，acc格式yyyyMMddHHmm
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String, Object> timeTurnAccForTimeSplice(String startTime, String endTime ,String date){
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalDate todayDate = LocalDate.parse(date);
        LocalTime start = LocalTime.parse(startTime, timeFormatter);
        LocalTime end = LocalTime.parse(endTime, timeFormatter);
        LocalDateTime startDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), start.getHour(), start.getMinute(),start.getSecond());
        LocalDateTime endDateTime = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), end.getHour(), end.getMinute(),end.getSecond());
        Map<String, Object> result = new HashMap<>();
        if (end.isAfter(start) && start.getHour()>=0&&start.getHour()<4){
            startDateTime = startDateTime.plusDays(1);
            endDateTime = endDateTime.plusDays(1);
        }
        startTime = startDateTime.format(dateFormatter);
        endTime = endDateTime.format(dateFormatter);
        result.put("startTime",startTime);
        result.put("endTime",endTime);
        return result;
    }
    /**
     * 不设置日期的情况下默认当日时间下开始时间和结束时间进行acc格式的转换
     * @param startTime
     * @param endTime
     * @return
     */
    public static Map<String, Object> timeTurnAccForTimeSplice(String startTime, String endTime){
        LocalDate todayDate = LocalDate.now();
        Map<String, Object> result = timeTurnAccForTimeSplice(startTime,endTime, Convert.toStr(todayDate));
        return result;
    }


    /**
     * 线路号规范化
     * @param lineNumber
     * @return
     */
    public static String lineNumberTurn(int lineNumber){
        return "0"+lineNumber+"00000000";
    }
    /**
     * listmap格式规范化
     * @param obj
     * @return
     */
    public static List<Map<String,Object>> toListMap(Object obj){
        List<Map<String, Object>> result = new ArrayList<>();
        Convert.toList(obj).forEach(
                item -> result.add(JSON.parseObject(JSONObject.toJSONString(item), new TypeReference<Map<String, Object>>(){})));
        return result;
    }
    /**
     * 通过起始车站和结束车站的站点ID查询断面
     * @param startId 起始车站ID
     * @param endId 结束车站ID
     * @return
     */
    public static Map<String,Object> querySectionInfo(String startId,String endId){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("startStaId",startId);
        eiInfo.set("endStaId",endId);
        EiInfo sectionEiInfo = querySection(eiInfo);
        Map<String, Object> section = (Map<String, Object>) sectionEiInfo.getBlock("result").getRows().get(0);
        return  section;
    }
    public static Map<String,Object> querySectionInfoByCnName(String startName,String endName){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("startStaCname",startName);
        eiInfo.set("endStaCname",endName);
        EiInfo sectionEiInfo = querySection(eiInfo);
        Map<String, Object> section = (Map<String, Object>) sectionEiInfo.getBlock("result").getRows().get(0);
        return  section;
    }
    /**
     * 根据线路号查询一条线路内所有的断面信息
     * @param lineNumber
     * @return
     */
    public static List<Map<String,Object>> querySectionInfoByLineId(String lineNumber){
        EiInfo queryInfo = new EiInfo();
        queryInfo.set("lineId",lineNumber);
        EiInfo queryResult = querySection(queryInfo);
        List<Map<String,Object>> sectionList = queryResult.getBlock("result").getRows();
        return sectionList;
    }
    /**
     * 通过站点Id查询站点中文名
     * @param stationId
     * @return
     */
    public static String queryStationName(String stationId){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("stationId",stationId);
        EiInfo stationEiInfo = queryStation(eiInfo);
        Map<String, Object> station = (Map<String, Object>) stationEiInfo.getBlock("result").getRows().get(0);
        return Convert.toStr(station.get("sta_cname"));
    }
    /**
     * 通过站点名查询车站信息
     * @param stationName
     * @return
     */
    public static List<Map<String,Object>> queryStationInfo(String stationName){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("stationCName",stationName);
        EiInfo stationEiInfo = queryStation(eiInfo);
        List<Map<String, Object>> station = stationEiInfo.getBlock("result").getRows();
        return station;
    }
    public static List<Map<String,Object>> querySectionInfoByStartEnd(String start,String end){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("startStaCname",start);
        eiInfo.set("endStaCname",end);
        EiInfo stationEiInfo = querySection(eiInfo);
        List<Map<String,Object>> section = stationEiInfo.getBlock("result").getRows();
        return section;
    }
    /**
     * 查询线网内所有车站
     * @return
     */
    public static List<Map<String,Object>> queryStationInfo(){
        EiInfo eiInfo = new EiInfo();
        EiInfo stationEiInfo = queryStation(eiInfo);
        List<Map<String, Object>> station = stationEiInfo.getBlock("result").getRows();
        List<Map<String, Object>> returnStation = new ArrayList<>();
        for (Map<String,Object> stationElement : station){
            if (stationElement.get("sta_type").equals("STATION")&&stationElement.get("enable_status").equals("true")){
                returnStation.add(stationElement);
            }
        }
        return returnStation;
    }

    /**
     * 查询线网中所有换乘站
     * @return
     */
    public static Map<String,Object> queryAllTranInfoByStationNumber(){
        EiInfo eiInfo = new EiInfo();
        EiInfo stationEiInfo = queryStation(eiInfo);
        List<Map<String, Object>> station = stationEiInfo.getBlock("result").getRows();
        List<Map<String, Object>> returnStation = new ArrayList<>();
        for (Map<String,Object> stationElement : station){
            if (stationElement.get("sta_type").equals("STATION")&&stationElement.get("enable_status").equals("true")&&!stationElement.get("transfer_info").equals("")){
                returnStation.add(stationElement);
            }
        }
        Map<String,Object> allStationInfo = returnStation.stream().collect(Collectors.toMap(
                e->Convert.toStr(e.get("sta_id")),
                e -> e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        return allStationInfo;
    }
    public static List<Map<String,Object>> queryStationInfoByLine(String lineNumber){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("lineId",lineNumber);
        EiInfo stationEiInfo = queryStation(eiInfo);
        List<Map<String, Object>> station = stationEiInfo.getBlock("result").getRows();
        List<Map<String, Object>> returnStation = new ArrayList<>();
        for (Map<String,Object> stationElement : station){
            if (stationElement.get("sta_type").equals("STATION")&&stationElement.get("enable_status").equals("true")){
                returnStation.add(stationElement);
            }
        }
        return returnStation;
    }
    public static Map<String,Object> queryStationInfoByMap(){
        Map<String,Object> allStationInfo = dataProcess.queryStationInfo().stream().collect(Collectors.toMap(
                e->Convert.toStr(e.get("sta_id")),
                e -> e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        return allStationInfo;
    }
    /**
     * 通过线路号查询线路内所有站点
     * @param lineId
     * @return
     */
    public static List<Map<String,Object>> queryStationInfoByLineId(String lineId){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("lineId",lineId);
        EiInfo stationEiInfo = queryStation(eiInfo);
        List<Map<String, Object>> station = stationEiInfo.getBlock("result").getRows();
        List<Map<String, Object>> returnStation = new ArrayList<>();
        for (Map<String,Object> stationElement : station){
            if (stationElement.get("sta_type").equals("STATION")&&stationElement.get("enable_status").equals("true")){
                returnStation.add(stationElement);
            }
        }
        return returnStation;
    }
    /**
     * 查询线网内所有的线路
     * @return
     */
    public static List<Map<String,Object>> queryLineInfo(){
        EiInfo eiInfo = new EiInfo();
        EiInfo lineEiInfo = queryLine(eiInfo);
        List<Map<String, Object>> line = lineEiInfo.getBlock("result").getRows();
        return line;
    }
    /**
     * 查询所有断面信息
     * @return
     */
    public static List<Map<String,Object>> queryAllSection(){
        EiInfo eiInfo = new EiInfo();
        EiInfo sectionInfo = querySection(eiInfo);
        List<Map<String, Object>> section = sectionInfo.getBlock("result").getRows();
        return section;
    }
    public static Map<String,Object> queryAllSectionByMap(){
        Map<String,Object> allSectionInfo = dataProcess.queryAllSection().stream().collect(Collectors.toMap(
                e->Convert.toStr(e.get("section_id")),
                e -> e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        return allSectionInfo;
    }
    /**
     * 当日时间拼接格式话
     * @param time
     * @return
     */
    public static LocalDateTime timeFormat(String time){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalDate todayDate = LocalDate.now();
        LocalTime timeFormat = LocalTime.parse(time,timeFormatter);
        LocalDateTime timeDate = LocalDateTime.of(todayDate.getYear(), todayDate.getMonth(), todayDate.getDayOfMonth(), timeFormat.getHour(), timeFormat.getMinute(), timeFormat.getSecond());
        if (timeFormat.getHour()<=5){
            timeDate = timeDate.plusDays(1);
        }
        return timeDate;
    }
    /**
     * 日期时间拼接格式化
     * @param time
     * @param date
     * @return
     */
    public static LocalDateTime timeFormat(String time,String date){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalDate dateFormat = LocalDate.parse(date);
        LocalTime timeFormat = LocalTime.parse(time,timeFormatter);
        LocalDateTime timeDate = LocalDateTime.of(dateFormat.getYear(), dateFormat.getMonth(), dateFormat.getDayOfMonth(), timeFormat.getHour(), timeFormat.getMinute(), timeFormat.getSecond());
        if (timeFormat.getHour()<=5){
            timeDate = timeDate.plusDays(1);
        }
        return timeDate;
    }
    public static LocalTime timeFormatToTime(String time){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime timeFormat = LocalTime.parse(time,timeFormatter);
        return timeFormat;
    }
    /**
     * 对List根据某个字段排序
     * @param list
     * @param sortSpan
     * @param ascOrdesc
     */
    public static void sortListBySpan(List<Map<String,Object>> list, String sortSpan, String ascOrdesc){
        //升序
        if (ascOrdesc.equals("asc")){
            Collections.sort(list, new Comparator<Map<String, Object>>() {
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    Integer value1= Double.valueOf(o1.get(sortSpan).toString()).intValue();
                    Integer value2= Double.valueOf(o2.get(sortSpan).toString()).intValue();
                    return value1.compareTo(value2);
                }
            });
        }else{
            //降序
            Collections.sort(list, new Comparator<Map<String, Object>>() {
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    Integer value1= Double.valueOf(o1.get(sortSpan).toString()).intValue() ;
                    Integer value2= Double.valueOf(o2.get(sortSpan).toString()).intValue() ;
                    return value2.compareTo(value1);
                }
            });
        }
    }
    public static void sortListBySpanByDouble(List<Map<String,Object>> list, String sortSpan, String ascOrdesc){
        try {
            if (ascOrdesc.equals("asc")){
                Collections.sort(list, new Comparator<Map<String, Object>>() {
                    public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                        BigDecimal value1 = new BigDecimal(o1.get(sortSpan).toString());
                        BigDecimal value2 = new BigDecimal(o2.get(sortSpan).toString());
                        return value1.compareTo(value2);
                    }
                });
            }else{
                //降序
                Collections.sort(list, new Comparator<Map<String, Object>>() {
                    public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                        BigDecimal value1 = new BigDecimal(o1.get(sortSpan).toString());
                        BigDecimal value2 = new BigDecimal(o2.get(sortSpan).toString());
                        return value2.compareTo(value1);
                    }
                });
            }
        }catch (Exception e){

        }

    }
    public static void sortListBySpanByDouble1(List<Map<String,Object>> list, String sortSpan, String ascOrdesc){
        try {
            if (ascOrdesc.equals("asc")){
                Collections.sort(list, new Comparator<Map<String, Object>>() {
                    public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                        BigDecimal value1 = new BigDecimal(o1.get(sortSpan).toString());
                        BigDecimal value2 = new BigDecimal(o2.get(sortSpan).toString());
                        return value1.compareTo(value2);
                    }
                });
            }else{
                //降序
                Collections.sort(list, new Comparator<Map<String, Object>>() {
                    public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                        Double value1 = Convert.toDouble(o1.get(sortSpan).toString());
                        Double value2 = Convert.toDouble(o2.get(sortSpan).toString());
                        return value2.compareTo(value1);
                    }
                });
            }
        }catch (Exception e){

        }

    }
    /**
     * 判断时间段是否跨天
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean isCrossDayAcc(String startTime,String endTime){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime start = LocalTime.parse(startTime,timeFormatter);
        LocalTime end = LocalTime.parse(endTime,timeFormatter);
        if (end.isAfter(start)){
            return false;
        }else if (start.isAfter(end)){
            return true;
        }
        return false;
    }
    public static boolean isCrossDay(String startTime, String endTime){
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime start = LocalTime.parse(startTime,timeFormatter);
        LocalTime end = LocalTime.parse(endTime,timeFormatter);
        if (end.isAfter(start)){
           return false;
        }else if(start.isAfter(end)){
            return true;
        }
        return false;
    }
    /**
     * 判断不跨天的时间是都在第一天还是都在第二天(acc前端传过来时间为14：00)
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean isOnSecondDay(String startTime,String endTime){
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime startTimeFormat = LocalTime.parse(startTime,dateTimeFormatter);
        LocalTime endTimeFormat = LocalTime.parse(endTime,dateTimeFormatter);
        if (endTimeFormat.isAfter(startTimeFormat)&&endTimeFormat.getHour()<=4){
            return true;
        }else{
            return false;
        }
    }
    /**
     * 同上，返回日期格式
     * @param startTime 14：00
     * @param endTime
     * @param date 2023-10-12
     * @return 2023-10-12
     */
    public static String isOnSecondDay(String startTime,String endTime,String date) {
        DateTimeFormatter TimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalTime startTimeFormat = LocalTime.parse(startTime,TimeFormatter);
        LocalTime endTimeFormat = LocalTime.parse(endTime,TimeFormatter);
        LocalDate dateFormat = LocalDate.parse(date,dateFormatter);
        if (endTimeFormat.isAfter(startTimeFormat)&&endTimeFormat.getHour()<=4){
            return Convert.toStr(dateFormat.plusDays(1));
        }else{
            return date;
        }
    }
    /**
     * 同上，返回日期格式
     * @param startTime 14：00
     * @param endTime
     * @param date 2023-10-12
     * @return 2023-10-12
     */
    public static String isOnSecondDayByPfp(String startTime,String endTime,String date) {
        DateTimeFormatter TimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalTime startTimeFormat = LocalTime.parse(startTime,TimeFormatter);
        LocalTime endTimeFormat = LocalTime.parse(endTime,TimeFormatter);
        LocalDate dateFormat = LocalDate.parse(date,dateFormatter);
        if (endTimeFormat.isAfter(startTimeFormat)&&endTimeFormat.getHour()<=5){
            return Convert.toStr(dateFormat.plusDays(1));
        }else{
            return date;
        }
    }
    public static boolean isCrossDay(LocalDateTime startTime,LocalDateTime endTime){
        if (startTime.getDayOfYear()==endTime.getDayOfYear()){
            return false;
        }else{
            return true;
        }
    }
    /**
     * LocalDateTime提取时间然后转成String
     * @param time
     * @return
     */
    public static String localDateTimeToTimeByString(LocalDateTime time){
        String returnTime = String.format("%02d", time.getHour())+":"+String.format("%02d", time.getMinute())+":"+"00";
        return returnTime;
    }
    /**
     * 根据开始时间结束时间和时间颗粒度生成时间列表
     * @param startTime
     * @param endTime
     * @param interval
     * @return
     */
    public static List<String> createTimeListByInterval(String startTime,String endTime,int interval){
        List<String> returnList = new ArrayList<>();
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        int addTime = 0;
        switch (interval){
            case 410001:
                addTime = 5;
                break;
            case 410002:
                addTime = 15;
                break;
            case 410003:
                addTime = 30;
                break;
        }

        while(!startTime.equals(endTime)){
            returnList.add(startTime);
            LocalTime startTimeFormat = LocalTime.parse(startTime,timeFormatter);
            startTime = Convert.toStr(startTimeFormat.plusMinutes(addTime));
        }
        returnList.add(endTime);
        return returnList;
    }
    /**
     * 根据开始时间结束时间和时间颗粒度生成时间段列表
     * @param startTime
     * @param endTime
     * @param interval
     * @return
     */
    public static List<String> createTimeSpanListByInterval(String startTime,String endTime,int interval){
        List<String> returnList = new ArrayList<>();
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        int addTime = 0;
        switch (interval){
            case 410001:
                addTime = 5;
                break;
            case 410002:
                addTime = 15;
                break;
            case 410003:
                addTime = 30;
                break;
            case 410004:
                addTime = 60;
                break;
        }
        LocalTime startTimeFormat = LocalTime.parse(startTime,timeFormatter);
        LocalTime endTimeFormat = LocalTime.parse(endTime,timeFormatter);
        while(!startTimeFormat.isAfter(endTimeFormat)&&!startTimeFormat.equals(endTimeFormat)){
            returnList.add(Convert.toStr(startTimeFormat));
            startTimeFormat = startTimeFormat.plusMinutes(addTime);
        }
        List<String> returnTimeList = new ArrayList<>();
        for (int i = 1;i<returnList.size();i++){
            String time = returnList.get(i-1)+"-"+returnList.get(i);
            returnTimeList.add(time);
        }
        return returnTimeList;
    }

    /**
     * 时间转为Acc格式
     * @param time
     * @return
     */
    public static String timeTurnAcc(String time){
        String returnTime;
        String[] timeArray = time.split(":");
        if (timeArray.length==2||timeArray.length == 3){
            returnTime = timeArray[0]+timeArray[1];
        }else{
            returnTime = time;
        }
        return returnTime;
    }
    /**
     * 时间转为Pfp格式
     * @param time
     * @return
     */
    public static String timeTurnPfp(String time){
        String returnTime;
        String[] timeArray = time.split(":");
        if(timeArray.length==2){
            returnTime = timeArray[0]+":"+timeArray[1]+":00";
        }else {
            returnTime = time;
        }
        return returnTime;
    }
    /**
     * 日期转为Acc格式
     * @param time
     * @return
     */
    public static String dateTurnAcc(String time){
        String date;
        String[] dateArray = time.split("-");
        date = dateArray[0]+dateArray[1]+dateArray[2];
        return date;
    }

    /**
     * 查询所有的断面数据（开始站点id-结束站点id查询）
     * @return
     */
    public static Map<String,Object> allSectionByStartIdAndEndId(){
        Map<String,Object> allSectionInfo = dataProcess.queryAllSection().stream().collect(Collectors.toMap(
                e->Convert.toStr(e.get("start_sta_id"))+Convert.toStr(e.get("end_sta_id")),
                e -> e,
                (v1,v2) -> v1,
                LinkedHashMap::new
        ));
        return allSectionInfo;
    }
    /**
     * 返回文件保存时间格式
     */
    public static String fileDataTimeFormat(){
        String nowDate = Convert.toStr(LocalDate.now());
        String nowTime = Convert.toStr(LocalTime.now());
        String returnDateTime = nowDate.replace("-","")+nowTime.substring(0,8).replace(":","");
        return returnDateTime;
    }
}
