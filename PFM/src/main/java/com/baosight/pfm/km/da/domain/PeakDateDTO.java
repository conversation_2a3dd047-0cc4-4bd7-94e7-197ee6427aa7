package com.baosight.pfm.km.da.domain;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Pattern;

/**
 * 峰值日期
 *
 * <AUTHOR>
 * @date 2023/12/04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class PeakDateDTO {

    @NotBlank
    @Pattern(regexp = "^(line|station|section)$", message = "参数值不对,必须为 line|station|section")
    private String command;

    private String lineNumber;

    private String stationNumber;

    private String targetType;
}
