package com.baosight.pfm.km.dc.common;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.pfm.km.ds.service.ServiceKMDS01;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Method;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PfpDataSource implements DataSource {
    String functionName;
    @Override
    public EiInfo getData(EiInfo queryInfo) {
        EiInfo returnEi = new EiInfo();
        try{
            ServiceKMDS01 serviceKMDS01 = new ServiceKMDS01();
            Method method = serviceKMDS01.getClass().getDeclaredMethod(functionName,EiInfo.class);
            returnEi = (EiInfo) method.invoke(serviceKMDS01,queryInfo);
        } catch (Exception e) {
System.out.println(e);
        }
        return returnEi;
    }
}
