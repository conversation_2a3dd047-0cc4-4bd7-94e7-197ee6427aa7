package com.baosight.pfm.km.dc.common;

import cn.hutool.core.convert.Convert;
import com.baosight.pfm.km.common.dataUtils.timeProcess;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Rule {
    private String indicator;
    private int m;
    private int n;
    private String startTimeOffset;
    private String endTimeOffset;

    public Rule(String indicator,int m,int n){
        this.indicator = indicator;
        this.m = m;
        this.n = n;
        ruleMapping();
    }
    private void ruleMapping(){
        LocalTime currentTime = LocalTime.now();
        String startTime = Convert.toStr(currentTime.minusMinutes(m));
        String endTime = Convert.toStr(currentTime.plusMinutes(n));
        this.startTimeOffset = timeProcess.timeTurnPfp(startTime);
        this.endTimeOffset = timeProcess.timeTurnPfp(endTime);
    }
}
